<template>
  <NModal
    v-model:show="showModal"
    :mask-closable="false"
    preset="dialog"
    :title="modalTitle"
    class="!w-600px !max-w-[90vw]"
    @after-leave="handleAfterLeave"
  >
    <template #header>
      <div class="flex items-center justify-between py-2">
        <div class="flex items-center space-x-3">
          <div class="w-1 h-6 bg-gradient-to-b from-purple-500 to-purple-600 rounded-full"></div>
          <div>
            <h3 class="h-6 text-lg font-semibold text-gray-800 dark:text-gray-100">
              {{ modalTitle }}
            </h3>
          </div>
        </div>
        <div class="flex items-center space-x-2 ml-5">
          <NTag :type="statusTagType" :bordered="false" size="medium">
            {{ statusText }}
          </NTag>
        </div>
      </div>
    </template>
    <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 mt-1">
      <!-- 申请基本信息 -->
      <div class="space-y-4 mb-6">
        <div class="flex items-center space-x-2 mb-4">
          <CAIcon icon="tabler:info-circle" class="text-purple-600 text-lg flex-shrink-0" />
          <h4 class="text-base font-medium text-gray-800 dark:text-gray-200 my-0">
            申请信息
          </h4>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="flex items-center gap-4">
            <label class="text-sm font-medium text-gray-600 dark:text-gray-400 w-16">申请人</label>
            <div class="flex items-center space-x-2 p-3 bg-white dark:bg-gray-700 rounded-lg border flex-1">
              <CAIcon icon="tabler:user" class="text-gray-400" />
              <span class="text-gray-800 dark:text-gray-200">{{ data?.applicant || '--' }}</span>
            </div>
          </div>

          <div class="flex items-center gap-4">
            <label class="text-sm font-medium text-gray-600 dark:text-gray-400 w-16">租户名称</label>
            <div class="flex items-center space-x-2 p-3 bg-white dark:bg-gray-700 rounded-lg border flex-1">
              <CAIcon icon="tabler:building" class="text-gray-400" />
              <span class="text-gray-800 dark:text-gray-200">{{ data?.tenant_name || '--' }}</span>
            </div>
          </div>

          <div class="flex items-center gap-4">
            <label class="text-sm font-medium text-gray-600 dark:text-gray-400 w-16">申请类型</label>
            <div class="flex items-center space-x-2 p-3 bg-white dark:bg-gray-700 rounded-lg border flex-1">
              <CAIcon :icon="applicationTypeIcon" :class="applicationTypeColor" />
              <span class="text-gray-800 dark:text-gray-200">{{ applicationTypeText }}</span>
            </div>
          </div>

          <div class="flex items-center gap-4">
            <label class="text-sm font-medium text-gray-600 dark:text-gray-400 w-16">申请时间</label>
            <div class="flex items-center space-x-2 p-3 bg-white dark:bg-gray-700 rounded-lg border flex-1">
              <CAIcon icon="tabler:calendar-plus" class="text-gray-400" />
              <span class="text-gray-800 dark:text-gray-200">{{ data?.apply_time ? formatToDate(data.apply_time) : '--' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 申请描述 -->
      <div v-if="data?.description" class="space-y-2 mb-2">
        <div class="flex items-center space-x-2 mb-1">
          <h4 class="text-base font-medium text-gray-800 dark:text-gray-200">
            申请描述
          </h4>
        </div>

        <div class="flex items-center space-x-2 p-4 bg-white dark:bg-gray-700 rounded-lg border">
          <CAIcon icon="tabler:file-text" class="text-blue-600 text-lg" />
          <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
            {{ data.description }}
          </p>
        </div>
      </div>

      <!-- 审核信息 -->
      <div v-if="data?.approve_time || data?.approver || data?.reason" class="space-y-2 pt-2 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-2 mb-4">
          <CAIcon icon="tabler:clipboard-check" class="text-green-600 text-lg flex-shrink-0" />
          <h4 class="text-base font-medium text-gray-800 dark:text-gray-200 my-0">
            审核信息
          </h4>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div v-if="data?.approver" class="flex items-center gap-4">
            <label class="text-sm font-medium text-gray-600 dark:text-gray-400 w-16">审核人</label>
            <div class="flex items-center space-x-2 p-3 bg-white dark:bg-gray-700 rounded-lg border flex-1">
              <CAIcon icon="tabler:user-check" class="text-gray-400" />
              <span class="text-gray-800 dark:text-gray-200">{{ data.approver }}</span>
            </div>
          </div>

          <div v-if="data?.approve_time" class="flex items-center gap-4">
            <label class="text-sm font-medium text-gray-600 dark:text-gray-400 w-16">审核时间</label>
            <div class="flex items-center space-x-2 p-3 bg-white dark:bg-gray-700 rounded-lg border flex-1">
              <CAIcon icon="tabler:calendar-event" class="text-gray-400" />
              <span class="text-gray-800 dark:text-gray-200">{{ formatToDate(data.approve_time) }}</span>
            </div>
          </div>
        </div>

        <!-- 审核意见输入区域 -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-600 dark:text-gray-400">审核意见</label>
          <div class="p-4 bg-white dark:bg-gray-700 rounded-lg border">
            <NInput
              v-model:value="reviewReason"
              type="textarea"
              :placeholder="getReasonPlaceholder"
              :autosize="{ minRows: 3, maxRows: 6 }"
              :disabled="!canInputReason"
              class="w-full"
              :status="reasonInputStatus"
            />
            <div v-if="showReasonError" class="text-red-500 text-xs mt-2">
              拒绝申请时必须填写审核意见
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #action>
      <div class="flex justify-center items-center pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex space-x-3">
          <!-- 根据申请类型和状态显示不同的操作按钮 -->
          <NButton
            v-if="canApprove"
            type="success"
            size="medium"
            :loading="approveLoading"
            class="px-6 py-2 transition-all duration-200 hover:shadow-md"
            @click="handleApprove"
          >
            <template #icon>
              <CAIcon icon="tabler:check" />
            </template>
            通过申请
          </NButton>

          <NButton
            v-if="canReject"
            type="error"
            size="medium"
            :loading="rejectLoading"
            class="px-6 py-2 transition-all duration-200 hover:shadow-md"
            @click="handleReject"
          >
            <template #icon>
              <CAIcon icon="tabler:x" />
            </template>
            拒绝申请
          </NButton>

          <NButton
            v-if="canCancel"
            type="warning"
            size="medium"
            :loading="cancelLoading"
            class="px-6 py-2 transition-all duration-200 hover:shadow-md"
            @click="handleCancel"
          >
            <template #icon>
              <CAIcon icon="tabler:ban" />
            </template>
            取消申请
          </NButton>
        </div>
      </div>
    </template>
  </NModal>
</template>

<script setup lang="ts">
  import type { ApprovalInfoExpanded } from "-/tenant";
  import { CAIcon } from "@celeris/components";
  import { formatToDate } from "@celeris/utils";
  import { NButton, NInput, NModal, NTag, useDialog, useMessage } from "naive-ui";

  interface Props {
    show: boolean;
    data?: ApprovalInfoExpanded | null;
  }

  interface Emits {
    (e: "update:show", value: boolean): void;
    (e: "approve", data: ApprovalInfoExpanded): void;
    (e: "reject", data: ApprovalInfoExpanded): void;
    (e: "cancel", data: ApprovalInfoExpanded): void;
    (e: "refresh"): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    show: false,
    data: null,
  });

  const emit = defineEmits<Emits>();

  // ========== 响应式数据 ==========
  const message = useMessage();
  const dialog = useDialog();

  const showModal = computed({
    get: () => props.show,
    set: value => emit("update:show", value),
  });

  const approveLoading = ref(false);
  const rejectLoading = ref(false);
  const cancelLoading = ref(false);

  // 审核意见相关
  const reviewReason = ref("");
  const reasonValidationError = ref(false);

  // ========== 计算属性 ==========
  const applicationTypeText = computed(() => {
    if (!props.data) {
      return "";
    }
    const typeMap = {
      create: "创建租户",
      update: "更新租户",
      delete: "删除租户",
      reset: "重置密码",
    } as const;
    return typeMap[props.data.application_type as keyof typeof typeMap] || props.data.application_type;
  });

  const modalTitle = computed(() => {
    if (!props.data) {
      return "申请详情";
    }
    return `${applicationTypeText.value} - 申请详情`;
  });

  const modalSubtitle = computed(() => {
    if (!props.data) {
      return "";
    }
    return `申请人：${props.data.applicant} | 租户：${props.data.tenant_name}`;
  });

  const applicationTypeIcon = computed(() => {
    if (!props.data) {
      return "tabler:help";
    }
    const iconMap = {
      create: "tabler:plus",
      update: "tabler:edit",
      delete: "tabler:trash",
      reset: "tabler:key",
    } as const;
    return iconMap[props.data.application_type as keyof typeof iconMap] || "tabler:help";
  });

  const applicationTypeColor = computed(() => {
    if (!props.data) {
      return "text-gray-400";
    }
    const colorMap = {
      create: "text-green-600",
      update: "text-blue-600",
      delete: "text-red-600",
      reset: "text-orange-600",
    } as const;
    return colorMap[props.data.application_type as keyof typeof colorMap] || "text-gray-400";
  });

  const statusText = computed(() => {
    if (!props.data) {
      return "";
    }
    const statusMap = {
      pending: "待审核",
      approved: "已通过",
      rejected: "已拒绝",
      cancelled: "已取消",
    } as const;
    return statusMap[props.data.status as keyof typeof statusMap] || props.data.status;
  });

  const statusTagType = computed(() => {
    if (!props.data) {
      return "default";
    }
    const typeMap = {
      pending: "warning",
      approved: "success",
      rejected: "error",
      cancelled: "info",
    } as const;
    return typeMap[props.data.status as keyof typeof typeMap] || "default";
  });

  // 权限控制
  const canApprove = computed(() => props.data?.status === "pending");
  const canReject = computed(() => props.data?.status === "pending");
  const canCancel = computed(() => props.data?.status === "pending");

  // 审核意见相关计算属性
  const canInputReason = computed(() => props.data?.status === "pending");

  const getReasonPlaceholder = computed(() => {
    if (!props.data || props.data.status !== "pending") {
      return "该申请已处理，无法修改审核意见";
    }
    return "请输入审核意见（拒绝申请时必填）";
  });

  const reasonInputStatus = computed(() => {
    return reasonValidationError.value ? "error" : undefined;
  });

  const showReasonError = computed(() => {
    return reasonValidationError.value;
  });

  // ========== 方法 ==========
  function handleClose() {
    showModal.value = false;
  }

  function handleAfterLeave() {
    approveLoading.value = false;
    rejectLoading.value = false;
    cancelLoading.value = false;
    // 清理审核意见相关数据
    reviewReason.value = "";
    reasonValidationError.value = false;
  }

  // ========== 监听器 ==========
  // 监听弹窗显示状态，初始化审核意见
  watch(
    () => props.show,
    (newShow) => {
      if (newShow && props.data) {
        // 如果申请已有审核意见，则显示现有的意见
        reviewReason.value = props.data.reason || "";
        reasonValidationError.value = false;
      }
    },
    { immediate: true },
  );

  // 监听审核意见输入，清除校验错误
  watch(
    () => reviewReason.value,
    () => {
      if (reasonValidationError.value && reviewReason.value.trim()) {
        reasonValidationError.value = false;
      }
    },
  );

  async function handleApprove() {
    if (!props.data) {
      return;
    }

    dialog.info({
      title: "确认审核",
      content: `确认通过 "${props.data.applicant}" 的 "${props.data.tenant_name}" ${applicationTypeText.value}申请吗？`,
      positiveText: "确认通过",
      negativeText: "取消",
      async onPositiveClick() {
        try {
          approveLoading.value = true;
          // 将审核意见添加到数据中（可选）
          const approveData = {
            ...props.data!,
            reason: reviewReason.value.trim() || undefined,
          };
          emit("approve", approveData);
          // 等待父组件处理完成
          await nextTick();
          message.success("审核通过成功");
          emit("refresh");
          showModal.value = false;
        } catch (error) {
          console.error("审核通过失败:", error);
          message.error("审核通过失败，请重试");
        } finally {
          approveLoading.value = false;
        }
      },
    });
  }

  async function handleReject() {
    if (!props.data) {
      return;
    }

    // 拒绝申请时必须填写审核意见
    if (!reviewReason.value.trim()) {
      reasonValidationError.value = true;
      message.error("拒绝申请时必须填写审核意见");
      return;
    }

    // 清除校验错误
    reasonValidationError.value = false;

    dialog.info({
      title: "确认审核",
      content: `确认拒绝 "${props.data.applicant}" 的 "${props.data.tenant_name}" ${applicationTypeText.value}申请吗？`,
      positiveText: "确认拒绝",
      negativeText: "取消",
      async onPositiveClick() {
        try {
          rejectLoading.value = true;
          // 将审核意见添加到数据中
          const rejectData = {
            ...props.data!,
            reason: reviewReason.value.trim(),
          };
          emit("reject", rejectData);
          // 等待父组件处理完成
          await nextTick();
          message.success("审核拒绝成功");
          emit("refresh");
          showModal.value = false;
        } catch (error) {
          console.error("审核拒绝失败:", error);
          message.error("审核拒绝失败，请重试");
        } finally {
          rejectLoading.value = false;
        }
      },
    });
  }

  async function handleCancel() {
    if (!props.data) {
      return;
    }

    dialog.warning({
      title: "确认取消",
      content: `确认取消 "${props.data.applicant}" 的 "${props.data.tenant_name}" ${applicationTypeText.value}申请吗？`,
      positiveText: "确认取消",
      negativeText: "保留",
      async onPositiveClick() {
        try {
          cancelLoading.value = true;
          emit("cancel", props.data!);
          // 等待父组件处理完成
          await nextTick();
          message.success("取消申请成功");
          emit("refresh");
          showModal.value = false;
        } catch (error) {
          console.error("取消申请失败:", error);
          message.error("取消申请失败，请重试");
        } finally {
          cancelLoading.value = false;
        }
      },
    });
  }
</script>

<style scoped>
/* 模态框样式 */
:deep(.n-modal) {
  backdrop-filter: blur(4px);
}

:deep(.n-dialog) {
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 标签样式 */
:deep(.n-tag) {
  border-radius: 8px;
  font-weight: 500;
  padding: 4px 12px;
}

/* 按钮样式 */
:deep(.n-button) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

:deep(.n-button:hover) {
  transform: translateY(-1px);
}

/* 信息卡片样式 */
.info-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease-in-out;
}

.info-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

:deep(.dark .info-card) {
  background: #374151;
  border-color: #4b5563;
}

:deep(.dark .info-card:hover) {
  border-color: #60a5fa;
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.1);
}

/* 图标样式 */
.icon-prefix {
  color: #6b7280;
  transition: color 0.2s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.n-dialog) {
    margin: 16px;
    width: calc(100vw - 32px) !important;
    max-width: none !important;
  }

  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 加载状态样式 */
:deep(.n-button--loading) {
  pointer-events: none;
}

:deep(.n-button--loading .n-button__icon) {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
