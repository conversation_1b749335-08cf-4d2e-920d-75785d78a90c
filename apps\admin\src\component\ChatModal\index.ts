import type { VNode } from "vue";
import { createVNode, render } from "vue";
import ChatModal from "./src/ChatModal.vue";

let vNode: VNode;

/*
 * 初始化弹窗
 * Initialize the modal
 */
function init() {
  const container = document.createElement("div");

  const vNode = createVNode(ChatModal, {});
  render(vNode, container);

  document.body.appendChild(container);

  return vNode;
}

/*
 * 显示弹窗
 * Show the modal
 */
function show(options: AnyObject | null | undefined) {
  if (!vNode) {
    vNode = init();
  }

  vNode?.component?.exposed?.show?.(options);
}

/*
 * 关闭弹窗
 * Hide the modal
 */
function hide() {
  vNode?.component?.exposed?.hide?.();
}

/*
 * 弹窗是否可见
 * Is the modal visible
 */
function isVisible() {
  return vNode?.component?.exposed?.isVisible?.();
}

/*
 * 切换聊天目标
 * Change the chat target
 */
function changeTarget(params) {
  vNode?.component?.exposed?.changeTarget?.(params);
}

export default {
  show,
  hide,
  isVisible,
  changeTarget,
};
