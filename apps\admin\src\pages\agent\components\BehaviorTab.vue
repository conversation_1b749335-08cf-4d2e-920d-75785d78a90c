<template>
  <div>
    <NFlex
      class="overflow-hidden flex-1"
      size="large"
      vertical
      style="height: calc(100vh - 230px)"
    >
      <!-- 工具栏 -->
      <NPageHeader>
        <NFlex class="pr" justify="end">
          <NFlex>
            <NForm ref="searchFormRef" :model="searchModel" :show-feedback="false" inline label-placement="left" @submit.prevent>
              <NFormItem path="uid" label="用户">
                <UserSelect
                  v-model:value="searchModel.uid"
                  placeholder="请输入用户账号或姓名"
                  remote
                  clearable
                  @update:value="searchConfirm"
                />
              </NFormItem>
              <NFormItem path="date" label="时间">
                <NDatePicker
                  v-model:value="queryTime"
                  type="daterange"
                  class="w-70"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @update:value="searchConfirm"
                />
              </NFormItem>
              <NFormItem>
                <NSpace>
                  <NButton type="primary" @click="searchConfirm">
                    查询
                  </NButton>
                  <NButton @click="searchReset">
                    重置
                  </NButton>
                </NSpace>
              </NFormItem>
            </NForm>
          </NFlex>
        </NFlex>
      </NPageHeader>
      <!-- 表格 -->
      <NDataTable
        class="fixed-right-table h-full [&_.ca-data-table-resize-button::after]:(!bg-[var(--primary-color)])"
        scroll-x="min-content"
        remote
        flex-height
        :bordered="false"
        :single-line="false"
        :columns="columns"
        :data="tableData"
        :loading="tableLoading"
        :pagination="tablePagination"
        @scroll="handleScroll"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      >
      </NDataTable>
    </NFlex>
    <!-- 设置权限 -->
    <BehaviorDetailDrawer v-model:show="detailVisible" :data="currBehavior" :search-param="detailParam"></BehaviorDetailDrawer>
  </div>
</template>

<script setup lang="tsx">
  import type { Behavior, BehaviorExpanded } from "-/behavior";
  import type { DataTableColumns, FormInst } from "naive-ui";
  import { dateUtil, formatToDate } from "@celeris/utils";
  import { cloneDeep } from "lodash-es";
  import { NButton, NFlex } from "naive-ui";
  import { getUserBehaviors } from "~/apis/internal/behavior";
  import { UserSelect } from "~/component/UserSelect";
  import BehaviorDetailDrawer from "./BehaviorDetailDrawer.vue";

  interface QueryCriteria {
    uid: string | null;
    from: string | null;
    to: string | null;
  }

  const INIT_QUERY_MODEL = {
    uid: null,
  };

  const queryCriteria = ref<QueryCriteria>({
    uid: null,
    from: null,
    to: null,
  });

  const {
    tableLoading,
    tableData,
    tablePagination,
    loadData,
    refreshData,
    reloadData,
    handlePageChange,
    handlePageSizeChange,
    handleScroll,
  } = useTable<QueryCriteria, Behavior, BehaviorExpanded>();

  // 查询参数
  const searchModel = ref({ ...INIT_QUERY_MODEL });
  const queryTime = ref<[number, number]>([
    dateUtil().startOf("day").valueOf(),
    dateUtil().startOf("day").valueOf(),
  ]);

  const searchFormRef = useTemplateRef<FormInst>("searchFormRef");

  const currBehavior = ref<BehaviorExpanded | null>(null);

  // 弹窗相关
  const detailVisible = ref(false);
  const detailParam = ref<any | null>(null);

  // ========== 表格列定义 ==========
  const columns: DataTableColumns<BehaviorExpanded> = [
    {
      title: "账号",
      key: "uid",
    },
    {
      title: "姓名",
      key: "userName",
    },
    {
      title: "git行为",
      key: "gitBehavior",
      render(row) {
        return (
          <NFlex>
            <NButton
              text
              type="info"
              onClick={() => showBehaviorDetail?.(row, "gitlab")}
            >
              { row.gitBehavior }
            </NButton>
          </NFlex>
        );
      },
    },
    {
      title: "wone行为",
      key: "woneBehavior",
      render(row) {
        return (
          <NFlex>
            <NButton
              text
              type="info"
              onClick={() => showBehaviorDetail?.(row, "wone")}
            >
              { row.woneBehavior }
            </NButton>
          </NFlex>
        );
      },
    },
    {
      title: "jone行为",
      key: "joneBehavior",
      render(row) {
        return (
          <NFlex>
            <NButton
              text
              type="info"
              onClick={() => showBehaviorDetail?.(row, "jone")}
            >
              { row.joneBehavior }
            </NButton>
          </NFlex>
        );
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      render(row) {
        return (
          <NFlex>
            <NButton
              text
              type="info"
              onClick={() => handleAIAnaly?.(cloneDeep(row))}
            >
              大模型分析
            </NButton>
          </NFlex>
        );
      },
    },
  ];

  // 初始化
  onMounted(() => {
    loadTableData();
  });

  // 监听 searchModel、queryTime 变化，同步到 queryCriteria
  watch([searchModel, queryTime], ([searchVal, timeVal]) => {
    Object.keys(searchVal).forEach((key) => {
      queryCriteria.value[key] = searchVal?.[key];
    });
    if (timeVal) {
      queryCriteria.value.from = formatToDate(timeVal[0]);
      queryCriteria.value.to = formatToDate(timeVal[1]);
    }
  }, { immediate: true, deep: true });

  /**
   * @description 获取行为分析列表
   */
  function loadTableData() {
    loadData({
      getQueryCriteria: () => queryCriteria.value,
      dataPath: "data.behavior",
      totalPath: "data.total",
      tableRequest: getUserBehaviors as unknown as (queryCriteria?: QueryCriteria) => Promise<Behavior[]>,
      handleTableData: (dataSource: BehaviorExpanded[]) => {
        return dataSource.map((item, index) => {
          return {
            ...item,
            key: item.uid ? String(item.uid) : index.toString(),
            deleteLoading: false,
          } as BehaviorExpanded;
        });
      },
    });
  }

  /**
   * @description 筛选条件查询，重置页码为1并加载数据
   */
  function searchConfirm() {
    nextTick(() => {
      reloadData();
    });
  }

  /**
   * @description 查询条件重置，清空筛选项并重置页码，重新加载数据
   */
  function searchReset() {
    searchModel.value.uid = null;
    reloadData();
  }

  /**
   * @description 显示行为详情抽屉
   * @param row - 当前行为数据
   * @param type - 行为类型（gitlab/wone/jone）
   */
  function showBehaviorDetail(row, type) {
    detailParam.value = {
      src: type,
      from: queryTime.value ? formatToDate(queryTime.value?.[0]) : null,
      to: queryTime.value ? formatToDate(queryTime.value?.[1]) : null,
    };
    currBehavior.value = row;
    detailVisible.value = true;
  }

  /**
   * @description 调用大模型分析接口，显示分析详情
   * @param row - 当前行为数据
   */
  function handleAIAnaly(row) {
    detailParam.value = {
      src: row.gitBehavior ? "gitlab" : row.woneBehavior ? "wone" : "jone",
      from: queryTime.value ? formatToDate(queryTime.value?.[0]) : null,
      to: queryTime.value ? formatToDate(queryTime.value?.[1]) : null,
    };
    currBehavior.value = row;
    detailVisible.value = true;
  }
</script>

<style scoped></style>
