import type {
  AddCommentParams,
  CodeReviewDetail,
  CodeReviewDetailParams,
  CodeReviewItem,
  CodeReviewListParams,
  DeleteCommentParams,
  GitMessageParams,
} from "-/codeReview";
import type { ResponseData } from "-/http";
import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";

enum API {
  CodeReviewList = "/v1/merge_requests/cr/list",
  CodeReviewDetail = "/v1/merge_requests/cr/detail",
  GitMessage = "/v1/merge_requests/cr/get_project_by_merge",
  Comment = "/v1/merge_requests/cr/comment",
}

/**
 * 获取代码审查列表
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<{items: CodeReviewItem[], total: number, total_pages: number}>
 */
export function getCodeReviewList(
  params: CodeReviewListParams,
  errorMessageMode: MessageMode = "message",
): Promise<{ items: CodeReviewItem[]; total: number; total_pages: number }> {
  return request.get({ url: API.CodeReviewList, params }, { errorMessageMode });
}

/**
 * 获取代码审查详情
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<ResponseData<CodeReviewDetail>>
 */
export function getCodeReviewDetail(
  params: CodeReviewDetailParams,
  errorMessageMode: MessageMode = "message",
): Promise<CodeReviewDetail> {
  return request.get({ url: API.CodeReviewDetail, params }, { errorMessageMode });
}

/**
 * 根据Git链接获取信息
 * @param params - Git链接参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<ResponseData<CodeReviewItem>>
 */
export function getGitMessage(
  params: GitMessageParams,
  errorMessageMode: MessageMode = "message",
): Promise<ResponseData<CodeReviewItem>> {
  return request.get({ url: API.GitMessage, params }, { errorMessageMode });
}

/**
 * 添加评论
 * @param params - 评论参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<ResponseData<any>>
 */
export function addComment(
  params: AddCommentParams,
  errorMessageMode: MessageMode = "message",
): Promise<ResponseData<any>> {
  return request.post({ url: API.Comment, data: params }, { errorMessageMode });
}

/**
 * 删除评论
 * @param params - 删除参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<ResponseData<any>>
 */
export function deleteComment(
  params: DeleteCommentParams,
  errorMessageMode: MessageMode = "message",
): Promise<ResponseData<any>> {
  return request.delete({ url: API.Comment, params }, { errorMessageMode });
}
