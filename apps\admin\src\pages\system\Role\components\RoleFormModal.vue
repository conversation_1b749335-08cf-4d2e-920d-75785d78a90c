<template>
  <CardModal
    v-bind="$attrs"
    class="w-xl"
    :title="modalTitle"
    :empty-func="() => false"
    @update:show="handleHide"
    @after-enter="handleAfterEnter"
    @after-leave="handleAfterLeave"
  >
    <div class="p-4">
      <NForm ref="formRef" :model="formModel" :rules="formRules" label-width="60" label-placement="left" require-mark-placement="left">
        <NFormItem label="角色" path="name">
          <NInput v-model:value="formModel.name" placeholder="请输入角色名称" />
        </NFormItem>
        <NFormItem label="描述" path="description">
          <NInput v-model:value="formModel.description" type="textarea" placeholder="请输入描述" />
        </NFormItem>
      </NForm>
    </div>
    <template #footer>
      <NFlex justify="center">
        <NButton @click="handleHide">
          取消
        </NButton>
        <NButton type="primary" :loading="submitting" @click="handleCreate">
          确定
        </NButton>
      </NFlex>
    </template>
  </CardModal>
</template>

<script lang="ts" setup>
  import type { RoleExpanded } from "-/role";
  import type { FormInst, FormRules } from "naive-ui";
  import { createRole, updateRole } from "~/apis/internal/role";
  import CardModal from "~/component/CardModal/src/CardModal.vue";

  defineOptions({
    name: "RoleFormModal",
    inheritAttrs: false,
  });

  const props = withDefaults(defineProps<{
    data: RoleExpanded | null;
    domainId: string;
  }>(), {
    data: () => ({}) as RoleExpanded,
  });

  const emit = defineEmits<{
    "update:show": [show: boolean];
    "positiveClick": [success: boolean];
  }>();

  /**
   * @description 权限角色表单初始值
   */
  const INIT_ROLE_FORM_MODEL: RoleExpanded = {
    id: null,
    name: "",
    description: "",
  };

  const isEdit = computed(() => !!(props.data && props.data.id));

  const modalTitle = computed(() => {
    return isEdit.value ? "编辑角色" : "新增角色";
  });

  const message = useMessage();

  const submitting = ref(false);

  const formRef = useTemplateRef<FormInst>("formRef");
  const formModel = ref<RoleExpanded>({ ...INIT_ROLE_FORM_MODEL });
  /**
   * @description 角色表单校验规则
   */
  const formRules: FormRules = {
    name: [
      { required: true, message: "请输入角色名称", trigger: "blur" },
    ],
    description: [
      { required: true, message: "请输入动作", trigger: "blur" },
    ],
  };

  /**
   * @description 弹窗进入动画后初始化表单数据。
   * @returns {void}
   */
  function handleAfterEnter() {
    props.data ? initFormModel() : clearFormModel();
  }

  /**
   * @description 弹窗关闭动画后清空表单数据。
   * @returns {void}
   */
  function handleAfterLeave() {
    clearFormModel();
  }

  /**
   * @description 关闭弹窗。
   * @returns {void}
   */
  function handleHide() {
    emit("update:show", false);
  }

  /**
   * @description 根据 props.data 初始化表单模型。
   * @returns {void}
   */
  function initFormModel() {
    const data = props.data;
    Object.keys(INIT_ROLE_FORM_MODEL).forEach((key) => {
      formModel.value[key] = data?.[key] || INIT_ROLE_FORM_MODEL[key];
    });
  }

  /**
   * @description 重置表单模型为初始值，并恢复校验状态。
   * @returns {void}
   */
  function clearFormModel() {
    formModel.value = { ...INIT_ROLE_FORM_MODEL, project_name: props.domainId };
    formRef.value?.restoreValidation?.();
  }

  /**
   * @description 提交表单，创建或更新角色。
   * @returns {Promise<void>}
   */
  async function handleCreate() {
    await formRef.value?.validate();
    submitting.value = true;
    try {
      if (formModel.value.id) {
        await updateRole(props.domainId, formModel.value);
      } else {
        await createRole(props.domainId, formModel.value);
      }
      handleHide();
      emit("positiveClick", true);
    } catch (e) {
      message.error("操作失败");
    } finally {
      submitting.value = false;
    }
  }
</script>
