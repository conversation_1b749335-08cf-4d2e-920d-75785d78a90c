import type { Audit, AuditExpanded, AuditParam } from "-/audit";
import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";
import { replaceUrlPathParams } from "@celeris/utils";

enum API {
  Audits = "/v2/projects/111/audits",
}

/**
 * 获取用户审计列表
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function getUserAudits(params: AuditParam = {}, errorMessageMode: MessageMode = "message") {
  return request.get({ url: API.Audits, params }, { errorMessageMode, shouldTransformResponse: false });
}
