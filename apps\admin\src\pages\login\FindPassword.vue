<template>
  <NFlex
    vertical
    :size="0"
    class="w-full h-screen bg-top bg-no-repeat bg-cover"
    :style="{ backgroundImage: `url(${loginBg})` }"
  >
    <div class="h-[9%] header__logo">
      <!-- <img :src="loginLogo" alt="登录页 logo" class="mt-[1.2vh] ml-[1.9vw] h-full" /> -->
    </div>
    <NFlex justify="center" align="center" class="w-full h-[83%]">
      <NFlex vertical justify="center" align="center" :size="0" class="rounded-20px shadow-[0_0_40px_25px_rgba(3,69,236,0.2)] h-[78vh] w-[75vh] transition-all duration-1000 m-0 p-0">
        <NFlex vertical justify="center" align="center" :size="0">
          <span class="text-[#3d73f5] text-[2.6vh] underline underline-offset-6 decoration-[#0076fe33] decoration-6">{{ step === 1 ? '短信验证' : '设置密码' }}</span>
        </NFlex>
        <NFlex vertical justify="center" align="center" :size="0" class="w-[70%] mt-10px">
          <NForm
            ref="passwordForm"
            :show-label="false"
            :model="formModel"
            :rules="formRules"
            class="mt-[5vh] w-full passwordForm"
          >
            <NFormItem path="username">
              <NInput
                v-model:value.trim="formModel.username"
                name="username"
                placeholder="请输入用户名"
                autocomplete="on"
                :disabled="step !== 1"
              >
                <template #prefix>
                  <CAIcon icon="tabler:user" alt="用户名图标" />
                </template>
              </NInput>
            </NFormItem>
            <NFormItem v-if="step === 1" path="code">
              <NInput
                v-model:value="formModel.code"
                placeholder="请输入验证码"
                @keydown.enter="handleNext"
              >
                <template #prefix>
                  <CAIcon icon="tabler:shield-lock" alt="验证码图标" />
                </template>
              </NInput>
              <NButton
                class="ml-[1.85vh] text-[1.85vh] px-[1.8vh] py-[2.7vh]"
                :disabled="countdown > 0 || codeLoading"
                type="primary"
                @click="handleSendCaptcha"
              >
                <span v-if="codeLoading">正在获取验证码</span>
                <span v-else-if="!countdown">点击获取验证码</span>
                <span v-else>重新获取<span v-if="countdown"> {{ countdown }} 秒</span></span>
              </NButton>
            </NFormItem>
            <template v-else>
              <NFormItem path="password">
                <NInput
                  v-model:value="formModel.password"
                  type="password"
                  show-password-on="click"
                  placeholder="请输入新密码"
                >
                  <template #prefix>
                    <CAIcon icon="tabler:lock" alt="密码图标" />
                  </template>
                  <template #password-visible-icon>
                    <CAIcon icon="tabler:eye" />
                  </template>
                  <template #password-invisible-icon>
                    <CAIcon icon="tabler:eye-closed" />
                  </template>
                </NInput>
              </NFormItem>
              <NFormItem path="repeat_password">
                <NInput
                  v-model:value="formModel.repeat_password"
                  type="password"
                  show-password-on="click"
                  placeholder="请再次输入新密码"
                >
                  <template #prefix>
                    <CAIcon icon="tabler:lock" alt="密码图标" />
                  </template>
                  <template #password-visible-icon>
                    <CAIcon icon="tabler:eye" />
                  </template>
                  <template #password-invisible-icon>
                    <CAIcon icon="tabler:eye-closed" />
                  </template>
                </NInput>
              </NFormItem>
            </template>
            <NFormItem>
              <NButton v-if="step === 1" class="h-[6.8vh] w-full text-[2.2vh] shadow-[1px_10px_10px_#aec2e7]" type="primary" size="large" :loading="loading" @click="handleNext">
                下一步
              </NButton>
              <NButton v-if="step === 2" class="h-[6.8vh] w-full text-[2.2vh] shadow-[1px_10px_10px_#aec2e7]" type="primary" size="large" :loading="loading" @click="handleResetPassword">
                确 定
              </NButton>
            </NFormItem>
          </NForm>
        </NFlex>
      </NFlex>
    </NFlex>
    <NFlex vertical justify="end" align="center" :size="2" class="footer h-[8%] pb-1 text-[#96afcc]">
      <span class="text-base">天翼云科技有限公司 版权所有</span>
      <span class="text-xs">design by cute-design</span>
    </NFlex>
  </NFlex>
</template>

<script setup lang="ts">
  import type { LoginParam } from "-/login";
  import type { FormInst, FormItemRule, FormRules } from "naive-ui";
  import { loginBg, loginLogo } from "@celeris/assets";
  import { PageConstants } from "@celeris/constants";
  import { checkPasswd, getCode, getLoginMode, setPasswd } from "~/apis/internal/login";
  import { TOKEN_KEY } from "~/router/constant";
  import { encryptPassword } from "~/utils/cipher";

  // 类型定义
  interface PasswordFormModel {
    username: string;
    password: string;
    code: string;
    repeat_password: string;
  }

  const router = useRouter();
  const message = useMessage();

  const step = ref<number>(1); // 1: 短信验证 2: 设置密码
  const loading = ref<boolean>(false);
  const codeLoading = ref<boolean>(false);
  const countdown = ref<number>(0); // 验证码倒计时
  const countdownInterval = ref<number | null>(null); // 验证码倒计时定时器
  const secretKey = ref<string>("");

  const passwordForm = useTemplateRef<HTMLElement & FormInst>("passwordForm");
  const formModel = ref<PasswordFormModel>({
    username: "",
    code: "",
    password: "",
    repeat_password: "",
  });

  // 校验规则
  const formRules = ref<FormRules>({
    username: [
      { required: true, message: "请输入用户名", trigger: ["blur"] },
    ],
    code: [
      { required: true, message: "请输入验证码", trigger: ["blur"] },
    ],
    password: [
      { required: true, message: "请输入新密码", trigger: ["blur"] },
      {
        validator: validatePassword,
        trigger: ["input"],
      },
    ],
    repeat_password: [
      { required: true, message: "请输入新密码", trigger: ["blur"] },
      {
        validator: validateRepeatPassword,
        trigger: ["input"],
      },
    ],
  });

  // 组件挂载前执行
  onBeforeMount(async () => {
    const res = await getLoginMode();
    const { aes_secret } = res;
    secretKey.value = aes_secret;
  });

  /**
   * 验证密码的强度和格式
   * @param rule 校验规则
   * @param value 待验证的密码
   * @returns {boolean | Error} 如果密码符合要求则返回true，否则返回Error对象
   */
  function validatePassword(rule: FormItemRule, value: string) {
    const passwordClasses = [
      /\d/, // 包含数字
      /[a-z]/, // 包含小写字母
      /[A-Z]/, // 包含大写字母
      /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/, // 包含特殊符号
    ];

    const classCount = passwordClasses.reduce((count, regex) => {
      return count + (regex.test(value) ? 1 : 0);
    }, 0);

    if (value.length < 12) {
      return new Error("新密码长度至少为 12 位");
    } else if (classCount < 3) {
      return new Error("新密码至少包含数字，大、小写字母，特殊符号中的三类");
    }
    return true;
  }

  /**
   * 验证重复输入的密码是否与初次输入的密码一致
   * @param rule 校验规则
   * @param value 用户重复输入的密码
   * @returns 如果两次输入的密码不一致，则返回一个包含错误信息的Error对象；否则返回true
   */
  function validateRepeatPassword(rule: FormItemRule, value: string) {
    if (value !== formModel.value.password) {
      return new Error("两次输入的密码不一致");
    } else {
      return true;
    }
  }

  /**
   * 触发验证码的发送，并处理发送过程中的状态变化
   */
  function handleSendCaptcha() {
    if (!formModel.value.username) {
      message.error("请输入用户名");
      return false;
    }
    codeLoading.value = true;
    // 在验证码请求发送过程中一直到返回结果前，不能二次点击
    getCode({ username: formModel.value.username })
      .then(() => {
        message.success("验证码发送成功");
        countdown.value = 60;
        if (countdownInterval.value) {
          clearInterval(countdownInterval.value);
        }
        countdownInterval.value = window.setInterval(() => {
          countdown.value--;
          // 倒计时结束
          if (countdown.value === 0) {
            clearInterval(countdownInterval.value!);
            countdownInterval.value = null;
          }
        }, 1000);
      })
      .catch((e) => {
        message.error(e.message || "验证码发送失败");
      })
      .finally(() => {
        codeLoading.value = false;
      });
  }

  /**
   * 处理下一步按钮点击事件
   * 此函数首先尝试验证密码表单，如果验证成功，则进行密码验证逻辑
   */
  function handleNext() {
    passwordForm.value?.validate().then(async () => {
      loading.value = true;
      try {
        const params: LoginParam = {
          username: formModel.value.username,
          code: formModel.value.code,
        };
        const res = await checkPasswd(params);
        formModel.value.username = res.user.user_name;
        step.value = 2;
      } catch (e: any) {
        message.error(e.message || "获取密码失败");
      } finally {
        loading.value = false;
      }
    });
  }

  /**
   * 重置密码
   * 加密用户密码，并调用后端接口进行密码重置如果重置成功，清除当前用户信息，
   * 并重定向到登录页面；如果失败，则显示错误消息
   */
  function handleResetPassword() {
    passwordForm.value?.validate().then(async () => {
      loading.value = true;
      try {
        const key = secretKey.value;
        const iv = key.slice(0, 16);
        const params: LoginParam = {
          username: formModel.value.username,
          password: encryptPassword(key, iv, formModel.value.password),
        };
        await setPasswd(params);
        message.success("密码重置成功");
        const cookies = useCookies([], { autoUpdateDependencies: true });
        cookies.remove(TOKEN_KEY);
        router.replace(PageConstants.BASE_LOGIN);
      } catch (e: any) {
        message.error(e.message || "密码重置失败");
      } finally {
        loading.value = false;
      }
    });
  }
</script>

<style scoped>
/deep/ .passwordForm .ca-form-item--medium-size .ca-input {
  padding: calc(2.8vh - 16px) 0vh;
  font-size: 1.85vh;
}
/deep/ .passwordForm .ca-form-item--medium-size .ca-form-item-feedback {
  font-size: 1.85vh;
}
/deep/ .passwordForm .ca-form-item--medium-size:not(:first-child) {
  margin-top: calc(5vh - 24px);
}
@media screen and (max-height: 450px) {
  .header__logo,
  .footer {
    display: none !important;
  }
}
</style>
