import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";
import { replaceUrlPathParams } from "@celeris/utils";

enum API {
  TokenApi = "/v2/authn/token",
  DeleteTokenApi = "/v2/authn/token/{id}",
}

/**
 * 获取用户令牌
 * @param data
 * @returns
 */
export function getAccessTokens(params: AnyObject = {}, errorMessageMode: MessageMode = "message") {
  return request.get({ url: API.TokenApi, params }, { errorMessageMode });
}

/**
 * 新增令牌
 * @param data
 * @returns
 */
export function createToken(data: AnyObject = {}, errorMessageMode: MessageMode = "message") {
  return request.post({ url: API.TokenApi, data }, { errorMessageMode });
}

/**
 * 修改令牌
 * @param data
 * @returns
 */
export function updateToken(data: AnyObject = {}, errorMessageMode: MessageMode = "message") {
  return request.put({ url: API.TokenApi, data }, { errorMessageMode });
}

/**
 * 删除令牌
 * @param data
 * @returns
 */
export function deleteToken(id: number, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.DeleteTokenApi, { id });
  return request.delete({ url }, { errorMessageMode });
}

export { API };
