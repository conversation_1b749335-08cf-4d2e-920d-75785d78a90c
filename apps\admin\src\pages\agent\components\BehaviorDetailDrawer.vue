<template>
  <NDrawer
    :show="show"
    width="60%"
    placement="right"
    @update:show="handleUpdateShow"
    @after-leave="handleAfterLeave"
  >
    <NDrawerContent closable>
      <template #header>
        <NFlex justify="space-between">
          <span>{{ userInfo.name || drawerData.userName }} 的行为详情</span>
          <NButton type="primary" size="small" style="margin-left: 20px;" @click="handleAIAnaly">
            大模型分析
          </NButton>
        </NFlex>
      </template>
      <NFlex vertical class="h-full overflow-hidden">
        <!-- 人员信息 -->
        <div>
          <NDescriptions label-placement="left" :column="1" label-class="text-gray-400 inline-block w-[80px]">
            <NDescriptionsItem label="人员状态">
              {{ getUserStatus(userInfo) }}
            </NDescriptionsItem>
            <NDescriptionsItem label="主管">
              {{ userInfo.group_leader_name }}
            </NDescriptionsItem>
            <NDescriptionsItem label="组织架构">
              {{ userInfo.group }}
            </NDescriptionsItem>
          </NDescriptions>
        </div>
        <NFlex vertical class="overflow-hidden">
          <!-- 行为详情表格 -->
          <NDataTable
            :columns="columns"
            :data="tableData"
            :max-height="maxHeight"
            :bordered="true"
            class="my-2"
          />
          <!-- 分页 -->
          <div class="flex justify-end items-center">
            <div class="mr-5">
              共{{ page.total }}条
            </div>
            <NPagination
              v-model:page="page.pageSize"
              :item-count="page.total"
              @update:page="handleCurrentChange"
            />
          </div>
        </NFlex>
        <!-- 动态/评论区 -->
        <div v-show="isAIAnalying" class="activity-section">
          <div class="mb-3 comment-editor-card">
            <MdEditor
              ref="mdEditorRef"
              v-model:value="analysiResult"
              placeholder="请输入解读内容..."
              :height="commentHeight"
            />
          </div>
          <NFlex justify="start">
            <NButton :loading="btnLoading" type="primary" @click="saveAnalyResult">
              {{ btnLoading ? "分析中" : "保存" }}
            </NButton>
            <NButton @click="handleCancelEdit">
              取消
            </NButton>
          </NFlex>
        </div>
      </NFlex>
    </NDrawerContent>
  </NDrawer>
</template>

<script lang="ts" setup>
  import type { BehaviorExpanded } from "-/behavior";
  import { dateUtil, formatToDate } from "@celeris/utils";
  import { NFlex, NPopover } from "naive-ui";
  import { addModelResult, getActionDetailList } from "~/apis/internal/behavior";
  import { userListApi } from "~/apis/internal/user";
  import { MdEditor } from "~/component/MdEditor";
  import { createModelEventSource } from "~/utils/eventSourceHelper";

  defineOptions({
    name: "BehaviorDetailDrawer",
    inheritAttrs: false,
  });

  const props = withDefaults(defineProps<{
    show: boolean;
    data?: BehaviorExpanded | null;
    searchParam?: any;
  }>(), {
    show: false,
    data: () => ({}) as BehaviorExpanded,
    searchParam: () => ({}),
  });

  const emit = defineEmits<{
    (e: "update:show", show: boolean): void;
  }>();

  const show = toRef(props, "show");
  const drawerData = ref<Record<string, any>>({});
  const userInfo = ref<Record<string, any>>({});
  const tableData = ref<any[]>([]);
  const page = ref({ current: 1, pageSize: 10, total: 0 });
  const maxHeight = ref(window.innerHeight - 290);
  const isAIAnalying = ref(false);
  const analysiResult = ref("");
  const btnLoading = ref(false);
  const eventSource = ref<any>(null);
  const mdEditorRef = ref();
  const commentHeight = ref(450);
  let heightChangeTimer: any = null;

  const dialog = useDialog();
  const message = useMessage();

  const columns = [
    {
      title: "行为源",
      key: "behaviorSrc",
    },
    {
      title: "行为摘要",
      key: "summary",
      render(row: any) {
        return h("span", null, `${row.behaviorSummary}${row.behaviorCount}${row.behaviorUnit}`);
      },
    },
    {
      title: "行为详情",
      key: "behaviorDetails",
      render(row: any) {
        const content = row.behaviorDetails && isJSON(row.behaviorDetails)
          ? formatJSON(row.behaviorDetails)
          : row.behaviorDetails;
        return h(NPopover, { trigger: "hover", placement: "top", style: { "white-space": "pre-wrap" } }, {
          trigger: () => h("span", { style: "display:inline-block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:100%;" }, content),
          default: () => h("div", { class: "popover-content" }, content),
        });
      },
    },
    {
      title: "时间",
      key: "date",
      render(row: any) {
        return formatToDate(row.date);
      },
      ellipsis: {
        tooltip: true,
      },
    },
  ];

  /**
   * @description 弹窗打开初始化抽屉数据
   * @returns {void}
   */
  watch(show, async (val) => {
    if (val) {
      drawerData.value = props.data || {};
      await getActionDataList(props.searchParam);
      await getUserInfoDetail();
      nextTick(() => {
        maxHeight.value = window.innerHeight - 290;
      });
    }
  });

  onMounted(() => {
    // 监听窗口大小变化
    window.addEventListener("resize", handleResize);
  });

  onBeforeUnmount(() => {
    // 移除监听
    window.removeEventListener("resize", handleResize);
    if (eventSource.value) {
      eventSource.value.disconnect();
    }
  });

  function handleResize() {
    maxHeight.value = window.innerHeight - 290;
  }

  /**
   * @description 处理抽屉的显示状态更新
   * @param {boolean} show - 当前抽屉显示状态
   * @returns {void}
   */
  function handleUpdateShow(show) {
    if (!show) {
      beforeHandleClose();
    }
  }

  /**
   * @description 获取用户详细信息
   * @returns {Promise<void>}
   */
  async function getUserInfoDetail() {
    userInfo.value = {};
    const query = {
      q: `page_size=1,page_number=1,uid=${drawerData.value.uid}`,
    };
    try {
      const res = await userListApi(query);
      userInfo.value = res.data[0] || {};
    } catch (e) {
      console.warn(e);
    }
  }

  /**
   * @description 获取行为详情数据列表
   * @param {any} searchParam - 查询参数
   * @returns {Promise<void>}
   */
  async function getActionDataList(searchParam) {
    const query = {
      uid: drawerData.value.uid,
      page_index: page.value.current,
      page_size: page.value.pageSize,
      ...searchParam,
    };
    tableData.value = [];
    page.value.total = 0;
    try {
      const res = await getActionDetailList(query);
      tableData.value = res.userBehavior;
      page.value.total = res.total;
    } catch (error) {
      console.error("getActionDataList error", error);
    }
  }

  /**
   * @description 获取用户状态文本
   * @param {Record<string, any>} user - 用户信息对象
   * @returns {string} 用户状态
   */
  function getUserStatus(user: Record<string, any>): string {
    const statusMap: Record<number, string> = {
      0: "正常",
      1: "离职",
      2: "过期",
      3: "禁用",
      4: "冻结",
    };
    if (user?.preserved !== undefined && user.preserved in statusMap) {
      return statusMap[user.preserved];
    } else if (user?.name) {
      return "正常";
    }
    return "";
  }

  /**
   * @description 判断字符串是否为 JSON 格式
   * @param {string} str - 待判断字符串
   * @returns {boolean} 是否为 JSON
   */
  function isJSON(str: string): boolean {
    try {
      JSON.parse(str);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * @description 格式化 JSON 字符串
   * @param {string} str - JSON 字符串
   * @returns {string} 格式化后的字符串
   */
  function formatJSON(str: string): string {
    try {
      const obj = JSON.parse(str);
      return JSON.stringify(obj, null, 2);
    } catch {
      return str;
    }
  }

  /**
   * @description 处理分页页码变化
   * @param {number} val - 当前页码
   * @returns {void}
   */
  function handleCurrentChange(val: number) {
    page.value.current = val;
    getActionDataList(props.searchParam);
  }

  /**
   * @description 关闭抽屉
   * @returns {void}
   */
  function handleHide() {
    emit("update:show", false);
  }

  /**
   * @description 设置编辑器高度，带防抖
   * @param {number} height - 编辑器高度
   * @returns {void}
   */
  function setEditorHeight(height: number) {
    if (heightChangeTimer) {
      clearTimeout(heightChangeTimer);
    }
    heightChangeTimer = setTimeout(() => {
      commentHeight.value = height;
    }, 200);
  }

  /**
   * @description 关闭前的处理逻辑，包含关闭确认
   * @returns {void}
   */
  function beforeHandleClose() {
    if (btnLoading.value || analysiResult.value) {
      dialog.warning({
        title: "警告",
        content: "关闭后生成的评论将丢失，是否继续？",
        positiveText: "继续",
        negativeText: "取消",
        onPositiveClick: () => {
          btnLoading.value = false;
          handleHide();
        },
        onNegativeClick: () => {
          mdEditorRef.value?.handleFocus?.();
        },
      });
      return;
    }
    handleHide();
  }

  /**
   * @description 触发大模型分析，开启 AI 分析流程
   * @returns {Promise<void>}
   */
  async function handleAIAnaly() {
    isAIAnalying.value = true;
    setEditorHeight(450);
    btnLoading.value = true;
    const url = `/api/v2/risk_detection/behavior/doAnalysis?Uid=${drawerData.value.uid}&From=${props.searchParam.from}&To=${props.searchParam.to}`;
    eventSource.value = createModelEventSource(url, {
      onContent: (content: string) => {
        analysiResult.value += content;
      },
      onError: () => {
        btnLoading.value = false;
        message.error("连接失败，请稍后重试");
      },
      onFinish: () => {
        btnLoading.value = false;
        eventSource.value.disconnect();
      },
    });
    eventSource.value.connect();
  }

  /**
   * @description 保存 AI 分析结果
   * @returns {Promise<void>}
   */
  async function saveAnalyResult() {
    const reportRange = `${dateUtil(props.searchParam.from).format("YYYYMMDD")}~${dateUtil(props.searchParam.to).format("YYYYMMDD")}`;
    const saveParams = {
      uid: drawerData.value.uid,
      userName: userInfo.value.name,
      reportRange,
      report: analysiResult.value,
    };
    try {
      await addModelResult(saveParams);
      message.success("保存成功，请到结果页查看");
      handleCancelEdit();
    } catch {
      message.error("保存失败");
    }
  }

  /**
   * @description 取消编辑，重置相关状态
   * @returns {void}
   */
  function handleCancelEdit() {
    analysiResult.value = "";
    isAIAnalying.value = false;
    btnLoading.value = false;
    if (eventSource.value) {
      eventSource.value.disconnect();
    }
    maxHeight.value = window.innerHeight - 290;
  }

  /**
   * @description 在抽屉关闭后重置相关数据
   * @returns {void}
   */
  function handleAfterLeave() {
    // 重置所有数据
    drawerData.value = {};
    userInfo.value = {};
    tableData.value = [];
    handleCancelEdit();
    setEditorHeight(100);
  }
</script>

<style scoped>
.popover-content {
  max-width: 800px;
  max-height: 400px;
  white-space: pre-wrap;
  word-break: break-all;
  overflow-y: auto;
}

.activity-section {
  position: relative;
  flex-shrink: 0; /* 防止被压缩，保持自身高度 */
}
/* ::v-deep .v-md-editor 只能在 <style lang="scss"> 或 <style lang="postcss"> 下用，普通 CSS 不支持，需移除或用深选择器方案 */
.activity-section .comment-editor-card .v-md-editor {
  transition: height 0.3s ease-in-out;
}
</style>
