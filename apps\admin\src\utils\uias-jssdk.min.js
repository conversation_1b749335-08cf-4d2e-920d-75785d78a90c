'use strict'
/*
 * 解构数组
 * @param {Array} e 要解构的数组
 * @param {Number} t 解构的长度
 * @returns {Array} 解构后的数组
 */
function _slicedToArray(e, t) {
  return (
    _arrayWithHoles(e) ||
    _iterableToArrayLimit(e, t) ||
    _unsupportedIterableToArray(e, t) ||
    _nonIterableRest()
  )
}
/*
 * 抛出错误
 * @throws {TypeError} 抛出错误
 */
function _nonIterableRest() {
  throw new TypeError(
    'Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'
  )
}
/*
 * 将各种类型的可迭代对象转换为数组
 * @param {Any} e 要转换的对象
 * @param {Number} t 长度限制
 * @returns {Array} 转换后的数组
 */
function _unsupportedIterableToArray(e, t) {
  if (e) {
    if ('string' == typeof e) return _arrayLikeToArray(e, t)
    var r = Object.prototype.toString.call(e).slice(8, -1)
    return (
      'Object' === r && e.constructor && (r = e.constructor.name),
      'Map' === r || 'Set' === r
        ? Array.from(e)
        : 'Arguments' === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)
        ? _arrayLikeToArray(e, t)
        : void 0
    )
  }
}
/*
 * 将类数组对象转换为数组
 * @param {ArrayLike} e 类数组对象
 * @param {Number} t 长度限制
 * @returns {Array} 转换后的数组
 */
function _arrayLikeToArray(e, t) {
  ;(null == t || t > e.length) && (t = e.length)
  for (var r = 0, n = new Array(t); r < t; r++) n[r] = e[r]
  return n
}
/*
 * 将可迭代对象转换为数组
 * @param {Iterable} e 可迭代对象
 * @param {Number} t 长度限制
 * @returns {Array} 转换后的数组
 */
function _iterableToArrayLimit(e, t) {
  var r = null == e ? null : ('undefined' != typeof Symbol && e[Symbol.iterator]) || e['@@iterator']
  if (null != r) {
    var n,
      o,
      i,
      a,
      s = [],
      u = !0,
      c = !1
    try {
      if (((i = (r = r.call(e)).next), 0 === t)) {
        if (Object(r) !== r) return
        u = !1
      } else for (; !(u = (n = i.call(r)).done) && (s.push(n.value), s.length !== t); u = !0);
    } catch (e) {
      ;(c = !0), (o = e)
    } finally {
      try {
        if (!u && null != r.return && ((a = r.return()), Object(a) !== a)) return
      } finally {
        if (c) throw o
      }
    }
    return s
  }
}
/*
 * 检查是否为数组
 * @param {Any} e 要检查的值
 * @returns {Array} 如果是数组，则返回该数组
 */
function _arrayWithHoles(e) {
  if (Array.isArray(e)) return e
}
/*
 * 检查是否为类的实例
 * @param {Any} e 实例
 * @param {Function} t 类的构造函数
 * @throws {TypeError} 如果不是类的实例，则抛出错误
 */
function _classCallCheck(e, t) {
  if (!(e instanceof t)) throw new TypeError('Cannot call a class as a function')
}
/*
 * 定义属性
 * @param {Object} e 对象
 * @param {Array} t 属性数组
 */
function _defineProperties(e, t) {
  for (var r = 0; r < t.length; r++) {
    var n = t[r]
    ;(n.enumerable = n.enumerable || !1),
      (n.configurable = !0),
      'value' in n && (n.writable = !0),
      Object.defineProperty(e, _toPropertyKey(n.key), n)
  }
}
/*
 * 创建类
 * @param {Function} e 构造函数
 * @param {Array} t 实例属性
 * @param {Array} r 静态属性
 * @returns {Function} 构造函数
 */
function _createClass(e, t, r) {
  return (
    t && _defineProperties(e.prototype, t),
    r && _defineProperties(e, r),
    Object.defineProperty(e, 'prototype', { writable: !1 }),
    e
  )
}
/*
 * 转换为原始类型
 * @param {Any} e 值
 * @param {String} t 类型
 * @returns {Any} 值
 */
function _toPropertyKey(e) {
  var t = _toPrimitive(e, 'string')
  return 'symbol' == _typeof(t) ? t : t + ''
}
/*
 * 将对象转换为原始类型
 * @param {*} e 要转换的对象
 * @param {string} t 转换的类型
 * @returns {*} 转换后的原始类型
 */
function _toPrimitive(e, t) {
  if ('object' != _typeof(e) || !e) return e
  var r = e[Symbol.toPrimitive]
  if (void 0 !== r) {
    var n = r.call(e, t || 'default')
    if ('object' != _typeof(n)) return n
    throw new TypeError('@@toPrimitive must return a primitive value.')
  }
  return ('string' === t ? String : Number)(e)
}
/*
 * 获取对象的类型
 * @param {*} e 要获取类型的对象
 * @returns {string} 对象的类型
 */
function _typeof(e) {
  '@babel/helpers - typeof'
  return (_typeof =
    'function' == typeof Symbol && 'symbol' == typeof Symbol.iterator
      ? function (e) {
          return typeof e
        }
      : function (e) {
          return e && 'function' == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype
            ? 'symbol'
            : typeof e
        })(e)
}
/*
 * UiasSDK
 * @param {Object} e 全局对象
 * @param {Function} t 创建UiasSDK的函数
 * @returns {Object} UiasSDK
 */
!(function (e, t) {
  'object' === ('undefined' == typeof module ? 'undefined' : _typeof(module)) &&
  'object' === _typeof(module.exports)
    ? (module.exports = t())
    : 'function' == typeof define && define.amd
    ? define('UiasSDK', [], t)
    : (e.UiasSDK = t())
// UiasSDK
})('undefined' != typeof window ? window : void 0, function () {
  return (
    Object.entries ||
      (Object.entries = function (e) {
        var t = []
        for (var r in e) e.hasOwnProperty(r) && t.push([r, e[r]])
        return t
      }),
    'function' != typeof Array.prototype.reduce &&
      (Array.prototype.reduce = function (e, t) {
        return (
          this.forEach(function (r, n, o) {
            t = e(t, r, n, o)
          }),
          t
        )
      }),
    console.log('iframe.min.js tip:', 'JavaScript introduction is normal'),
    // UiasSDK
    (function () {
      function e(t) {
        var r,
          n,
          o = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}
        _classCallCheck(this, e),
          (this.iframe = t),
          (this.events = {}),
          (this.options = {
            sms: null === (r = o.sms) || void 0 === r || r,
            scancode: null === (n = o.scancode) || void 0 === n || n,
            timestamp: new Date().getTime(),
          }),
          this.initialURL(),
          window.addEventListener('message', this.receiveMessage.bind(this), !1)
      }
      return _createClass(e, [
        {
          key: 'init',
          value: function (e) {
            var t = this,
              r = function () {
                return e.call(t)
              }
            this.iframe.addEventListener('load', r)
          },
        },
        {
          key: 'initialURL',
          value: function () {
            var e = this.iframe.src
            ;(this.iframe.src = Object.entries(this.options).reduce(function (e, t) {
              var r = _slicedToArray(t, 2),
                n = r[0],
                o = r[1]
              return (e += e.indexOf('?') < 0 ? '?'.concat(n, '=').concat(o) : '&'.concat(n, '=').concat(o))
            }, e)),
              console.log("iframe.min.js tip, iframe's src:", this.iframe.src)
          },
        },
        {
          key: 'receiveMessage',
          value: function (e) {
            var t = e.data
            this.events[t.type] && this.events[t.type](t)
          },
        },
        {
          key: 'sendMessage',
          value: function (e) {
            this.iframe.contentWindow.postMessage(e, '*')
          },
        },
        {
          key: 'removeEvent',
          value: function (e) {
            delete this.events[e]
          },
        },
        {
          key: 'handleEvent',
          value: function (e, t) {
            this.events[e] = t
          },
        },
        {
          key: 'handleMessage',
          value: function (e) {
            var t = e.data,
              r = e.success,
              n = e.fail
            'success' === t.code ? r(t.data) : n(t)
          },
        },
      ])
    })()
  )
})
