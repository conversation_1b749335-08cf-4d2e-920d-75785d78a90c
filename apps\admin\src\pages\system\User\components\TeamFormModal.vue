<template>
  <CardModal
    v-bind="$attrs"
    class="w-xl"
    :title="modalTitle"
    :empty-func="() => false"
    :mask-closable="false"
    @update:show="handleHide"
    @after-enter="handleAfterEnter"
    @after-leave="handleAfterLeave"
  >
    <div class="p-4">
      <NForm
        ref="formRef"
        label-width="80"
        label-placement="left"
        require-mark-placement="left"
        :model="teamModel"
        :rules="formRules"
      >
        <NFormItem path="parent_id" label="上级团队">
          <UserTreeSelect
            v-model:value="teamModel.parent_id"
            :require-permission="false"
            :data-source="allUserTree"
            placeholder="请选择"
            style="width: 500px"
            clearable
            :disabled="isDisabledParent"
          />
        </NFormItem>
        <NFormItem path="name" label="团队名称">
          <NInput v-model:value="teamModel.name" placeholder="请输入团队名称" style="width: 500px" />
        </NFormItem>
      </NForm>
    </div>
    <template #footer>
      <NFlex justify="center">
        <NButton @click="handleHide">
          取消
        </NButton>
        <NButton type="primary" :loading="loading" @click="handleConfirm">
          确定
        </NButton>
      </NFlex>
    </template>
  </CardModal>
</template>

<script lang="ts" setup>
  import type { FormInst } from "naive-ui";
  import { createTeamApi, updateTeamApi } from "~/apis/internal/team";
  import CardModal from "~/component/CardModal/src/CardModal.vue";
  import UserTreeSelect from "~/component/UserTreeSelect/src/UserTreeSelect.vue";

  defineOptions({
    name: "TeamFormModal",
    inheritAttrs: false,
  });

  const props = withDefaults(defineProps<{
    data: { id?: number; name?: string; parent_id?: number | null; weight?: number } | null;
    allUserTree: any[];
  }>(), {
    data: () => ({ name: "", parent_id: null }),
    allUserTree: () => [],
  });

  const emit = defineEmits<{
    (e: "update:show", show: boolean): void;
    (e: "positiveClick", success: boolean): void;
  }>();

  const modalTitle = computed(() => props.data?.id ? "编辑团队" : "新建团队");
  const isDisabledParent = computed(() => props.data?.id && !props.data?.parent_id);

  const formRef = ref<FormInst | null>(null);
  interface TeamModel {
    name: string;
    parent_id: number | null;
  }
  const teamModel = ref<TeamModel>({ name: "", parent_id: null });

  // 只验证 name 字段，parent_id 将在提交前手动验证
  const formRules = {
    name: [{ required: true, message: "请输入团队名称", trigger: ["blur", "input"] }],
    // parent_id: [{ required: true, message: "请选择上级团队", trigger: ["blur", "input"] }],
  };
  const loading = ref(false);
  const message = useMessage();

  /**
   * @description 弹窗进入动画后初始化表单数据。
   * 若有传入 data，则初始化为编辑状态，否则清空表单。
   */
  function handleAfterEnter() {
    props.data ? initFormModel() : clearFormModel();
  }

  /**
   * @description 弹窗关闭动画后清空表单数据。
   */
  function handleAfterLeave() {
    clearFormModel();
  }

  /**
   * @description 初始化表单模型，将 props.data 的值赋给表单。
   */
  function initFormModel() {
    teamModel.value.name = props.data?.name ?? "";
    teamModel.value.parent_id = props.data?.parent_id ?? null;
  }

  /**
   * @description 清空表单模型并重置表单校验。
   */
  function clearFormModel() {
    teamModel.value = { name: "", parent_id: null };
    formRef.value?.restoreValidation?.();
  }

  /**
   * @description 关闭弹窗，通知父组件更新显示状态。
   */
  function handleHide() {
    emit("update:show", false);
  }

  /**
   * @description 验证 parent_id 是否已选择
   * @returns boolean 是否通过验证
   */
  function validateParentId(): boolean {
    if (!teamModel.value.parent_id && teamModel.value.parent_id !== 0) {
      message.error("请选择上级团队");
      return false;
    }
    return true;
  }

  /**
   * @description 提交表单，进行团队创建或编辑。
   * @returns Promise<void>
   */
  async function handleConfirm() {
    try {
      // 先验证表单其他字段
      await formRef.value?.validate();

      // 非根节点手动验证 parent_id
      if (!isDisabledParent.value && !validateParentId()) {
        return;
      }

      loading.value = true;
      if (props.data?.id) {
        await updateTeamApi({
          id: props.data?.id,
          name: teamModel.value.name,
          parent_id: teamModel.value.parent_id,
          weight: props.data.weight ?? 1,
        });
        message.success("修改成功");
      } else {
        await createTeamApi({
          name: teamModel.value.name,
          parent_id: teamModel.value.parent_id,
          weight: 1,
        });
        message.success("创建成功");
      }
      handleHide();
      emit("positiveClick", true);
    } catch (e) {
      message.error("操作失败,请重试");
    } finally {
      loading.value = false;
    }
  }
</script>

<style scoped></style>
