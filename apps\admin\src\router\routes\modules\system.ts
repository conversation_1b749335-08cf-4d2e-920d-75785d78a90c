import type { RouteRecordRaw } from "vue-router";
import { LAYOUT } from "~/router/constant";

const system: RouteRecordRaw = {
  path: "/sys",
  name: "sys",
  component: LAYOUT,
  redirect: "/sys/allUser",
  meta: {
    title: "系统管理",
    icon: "i-tabler-settings",
    orderNumber: 60,
    shouldVerifyVisiblePermission: false,
  },
  children: [
    {
      path: "all-user",
      name: "allUser",
      component: () => import("~/pages/system/AllUser.vue"),
      meta: {
        title: "全量用户",
        shouldVerifyVisiblePermission: false,
      },
    },
    {
      path: "permission",
      name: "sysPermission",
      component: () => import("~/pages/system/Permission.vue"),
      meta: {
        title: "策略管理",
        shouldVerifyVisiblePermission: false,
      },
    },
    {
      path: "tenant",
      name: "tenant",
      component: () => import("~/pages/system/tenant/index.vue"),
      meta: {
        title: "租户管理",
        shouldVerifyVisiblePermission: false,
      },
    },
  ],
};

export default system;
