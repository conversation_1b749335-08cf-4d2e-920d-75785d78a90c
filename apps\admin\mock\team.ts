import type { Mock<PERSON>eth<PERSON> } from "vite-plugin-mock";

export default [
  {
    url: "/api/v2/team/admin",
    method: "get",
    response: () => {
      return {
        code: 0,
        data: {
          tree: [
            {
              id: "888fa043-1ca1-4f59-8dbf-e88622f8087e",
              label: "智能边缘事业部",
              children: [
                {
                  id: "538fc01e-4c46-49ca-b12d-eff441ab33ae",
                  label: "CDN与边缘产品线",
                  pid: "888fa043-1ca1-4f59-8dbf-e88622f8087e",
                  leader: "任春德",
                  leader_id: "d11b3f12-ae54-4ecc-9628-1be8f83c96f5",
                  children: [
                    {
                      id: "447b26a8-3d85-4a29-a709-89b16412c5d9",
                      label: "缓存及网关",
                      pid: "538fc01e-4c46-49ca-b12d-eff441ab33ae",
                      leader: "任春德",
                      leader_id: "d11b3f12-ae54-4ecc-9628-1be8f83c96f5",
                    },
                    {
                      id: "48df528f-11eb-4c08-a071-cc5d48b91114",
                      label: "调度及配置",
                      pid: "538fc01e-4c46-49ca-b12d-eff441ab33ae",
                      leader: "刘志党",
                      leader_id: "95858557-04a9-4348-b8eb-f5b81a72bd93",
                    },
                    {
                      id: "70e7217b-c32e-4009-b9d6-dede154a75cf",
                      label: "CDN客户配置",
                      pid: "538fc01e-4c46-49ca-b12d-eff441ab33ae",
                      leader: "王昆",
                      leader_id: "deda317d-3104-4d84-a7d1-02c824bfddaa",
                    },
                    {
                      id: "af598f6a-a363-430e-b07e-bd94344e5366",
                      label: "测试1",
                      pid: "538fc01e-4c46-49ca-b12d-eff441ab33ae",
                      leader: "邱从贤",
                      leader_id: "e05ea128-283a-4027-918a-828ae57c4ee5",
                    },
                    {
                      id: "d09efd9b-6b15-402c-b566-3b1f46c6a2cd",
                      label: "测试2",
                      pid: "538fc01e-4c46-49ca-b12d-eff441ab33ae",
                      leader: "才华",
                      leader_id: "23a8dae3-e7af-407a-807c-e8b5e6e2c760",
                    },
                    {
                      id: "d18fb48f-03ee-4388-9762-2c695fc3baaf",
                      label: "前端1",
                      pid: "538fc01e-4c46-49ca-b12d-eff441ab33ae",
                      leader: "仇晗",
                      leader_id: "a57c93eb-17bf-48f9-a6da-2c1a92eca5e0",
                    },
                    {
                      id: "d412a2c7-81c9-4dba-b911-ca692b6cd685",
                      label: "前端2",
                      pid: "538fc01e-4c46-49ca-b12d-eff441ab33ae",
                      leader: "张晨晖",
                      leader_id: "d9f10f82-eba6-429d-8d80-11309d9b95b8",
                    },
                    {
                      id: "e00118dd-b402-4527-9d63-74ba5c8f3532",
                      label: "后端2",
                      pid: "538fc01e-4c46-49ca-b12d-eff441ab33ae",
                      leader: "张严明",
                      leader_id: "74728d0d-8797-42d3-bf84-a244dc62d926",
                    },
                  ],
                },
                {
                  id: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                  label: "大数据产品线",
                  pid: "888fa043-1ca1-4f59-8dbf-e88622f8087e",
                  leader: "王金土",
                  leader_id: "523de43b-5a06-4d51-9e76-f61939abcda8",
                  children: [
                    {
                      id: "0105fdac-d15f-4653-bc1d-a83c0d45c708",
                      label: "产品项管",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "潘星羽",
                      leader_id: "e318ebf5-67e9-44f2-bc98-aceca29764ee",
                    },
                    {
                      id: "049c7e84-1adc-4b1b-bb44-253e7f197751",
                      label: "管控平台",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "丛子涵",
                      leader_id: "099af405-979d-4fef-91ab-af6836114df0",
                    },
                    {
                      id: "13d3b2f6-554c-4d7e-803a-9bb0ada2e9d9",
                      label: "计算平台一组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "蔡东辉",
                      leader_id: "c5ab8c86-dcd2-4341-83b7-f06c217bb674",
                    },
                    {
                      id: "199ad1bc-3b47-4557-9116-6a869ef8ac81",
                      label: "技术服务",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "崔佳玲",
                      leader_id: "ce0f9a6e-c20b-4841-afe4-9a787a5c3b92",
                    },
                    {
                      id: "2352b443-14d9-434f-8350-c5cea98fb55f",
                      label: "业务架构",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "张龙海",
                      leader_id: "8489931c-07ee-4691-bdec-7b4287442ac3",
                    },
                    {
                      id: "33567b28-15b5-4af2-a2fd-9bfd720175bd",
                      label: "计算平台二组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "陈义伟",
                      leader_id: "79575c11-4c6d-4343-86cd-b760346f03d7",
                    },
                    {
                      id: "359a1874-886f-4de2-b804-968ea6711fa6",
                      label: "产品研发中心",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "王金土",
                      leader_id: "523de43b-5a06-4d51-9e76-f61939abcda8",
                    },
                    {
                      id: "4a54f37a-9c43-4308-8db3-a7cfe1d50880",
                      label: "产品运营中心",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "辛全超",
                      leader_id: "a840b523-3f3f-4ecb-b809-20d80ef6627e",
                    },
                    {
                      id: "4cec7bea-885f-4d39-808c-85d9aca7a4e1",
                      label: "基础支撑中心",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "蔡熙",
                      leader_id: "afdf821a-c986-48d8-b9fc-7a903f8842bf",
                    },
                    {
                      id: "6c4af675-a32b-488f-9e52-ee033bc55967",
                      label: "算法一组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "苏泽峰",
                      leader_id: "ef084c9d-9fc5-46a7-9b17-13922ef5534c",
                    },
                    {
                      id: "6e8270bf-46d2-49fc-b729-e7a5388c24c6",
                      label: "算法二组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "曹聪",
                      leader_id: "080fa1b9-29d4-4df7-8a22-36611ea3e909",
                    },
                    {
                      id: "6ff77d55-c459-4bde-8306-c5e49d3f63f5",
                      label: "算法三组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "齐铁鹏",
                      leader_id: "4c836d98-f375-42a5-87c4-51bca021d443",
                    },
                    {
                      id: "717179b2-0947-4494-ac21-cd0540fb0b33",
                      label: "算法四组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "段云涌",
                      leader_id: "6b789e0d-547b-4926-ad69-801767a6ffc1",
                    },
                    {
                      id: "74ad3e41-d801-4979-9b99-1632b393ad3a",
                      label: "前端一组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "吴小英",
                      leader_id: "f9e3a516-3083-4148-ab34-b0152b69b005",
                    },
                    {
                      id: "777c6618-3164-460a-a17a-1d5ecc299e96",
                      label: "前端二组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "潘艳婧",
                      leader_id: "185da9b0-3385-417c-8696-415319ccaa7d",
                    },
                    {
                      id: "8a5116e3-a4c7-4b11-b08d-101d1f6ea544",
                      label: "前端三组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "向校民",
                      leader_id: "2b3d8487-5e5f-4d3f-b743-cf07dc3ab02c",
                    },
                    {
                      id: "9011bb39-8226-4bb4-92d5-0de9d11e3f96",
                      label: "前端四组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "陈单江",
                      leader_id: "c05692dd-0235-4e2c-bc3d-1c8b989aa283",
                    },
                    {
                      id: "9f4ffa20-6ff0-4264-ab11-ae9b9b171965",
                      label: "后端一组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "张平",
                      leader_id: "e8e5dd5f-3fdd-40c3-92c9-963c1b10104e",
                    },
                    {
                      id: "c7ae948e-e4cd-4b71-9bbc-4e4c6aafab61",
                      label: "后端二组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "胡能宇",
                      leader_id: "1f6783c6-5309-40be-9be8-9650a6829316",
                    },
                    {
                      id: "d704a8b0-4053-4907-9eb1-d7930e37361a",
                      label: "后端三组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "邓英南",
                      leader_id: "2cb3a7d3-2ba0-434a-880f-4dd5aa108af3",
                    },
                    {
                      id: "f4f88f6f-3720-4c18-aa0e-b5e4df23a8d5",
                      label: "后端四组",
                      pid: "563a4fe3-e820-42e4-bf91-b7e60cb36493",
                      leader: "陈影旺",
                      leader_id: "9985992e-b84a-49d0-aac3-395182e7be10",
                    },
                  ],
                },
                {
                  id: "8c79d6b4-e84c-4f9a-bcb7-2764be68c7f4",
                  label: "综合支持中心",
                  pid: "888fa043-1ca1-4f59-8dbf-e88622f8087e",
                  leader: "林顺东",
                  leader_id: "33e75e4a-7d3a-4e9f-b8b2-dc7b723ec2f9",
                  children: [
                    {
                      id: "11a93c1d-4ea5-44a3-b798-00febe007403",
                      label: "项目管理组",
                      pid: "8c79d6b4-e84c-4f9a-bcb7-2764be68c7f4",
                      leader: "陈秋松",
                      leader_id: "a90ad007-48f8-4fe3-887a-77c22b061168",
                    },
                    {
                      id: "1fc8a4c6-f588-4c0c-b928-193213222d6b",
                      label: "综合管理组",
                      pid: "8c79d6b4-e84c-4f9a-bcb7-2764be68c7f4",
                      leader: "凌国辉",
                      leader_id: "3b6a162d-67b4-41fa-b7c5-276a3e38b040",
                    },
                    {
                      id: "230436fd-424d-4487-a45c-57b225b37c62",
                      label: "测试组",
                      pid: "8c79d6b4-e84c-4f9a-bcb7-2764be68c7f4",
                      leader: "彭华杰",
                      leader_id: "76eab064-385e-479d-bd65-39474c364202",
                    },
                  ],
                },
                {
                  id: "8c9a2cab-3414-4260-b009-d573cab6a696",
                  label: "技术支持中心",
                  pid: "888fa043-1ca1-4f59-8dbf-e88622f8087e",
                  leader: "林洁琬",
                  leader_id: "ef946dc3-523e-4e92-8ddb-aa7a4df82df3",
                  children: [
                    {
                      id: "109cfc51-2f1f-4bcb-b75d-859a8ea1a1f2",
                      label: "业务运营",
                      pid: "8c9a2cab-3414-4260-b009-d573cab6a696",
                      leader: "林洁琬",
                      leader_id: "ef946dc3-523e-4e92-8ddb-aa7a4df82df3",
                    },
                    {
                      id: "2ccbe5cb-6ecf-4092-bbf5-23a6b15fd8f7",
                      label: "平台运维",
                      pid: "8c9a2cab-3414-4260-b009-d573cab6a696",
                      leader: "周耀飞",
                      leader_id: "0302f352-0d3a-4f36-94d9-7c0f20423976",
                    },
                    {
                      id: "52c1e133-9e66-467a-ad92-ada6f4ffcef2",
                      label: "智能运维",
                      pid: "8c9a2cab-3414-4260-b009-d573cab6a696",
                      leader: "黄鹄",
                      leader_id: "5dfa36f1-d55e-46b0-bc97-553f5fc08c89",
                    },
                    {
                      id: "63167ca4-c53f-4e92-9474-0cea4de727dd",
                      label: "产品测试",
                      pid: "8c9a2cab-3414-4260-b009-d573cab6a696",
                      leader: "毛廷鸿",
                      leader_id: "091a10cc-5e71-4dd9-a24e-65955dd8506c",
                    },
                    {
                      id: "83a1a274-01db-4b45-b4e8-56bde33e3ffd",
                      label: "工程效能",
                      pid: "8c9a2cab-3414-4260-b009-d573cab6a696",
                      leader: "李旭",
                      leader_id: "ddac967b-a20e-4b7c-9a3f-13f0cd4dfe9a",
                    },
                  ],
                },
              ],
            },
          ],
        },
        message: "mock-数据获取成功",
      };
    },
  },
] as MockMethod[];
