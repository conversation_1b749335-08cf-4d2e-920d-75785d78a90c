import { describe, expect, it, vi } from "vitest";
import { mount } from "@vue/test-utils";
import { createPinia, setActivePinia } from "pinia";
import TenantSwitcher from "../TenantSwitcher.vue";

// Mock naive-ui components
vi.mock("naive-ui", () => ({
  NButton: { name: "NButton", template: "<button><slot /></button>" },
  NDropdown: { name: "NDropdown", template: "<div><slot /></div>" },
  useMessage: () => ({
    success: vi.fn(),
    error: vi.fn(),
  }),
}));

// Mock user store
vi.mock("~/store/modules/user", () => ({
  useUserStore: () => ({
    getCurrentTenant: { tenant_id: "tenant1", name: "当前租户", isDefault: true },
    getAvailableTenants: [
      { tenant_id: "tenant1", name: "当前租户", status: "active", isDefault: true },
      { tenant_id: "tenant2", name: "其他租户", status: "active", isDefault: false },
    ],
    getAvailableTenantsLoading: false,
    switchTenant: vi.fn(),
    fetchAvailableTenants: vi.fn(),
  }),
}));

describe("TenantSwitcher", () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it("应该正确渲染租户切换器", () => {
    const wrapper = mount(TenantSwitcher);
    expect(wrapper.exists()).toBe(true);
  });

  it("应该显示选择租户按钮", () => {
    const wrapper = mount(TenantSwitcher);
    expect(wrapper.text()).toContain("选择租户");
  });

  it("应该生成正确的租户选项", async () => {
    const wrapper = mount(TenantSwitcher);
    const vm = wrapper.vm as any;
    
    expect(vm.tenantOptions).toHaveLength(2);
    expect(vm.tenantOptions[0].key).toBe("tenant1");
    expect(vm.tenantOptions[0].label).toBe("当前租户");
    expect(vm.tenantOptions[1].key).toBe("tenant2");
    expect(vm.tenantOptions[1].label).toBe("其他租户");
  });
});
