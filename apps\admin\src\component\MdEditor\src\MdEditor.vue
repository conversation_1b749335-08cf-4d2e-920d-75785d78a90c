<template>
  <div>
    <MdEditor
      ref="editorRef"
      v-model="editorValue"
      :disabled-menus="['save']"
      :style="{ height: `${height}px` }"
      @blur="handleEditorBlur"
      @focus="handleEditorFocus"
    ></MdEditor>
  </div>
</template>

<script setup lang="ts">
  import { MdEditor } from "md-editor-v3";
  import { nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue";
  import "md-editor-v3/lib/style.css";

  // 将接口定义移到组件外部
  export interface MdEditorProps {
    content?: string;
    height?: number;
  }

  export interface MdEditorEmits {
    (e: "update:content", value: string): void;
    (e: "onFocus", event: Event): void;
    (e: "onBlur", event: Event): void;
  }

  defineOptions({
    name: "MdEditor",
  });

  const props = withDefaults(defineProps<MdEditorProps>(), {
    content: "",
    height: 300,
  });

  const emit = defineEmits<MdEditorEmits>();

  // 响应式数据
  const editorValue = ref<string>(props.content || "");
  const editorRef = ref();

  // 监听父组件传入的content变化
  watch(
    () => props.content,
    (newVal) => {
      if (editorValue.value !== newVal) {
        editorValue.value = newVal || "";
      }
    },
    { immediate: true },
  );

  // 监听编辑器内容变化
  watch(editorValue, (newVal) => {
    emit("update:content", newVal);
  });

  // 事件处理方法
  function handleEditorFocus(e: Event) {
    emit("onFocus", e);
  }

  function handleEditorBlur(e: Event) {
    emit("onBlur", e);
  }

  // 手动聚焦编辑器
  function handleFocus() {
    editorRef.value?.focus();
  }

  // 组件挂载后添加事件监听
  onMounted(() => {
    // 延迟执行，确保DOM完全渲染
    nextTick(() => {
      // 尝试多种可能的选择器
      const selectors = [
        ".v-md-textarea-editor textarea",
        ".md-editor-text-container textarea",
        ".md-editor-text textarea",
        "textarea",
        ".CodeMirror textarea",
      ];

      let editorElement: HTMLElement | null = null;
      for (const selector of selectors) {
        editorElement = editorRef.value?.$el?.querySelector(selector) as HTMLElement;
        if (editorElement) {
          break;
        }
      }

      if (editorElement) {
        editorElement.addEventListener("focus", handleEditorFocus);
        editorElement.addEventListener("blur", handleEditorBlur);
      } else {
        console.warn("Could not find editor textarea element");
      }
    });
  });

  // 组件卸载前移除事件监听
  onBeforeUnmount(() => {
    // 移除事件监听，避免内存泄漏
    const selectors = [
      ".v-md-textarea-editor textarea",
      ".md-editor-text-container textarea",
      ".md-editor-text textarea",
      "textarea",
      ".CodeMirror textarea",
    ];

    let editorElement: HTMLElement | null = null;
    for (const selector of selectors) {
      editorElement = editorRef.value?.$el?.querySelector(selector) as HTMLElement;
      if (editorElement) {
        break;
      }
    }

    if (editorElement) {
      editorElement.removeEventListener("focus", handleEditorFocus);
      editorElement.removeEventListener("blur", handleEditorBlur);
    }
  });

  // 暴露方法给父组件
  defineExpose({
    handleFocus,
  });
</script>

<style>
</style>
