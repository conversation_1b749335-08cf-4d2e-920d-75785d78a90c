interface EventSourceOptions {
  url: string;
  onMessage?: (data: any) => void;
  onError?: (error: any) => void;
  onClose?: () => void;
  parseJSON?: boolean;
}

class EventSourceHelper {
  private eventSource: EventSource | null = null;

  constructor(private options: EventSourceOptions) {}

  connect() {
    if (this.eventSource) {
      this.disconnect();
    }

    this.eventSource = new EventSource(this.options.url);

    this.eventSource.onmessage = (event) => {
      try {
        const data = this.options.parseJSON ? JSON.parse(event.data) : event.data;
        this.options.onMessage?.(data);
      } catch (error) {
        this.options.onError?.(error);
      }
    };

    this.eventSource.onerror = (error) => {
      this.options.onError?.(error);
      this.disconnect();
    };
  }

  disconnect() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
      this.options.onClose?.();
    }
  }
}

// 创建用于大模型接口的专用方法
export function createModelEventSource(url: string, callbacks: {
  onContent?: (content: string) => void;
  onError?: (error: any) => void;
  onFinish?: () => void;
}) {
  return new EventSourceHelper({
    url,
    parseJSON: true,
    onMessage: (data) => {
      if (data.event === "message") {
        callbacks.onContent?.(data.answer);
      } else if (data.event === "message_end") {
        callbacks.onFinish?.();
      } else {
        callbacks.onError?.(data);
      }
    },
    onError: callbacks.onError,
    onClose: callbacks.onFinish,
  });
}