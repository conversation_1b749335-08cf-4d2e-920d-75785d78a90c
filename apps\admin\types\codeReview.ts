export interface CodeReviewAuthor {
  id?: number,
  name?: string,
  username?: string,
  state?: string,
  web_url?: string
}

export interface CodeReviewItem {
  id: number;
  iid: number;
  project_id: number;
  title: string;
  description?: string;
  state: string;
  stateName?: string;
  created_at: string;
  updated_at?: string;
  target_branch: string;
  source_branch: string;
  author: CodeReviewAuthor;
  web_url?: string;
}

export interface CodeReviewListParams {
  page: number;
  pageSize: number;
  state: string;
  isAssign: number;
}

export interface CodeReviewDetailParams {
  projectId: number;
  iid: number;
}

export interface CodeReviewDetail {
  data?: CodeReviewItem;
  commit_count?: number;
  diff_count?: number;
  note_count?: number;
  notes?: CodeReviewNote[];
  id?: number;
  iid?: number;
  project_id?: number;
}

export interface CodeReviewNote {
  id: number;
  body: string;
  author: CodeReviewAuthor;
  created_at: string;
  updated_at?: string;
}

export interface GitMessageParams {
  mergeLink: string;
}

export interface AddCommentParams {
  projectId: number;
  iid: number;
  content: string;
  noteId?: number;
}

export interface DeleteCommentParams {
  projectId: number;
  iid: number;
  noteId: number;
}
