/* zoom-fade */
.zoom-fade-leave-active,
.zoom-fade-enter-active {
  transition: all 0.5s cubic-bezier(0.76, 0, 0.24, 1);
}
.zoom-fade-enter-from {
  opacity: 0;
  transform: scale(0.9);
}
.zoom-fade-enter-to {
  opacity: 1;
  transform: scale(1);
}
.zoom-fade-leave-to {
  opacity: 0;
  transform: scale(1.1);
}

/* zoom-out */
.zoom-out-leave-active,
.zoom-out-enter-active {
  transition: all 0.5s cubic-bezier(0.76, 0, 0.24, 1);
}
.zoom-out-enter-from {
  opacity: 0;
  transform: scale(1.1);
}
.zoom-out-enter-to {
  opacity: 1;
  transform: scale(1);
}
.zoom-out-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* fade-slide */
.fade-slide-leave-active,
.fade-slide-enter-active {
  transition: all 0.5s cubic-bezier(0.76, 0, 0.24, 1);
}
.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(100%);
}
.fade-slide-enter-to {
  opacity: 1;
  transform: translateX(0);
}
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-100%);
}

/* fade */
.fade-leave-active,
.fade-enter-active {
  transition: all 0.5s;
}
.fade-enter-from {
  opacity: 0;
}
.fade-leave-to {
  opacity: 0;
}

/* fade-bottom */
.fade-bottom-leave-active,
.fade-bottom-enter-active {
  transition: all 0.5s cubic-bezier(0.76, 0, 0.24, 1);
}
.fade-bottom-enter-from {
  opacity: 0;
  transform: translateY(100%);
}
.fade-bottom-enter-to {
  opacity: 1;
  transform: translateY(0);
}
.fade-bottom-leave-to {
  opacity: 0;
  transform: translateY(-100%);
}

/* fade-scale */
.fade-scale-leave-active,
.fade-scale-enter-active {
  transition: all 0.5s cubic-bezier(0.76, 0, 0.24, 1);
}
.fade-scale-enter-from {
  opacity: 0;
  transform: scale(0.9);
}
.fade-scale-enter-to {
  opacity: 1;
  transform: scale(1);
}
.fade-scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
