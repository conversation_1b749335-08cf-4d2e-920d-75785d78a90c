import type { PaginationInfo, PaginationProps } from "@celeris/ca-components";
import type { TableColumns } from "naive-ui/es/data-table/src/interface";
import type { Ref } from "vue";
import { isFunction } from "@celeris/utils";
import { get, has } from "lodash-es";

export type TableItemType = AnyObject & {
  key: string;
};

export type PathParam = string | number | null | undefined;

export interface UseTableLoadDataOptions<
  QueryCriteria extends Record<string, any> = AnyObject,
  TableItem extends Record<string, any> = AnyObject,
  NewTableItem extends Record<string, any> = AnyObject,
> {
  beforeLoad?: () => boolean;
  dataPath?: string;
  totalPath?: string;
  pageField?: string;
  pageSizeField?: string;
  withPagination?: boolean;
  pathParam?: string | number | null | undefined;
  getQueryCriteria?: () => QueryCriteria;
  tableRequest: (pathParamOrQueryCriteria?: PathParam | QueryCriteria, queryCriteria?: QueryCriteria) => Promise<any>;
  handleTableData?: (tableData: TableItem[]) => NewTableItem[];
}

export interface UseTableReturn<
  QueryCriteria extends Record<string, any> = AnyObject,
  TableItem extends Record<string, any> = AnyObject,
  NewTableItem extends Record<string, any> = AnyObject,
> {
  tableLoading: Ref<boolean>;
  tableData: Ref<NewTableItem[]>;
  tablePagination: Ref<PaginationProps>;
  loadData: (options: UseTableLoadDataOptions<QueryCriteria, TableItem, NewTableItem>) => Promise<unknown>;
  reloadData: () => Promise<unknown>;
  refreshData: () => Promise<unknown>;
  handleTableData: (tableData: TableItem[]) => NewTableItem[];
  handlePageChange: (page: number) => Promise<unknown>;
  handlePageSizeChange: (pageSize: number) => Promise<unknown>;
  createTableColumns: (columns?: TableColumns) => TableColumns;
  handleScroll: (e: Event) => void;
}

const INIT_PAGINATION = {
  page: 1, // 当前页面
  // pageCount: 1, // 总页数
  itemCount: 0, // 总条数
  pageSize: 20, // 每页条数
  pageSizes: [20, 50, 100], // 每页条数
  showSizePicker: true, // 每页条数选择器
  // selectProps: {}, // 分页大小选择器属性
  // pageSlot: 9, // 页码显示的个数
  prefix(info: PaginationInfo) { // 分页前缀
    return `总条数：${info.itemCount}`;
  },
};

/**
 * 处理表格数据，为每条数据添加 key
 * Handle table data, add key for each item
 */
function _handleTableData<
  TableItem extends Record<string, any> = AnyObject,
  NewTableItem extends Record<string, any> = AnyObject,
>(dataSource: TableItem[]): NewTableItem[] {
  const firstItem = dataSource[0];
  if (!has(firstItem, "key")) {
    return dataSource.map((item, index) => {
      return {
        ...item,
        key: item.id ? String(item.id) : index.toString(),
      } as unknown as NewTableItem;
    });
  }
  return dataSource as unknown as NewTableItem[];
}

/**
 * 表格相关操作
 * Table related operations
 */
export function useTable<
  QueryCriteria extends Record<string, any> = AnyObject,
  TableItem extends Record<string, any> = AnyObject,
  NewTableItem extends Record<string, any> = AnyObject,
>(): UseTableReturn<QueryCriteria, TableItem, NewTableItem> {
  const tableLoading = ref(false);
  const tableData = ref<NewTableItem[]>([]) as Ref<NewTableItem[]>;
  const tableColumns = ref<TableColumns>([]);
  const tablePagination = ref<PaginationProps>({ ...INIT_PAGINATION });

  let tableLoadDataOptions: UseTableLoadDataOptions<QueryCriteria, TableItem, NewTableItem>;

  function loadData(options: UseTableLoadDataOptions<QueryCriteria, TableItem, NewTableItem>): Promise<unknown> {
    tableLoadDataOptions = options;

    const {
      beforeLoad,
      dataPath,
      totalPath = "total",
      pageField = "page_index",
      pageSizeField = "page_size",
      withPagination = true,
      pathParam,
      getQueryCriteria,
      tableRequest,
      handleTableData,
    } = tableLoadDataOptions;

    if (beforeLoad && !beforeLoad()) {
      return Promise.reject(new Error("[useTable] beforeLoad is false."));
    }

    let queryCriteria = getQueryCriteria?.() || {} as QueryCriteria;

    if (withPagination) {
      const paginationParams = {
        [pageField]: tablePagination.value.page,
        [pageSizeField]: tablePagination.value.pageSize,
      };
      queryCriteria = { ...queryCriteria, ...paginationParams } as QueryCriteria;
    }

    tableLoading.value = true;

    const args = pathParam ? [pathParam, queryCriteria] : [queryCriteria];
    return tableRequest(...args)
      .then((res: unknown) => {
        let items: TableItem[] = [];
        let total: number = 0;
        if (dataPath) {
          items = (get(res, dataPath, []) as TableItem[]) || [];
          total = Number(get(res, totalPath, 0));
        } else {
          items = (res as TableItem[]) || [];
          total = items.length;
        }
        tableData.value = (handleTableData && isFunction(handleTableData) ? handleTableData(items) : _handleTableData(items)) as NewTableItem[];
        tablePagination.value.itemCount = total;
        return res;
      })
      .finally(() => {
        tableLoading.value = false;
      });
  }

  /**
   * 重新加载数据
   * 1. 重置页码为第一页
   * 2. 加载数据
   */
  function reloadData() {
    tablePagination.value.page = 1;
    return loadData(tableLoadDataOptions);
  }

  /**
   * 刷新数据
   * 1. 重新加载数据
   */
  function refreshData() {
    return loadData(tableLoadDataOptions);
  }

  /**
   * 处理页码变化
   * 1. 设置当前页码
   * 2. 加载数据
   */
  function handlePageChange(page: number) {
    tablePagination.value.page = page;
    return loadData(tableLoadDataOptions);
  }

  /**
   * 处理每页条数变化
   * 1. 重置页码为第一页
   * 2. 设置当前每页条数
   * 3. 加载数据
   */
  function handlePageSizeChange(pageSize: number) {
    tablePagination.value.page = 1;
    tablePagination.value.pageSize = pageSize;
    return loadData(tableLoadDataOptions);
  }

  /**
   * 创建表格列
   * 1. 设置表格列
   * 2. 返回表格列
   */
  function createTableColumns(columns?: TableColumns) {
    tableColumns.value = columns || [];
    return tableColumns.value;
  }

  /**
   * 当有固定列且 scroll-x="min-content" 时，右侧固定列的阴影消失了，此函数临时解决该问题。
   */
  function handleScroll(e: Event) {
    const baseTableEl = (e.target as HTMLDivElement)?.parentElement?.parentElement;
    const rootTableEl = baseTableEl?.parentElement?.parentElement;
    const { scrollLeft, clientWidth, scrollWidth } = e.target as HTMLDivElement;
    if (scrollLeft + clientWidth + 0.5 === scrollWidth) { // 滚动到最右侧
      rootTableEl?.classList?.add?.("scroll-right");
    } else {
      rootTableEl?.classList?.remove?.("scroll-right");
    }
  }

  // @ts-expect-error 此处会报错 Vue: Type instantiation is excessively deep and possibly infinite.
  // 原因是类型推断过于复杂，可能存在深度嵌套循环的现象，问题出在 tablePagination 上
  return {
    tableLoading,
    tableData,
    tablePagination,
    loadData,
    reloadData,
    refreshData,
    handleTableData: _handleTableData,
    handlePageChange,
    handlePageSizeChange,
    createTableColumns,
    handleScroll,
  };
}
