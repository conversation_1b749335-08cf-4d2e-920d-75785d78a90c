# EE绩效平台

## FAQ

### 1. 为什么【v-model】要写成【v-model:value】？

<details>
  <summary>展开查看</summary>

[文档：vue](https://v3-migration.vuejs.org/zh/breaking-changes/v-model.html)

[文档：antdv](https://next.antdv.com/docs/vue/migration-v2-cn#%E8%B0%83%E6%95%B4%E7%9A%84-api)

</details>

### 2. 自己写组件或元素时，主题色如何和 NaiveUI 组件库保持一致？

<details>
  <summary>展开查看</summary>

  ```ts
  import { useThemeVars } from "@celeris/ca-components";

  const themeVariables = useThemeVars();

  console.log("标题色调", themeVariables.value.textColor1);
  console.log("副标题色调", themeVariables.value.textColor3);
  ```
</details>

### 3. UnoCSS 中怎么写 deep 样式？

<details>
  <summary>展开查看</summary>

  冒号左侧部分表示深度选择器选择的元素

  冒号右侧部分表示选择的元素应用的样式，单个样式可以不加括号

  所以下面的例子的意思是：选择 CAAppLogo 组件中的 img 标签元素，应用样式 `my-3 mr-1 -ml-1`

  ```vue
  <CAAppLogo class="[&_img]:(my-3 mr-1 -ml-1)"></CAAppLogo>
  ```
</details>

### 4. NDataTable组件有固定列和可变列宽时，scroll-x 怎么设置？

<details>
  <summary>展开查看</summary>

一些前提条件，来自官方文档：

1. 在固定列时，列的 width 必须设置
2. 在固定列时，表格内容的横向宽度 scroll-x 必须设置

错误的做法：

1. 每列的 width 都设置
2. scroll-x 的值就是所有列的 width 相加
3. 列宽改变时动态设置列的 width 和表格内容横向宽度 scroll-x 的值

这会导致列宽改变不跟手

正确的做法([GitHub](https://github.com/tusen-ai/naive-ui/issues/5044))：

  ```vue
  <NDataTable ... scroll-x="min-content" ... />
  ```
</details>

