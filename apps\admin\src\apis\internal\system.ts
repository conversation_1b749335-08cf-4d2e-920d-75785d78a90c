import type { ExceptionPersonWrapperInfo } from "-/performance";
import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";
import { replaceUrlPathParams } from "@celeris/utils";

enum API {
  deleteRoles = "/v2/permissions",
  getAuthzsUser = "/v2/platform/user",
  createRole = "/v2/permissions",
  getAdmin = "/v2/permissions/admin",
  getTeamTree = "/v2/permissions/teamTree/admin",

  ExceptionUserList = "/v2/efficiency/violationList",
  CreateExceptionUser = "/v2/efficiency/addViolation",
  UpdateExceptionUser = "/v2/efficiency/saveViolation",
  DeleteExceptionUser = "/v2/efficiency/deleteViolation?id={id}",
}

// 获取已授权用户列表
export function getAdmin(params) {
  return request.get({ url: API.getAdmin, params });
}
// 删除用户授权
export function deleteRoles(params) {
  return request.delete({ url: API.deleteRoles, params });
}

// 查询用户
export function getAuthzsUser(params) {
  return request.get({ url: API.getAuthzsUser, params });
}

// 新建用户授权
export function createRole(params) {
  return request.post({ url: API.createRole, params });
}

// 获取团队树
export function getTeamTree() {
  return request.get({ url: API.getTeamTree });
}

// 获取异常用户列表
export function exceptionUserListApi(params: AnyObject, errorMessageMode: MessageMode = "none") {
  return request.get<ExceptionPersonWrapperInfo>({ url: API.ExceptionUserList, params }, { errorMessageMode });
}

// 保存异常用户
export function createExceptionUserApi(data: AnyObject, errorMessageMode: MessageMode = "message") {
  return request.post({ url: API.CreateExceptionUser, data }, { errorMessageMode });
}

// 更新异常用户
export function updateExceptionUserApi(data: AnyObject, errorMessageMode: MessageMode = "message") {
  return request.post({ url: API.UpdateExceptionUser, data }, { errorMessageMode });
}

// 删除异常用户
export function deleteExceptionUserApi(id: number, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.DeleteExceptionUser, { id });
  return request.delete({ url }, { errorMessageMode });
}
