<template>
  <NSelect
    v-bind="$attrs"
    label-field="nickname"
    value-field="username"
    :render-label="renderLabel"
    filterable
    :remote="remote"
    :loading="loading"
    :options="dataSource"
    :consistent-menu-width="false"
    :reset-menu-on-options-change="false"
    @scroll="handleScroll"
    @search="handleSearch"
    @focus="handleFocus"
  />
</template>

<script setup lang="ts">
  import type { SimpleUserItem, UserListItem } from "-/user";
  import type { SelectOption } from "naive-ui";
  import type { VNodeChild } from "vue";
  import { userListApi } from "~/apis/internal/user";

  const props = withDefaults(defineProps<{
    remote?: boolean;
    options?: SimpleUserItem[];
    /** 自定义请求参数 */
    queryParams?: Record<string, any>;
    /** 每页加载数量 */
    pageSize?: number;
  }>(), {
    remote: false,
    options: () => [] as SimpleUserItem[],
    queryParams: () => ({}),
    pageSize: 20,
  });

  const loading = ref(false);
  const dataSource = ref<SimpleUserItem[]>(props.options || []);

  const page = ref(1);
  const hasMore = ref(true);
  const searchQuery = ref("");

  /**
   * 获取用户列表（支持分页和追加）
   * @param query 搜索关键词
   * @param append 是否为追加数据（分页加载时为 true，普通搜索为 false）
   */
  async function fetchUserList(query: string, append = false) {
    try {
      const params = {
        // keyword: query,
        q: `nickname=~${query}`,
        page: page.value,
        limit: props.pageSize,
        domain_id: "one",
        ...props.queryParams,
      };
      loading.value = true;
      const res = await userListApi(params);
      res.data.value = res.data.value || [];
      props.options?.forEach?.((item) => {
        const data = res.data.value.find(item2 => item2.username === item.username);
        if (!data) {
          res.data.value.unshift(item as UserListItem);
        }
      });
      if (append) {
        dataSource.value = [...dataSource.value, ...res.data.value];
      } else {
        dataSource.value = res.data.value;
      }
      hasMore.value = res.data.total > dataSource.value.length;
    } finally {
      loading.value = false;
    }
  }

  function renderLabel(option: SelectOption): VNodeChild {
    return `${option.nickname}(${option.username})`;
  }

  watch(() => props.options, (newVal) => {
    newVal?.forEach?.((item) => {
      const data = dataSource.value.find(item2 => item2.username === item.username);
      if (!data) {
        dataSource.value.unshift(item);
      }
    });
  });

  // 输入聚集
  function handleFocus() {
    if (dataSource.value.length === 0) {
      handleSearch("");
    }
  }

  /**
   * 处理用户搜索输入，重置分页并发起新查询
   * @param query 搜索关键词
   */
  const handleSearch = useDebounceFn(async (query: string) => {
    // 关键字不为空时才调查询接口
    // if (!query.length) {
    //   dataSource.value = [];
    //   return;
    // }
    searchQuery.value = query;
    hasMore.value = true;
    page.value = 1;
    dataSource.value = [];
    await fetchUserList(query, false);
  }, 300);

  /**
   * 滚动到底部时加载下一页数据
   */
  async function loadMoreData() {
    page.value += 1;
    await fetchUserList(searchQuery.value, true);
  }

  /**
   * 下拉菜单滚动事件处理，触发分页加载
   * @param e 滚动事件对象
   */
  function handleScroll(e: Event) {
    const currentTarget = e.currentTarget as HTMLElement;
    const { scrollTop, scrollHeight, clientHeight } = currentTarget;
    // 当滚动到底部时加载更多数据
    if (scrollTop + clientHeight >= scrollHeight - 50 && !loading.value && hasMore.value) {
      loadMoreData();
    }
  }
</script>

<style scoped>

</style>
