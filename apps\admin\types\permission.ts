import type { Pagination, Paging } from "-/common";

export interface Permission {
  id?: number;
  action: string;
  resource: string;
  role: string | null;
  project: string;
}

export type PermissionExpanded = Permission & {
  key?: string;
  deleteLoading?: boolean;
};

export interface PermissionParam extends Paging {
  sort?: string;
  q?: string;
  resource?: string;
  domain_id?: string;
}

export interface UserPermissionInfo {
  uid: string;
  user_name: string;
  permissions: Permission[];
  menu: string[];
  btn: string[];
  teams: string[];
  tabs: string[];
  page: Pagination;
}

export interface UserPermissionInfo2 {
  authZ: boolean;
  syncBs: boolean;
  llm: boolean;
  assistant: boolean;
}

export type PermissionInfo = UserPermissionInfo & UserPermissionInfo2;
