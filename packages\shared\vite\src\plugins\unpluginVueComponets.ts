import type { ComponentResolver } from "unplugin-vue-components";
import type { PluginOption } from "vite";
import Components from "unplugin-vue-components/vite";

/**
 * Create Vue Components plugin configuration
 * 创建Vue Components插件配置
 * @returns Vite plugin configuration array Vite插件配置数组
 */
export function createVueComponentsPluginConfig(): PluginOption {
  return Components({
    extensions: ["vue"],
    include: [/\.vue$/, /\.vue\?vue/],
    dts: "autoResolver/components.d.ts",
    exclude: [/[\\/]node_modules[\\/]/, /[\\/]\.git[\\/]/, /[\\/]\.nuxt[\\/]/],
    resolvers: [
      CelerisAdminResolver(),
    ],
  });
}
/**
 * Resolver for CelerisAdmin
 *
 * <AUTHOR>
 */
export function CelerisAdminResolver(): ComponentResolver {
  return {
    type: "component",
    resolve: (name: string) => {
      // Resolver for Naive UI
      // @link https://www.naiveui.com/
      if (name.match(/^(N[A-Z]|n-[a-z])/)) {
        return { name, from: "@celeris/ca-components" };
      }
      if (name.match(/^(CA[A-Z]|ca-[a-z])/)) {
        return { name, from: "@celeris/components" };
      }
    },
  };
}
