<script setup lang="ts">
import { useDesignStore } from "~/store/modules/design";

defineOptions({
  name: "ThemeBackup",
});
const { t } = useI18n();
const message = useMessage();
const designStore = useDesignStore();

const dataClipboardText = ref(getSettingJson());

/*
 * 获取主题配置
 * Get theme setting
 */
function getSettingJson() {
  return JSON.stringify(designStore.$state);
}

/*
 * 重置主题配置
 * Reset theme setting
 */
function handleResetSetting() {
  designStore.resetDesignState();
  message.success(t("layouts.header.themeConfig.message.resetConfigSuccess"));
}

/*
 * 复制主题配置
 * Copy theme setting
 */
function handleCopySetting() {
  message.success(t("layouts.header.themeConfig.message.copyConfigSuccess"));
}
</script>

<template>
  <NDivider title-placement="center">
    {{ t('layouts.header.themeConfig.title') }}
  </NDivider>
  <NSpace vertical>
    <div v-copy="dataClipboardText">
      <NButton type="primary" :block="true" @click="handleCopySetting">
        {{ t('layouts.header.themeConfig.copyConfigButton') }}
      </NButton>
    </div>
    <NButton type="warning" :block="true" @click="handleResetSetting">
      {{ t('layouts.header.themeConfig.resetConfigButton') }}
    </NButton>
  </NSpace>
</template>

<style scoped></style>
