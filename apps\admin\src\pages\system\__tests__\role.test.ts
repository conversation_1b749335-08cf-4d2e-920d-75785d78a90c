import { mount } from "@vue/test-utils";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { nextTick } from "vue";
import { deleteRole, getRoleList } from "~/apis/internal/role";
import { useTable } from "~/composables/useTable";
import Index from "../Role/index.vue";

// mock 依赖
const messageMock = {
  success: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
  warning: vi.fn(),
};
vi.mock("~/apis/internal/role", () => ({
  getRoleList: vi.fn().mockResolvedValue([]),
  deleteRole: vi.fn().mockResolvedValue({}),
}));
vi.mock("../Role/components/RoleFormModal.vue", () => ({
  default: {
    name: "RoleFormModal",
    props: ["show"],
    template: "<div v-if=\"show\" class=\"mock-role-form-modal\"></div>",
  },
}));
vi.mock("../Role/components/AuthorityDrawer.vue", () => ({
  default: {
    name: "AuthorityDrawer",
    props: ["show"],
    template: "<div v-if=\"show\" class=\"mock-authority-drawer\"></div>",
  },
}));
vi.mock("@celeris/ca-components", async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useMessage: () => messageMock,
    useDialog: () => ({
      info: vi.fn().mockImplementation(({ onPositiveClick }) => {
        if (onPositiveClick) {
          onPositiveClick();
        }
      }),
    }),
  };
});
vi.mock("~/composables/useTable", () => {
  const loadDataMock = vi.fn();
  const refreshDataMock = vi.fn();
  const reloadDataMock = vi.fn();
  return {
    useTable: () => ({
      tableLoading: false,
      tableData: [
        {
          id: "1",
          name: "角色A",
          domain_id: "domain-1",
          description: "描述A",
          created_at: "2023-01-01",
          updated_at: "2023-01-02",
          key: "1",
          deleteLoading: false,
        },
      ],
      tablePagination: { page: 1, pageSize: 10, itemCount: 1 },
      loadData: loadDataMock,
      refreshData: refreshDataMock,
      reloadData: reloadDataMock,
      handlePageChange: vi.fn(),
      handlePageSizeChange: vi.fn(),
      handleScroll: vi.fn(),
    }),
  };
});

function getCurrentMessageMock() {
  return messageMock;
}

describe("role 角色管理页", () => {
  let wrapper;
  let tableApi;
  beforeEach(async () => {
    wrapper = mount(Index, {
      props: { domainId: "test-domain" },
    });
    tableApi = useTable();
    await nextTick();
  });

  it("应渲染主要结构和表格数据", async () => {
    expect(wrapper.findComponent({ name: "PageWrapper" }).exists()).toBe(true);
    expect(wrapper.findComponent({ name: "DataTable" }).exists()).toBe(true);
    expect(wrapper.text()).toContain("角色A");
    expect(wrapper.text()).toContain("描述A");
  });

  // loadTableData
  it("loadTableData 应调用 loadData 并格式化数据", () => {
    wrapper.vm.loadTableData();
    expect(tableApi.loadData).toHaveBeenCalled();
  });

  // searchConfirm
  it("searchConfirm 应调用 reloadData", () => {
    wrapper.vm.searchConfirm();
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  // searchReset
  it("searchReset 应清空筛选项并 reloadData", () => {
    wrapper.vm.searchModel.name = "xxx";
    wrapper.vm.searchReset();
    expect(wrapper.vm.searchModel.name).toBeNull();
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  // handleAdd
  it("handleAdd 应弹出新增弹窗", () => {
    wrapper.vm.roleFormModalShow = false;
    wrapper.vm.currRole = { id: "1" };
    wrapper.vm.handleAdd();
    expect(wrapper.vm.roleFormModalShow).toBe(true);
    expect(wrapper.vm.currRole).toBeNull();
  });

  // handleEdit
  it("handleEdit 应弹出编辑弹窗并设置当前角色", () => {
    const row = { id: "2", name: "角色B" };
    wrapper.vm.handleEdit(row);
    expect(wrapper.vm.roleFormModalShow).toBe(true);
    expect(wrapper.vm.currRole).toEqual(row);
  });

  // handleUpdateRole
  it("handleUpdateRole 应调用 searchConfirm", () => {
    wrapper.vm.handleUpdateRole();
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  // handleAuthority
  it("handleAuthority 应弹出权限抽屉并设置当前角色", () => {
    const row = { id: "3", name: "角色C" };
    wrapper.vm.handleAuthority(row);
    expect(wrapper.vm.currRole).toEqual(row);
    expect(wrapper.vm.authVisible).toBe(true);
  });

  // handleDelete
  it("handleDelete: 删除成功应提示并刷新", async () => {
    const row = { id: "1", name: "角色A" };
    (deleteRole as any).mockResolvedValue({});
    await wrapper.vm.handleDelete(row);
    expect(deleteRole).toHaveBeenCalled();
    expect(getCurrentMessageMock().success).toHaveBeenCalledWith("删除成功");
    expect(tableApi.refreshData).toHaveBeenCalled();
  });

  it("handleDelete: 删除失败应提示", async () => {
    const row = { id: "1", name: "角色A" };
    (deleteRole as any).mockRejectedValue(new Error("fail"));
    await wrapper.vm.handleDelete(row);
    expect(getCurrentMessageMock().error).toHaveBeenCalledWith("删除失败");
  });

  // UI交互
  it("点击新增角色按钮应弹出表单", async () => {
    expect(wrapper.find(".mock-role-form-modal").exists()).toBe(false);
    await wrapper.findAllComponents({ name: "Button" })[0].trigger("click");
    await nextTick();
    expect(wrapper.find(".mock-role-form-modal").exists()).toBe(true);
  });

  it("点击查询按钮应调用 reloadData", async () => {
    await wrapper.findAllComponents({ name: "Button" })[1].trigger("click");
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  it("点击重置按钮应清空筛选并 reloadData", async () => {
    await wrapper.findAllComponents({ name: "Button" })[2].trigger("click");
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  it("点击删除按钮应弹出确认框并调用删除接口和刷新", async () => {
    await wrapper.findAllComponents({ name: "Button" })[4].trigger("click");
    await nextTick();
    expect(deleteRole).toHaveBeenCalled();
    expect(tableApi.refreshData).toHaveBeenCalled();
  });
});
