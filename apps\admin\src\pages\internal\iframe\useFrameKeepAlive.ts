import type { RouteRecordName, RouteRecordRaw } from "vue-router";
import { uniqBy } from "@celeris/utils";
import { computed, toRaw } from "vue";
import { useRouter } from "vue-router";

/*
 * 用于保持 iframe 页面的激活状态
 * Used to keep the iframe page active
 */
export function useFrameKeepAlive() {
  const router = useRouter();
  const { currentRoute } = router;
  const getFramePages = computed(() => {
    return getAllFramePages(toRaw(router.getRoutes()));
  });

  // 获取所有 iframe 页面
  function getAllFramePages(routes: RouteRecordRaw[]): RouteRecordRaw[] {
    let framePages: RouteRecordRaw[] = [];
    for (const route of routes) {
      const { meta: { iframeLink } = {}, children } = route;
      if (iframeLink) {
        framePages.push(route);
      }
      if (children && children.length) {
        framePages.push(...getAllFramePages(children));
      }
    }
    framePages = uniqBy(framePages, "name");
    return framePages;
  }

  // 显示 iframe 页面
  function showIframe(item: RouteRecordRaw) {
    return item.name === currentRoute.value.name;
  }

  // 是否渲染 iframe 页面
  function hasRenderFrame(name: RouteRecordName) {
    return currentRoute.value.name === name;
  }

  return {
    hasRenderFrame,
    getFramePages,
    showIframe,
    getAllFramePages,
  };
}
