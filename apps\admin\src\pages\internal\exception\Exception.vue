<script setup lang="ts">
import { HttpStatusConstants, PageConstants } from "@celeris/constants";
import { checkStatus } from "@celeris/request";
import { getErrorMessage } from "@celeris/utils";
import PageWrapper from "~/component/PageWrapper/src/PageWrapper.vue";

const props = withDefaults(defineProps<exceptionProps>(), {
  status: HttpStatusConstants.NotFound,
});

enum StatusType {
  Error = "error",
  Info = "info",
  Success = "success",
  Warning = "warning",
}
interface exceptionProps {
  status?: number;
  title?: string;
  description?: string;
}
const router = useRouter();
const title = ref<string>("");
const statusType = ref<StatusType>(StatusType.Error);

/**
 * 根据状态码获取状态类型
 * @param status 状态码
 * @returns 状态类型
 */
function getStatusType(status: number): StatusType {
  if (status >= 500) {
    return StatusType.Error;
  } else if (status >= 400) {
    return StatusType.Error;
  } else if (status >= 300) {
    return StatusType.Warning;
  } else if (status >= 200) {
    return StatusType.Success;
  } else {
    return StatusType.Info;
  }
}

/**
 * 组件挂载后执行
 * 1. 获取状态类型
 * 2. 检查状态码
 * 3. 如果状态码异常，则设置标题
 */
onMounted(() => {
  try {
    statusType.value = getStatusType(props.status);
    checkStatus(props.status);
  } catch (error: any) {
    title.value = `${props.status} ${getErrorMessage(error)}`;
  }
});
</script>

<template>
  <PageWrapper>
    <NResult class="p-4" :status="statusType" :title="title" :description="description" size="large">
      <template #footer>
        <NSpace justify="center">
          <NButton type="primary" @click="router.push(PageConstants.BASE_HOME)">
            返回主页
          </NButton>
          <NButton @click="router.back()">
            返回上一级
          </NButton>
        </NSpace>
      </template>
    </NResult>
  </PageWrapper>
</template>
