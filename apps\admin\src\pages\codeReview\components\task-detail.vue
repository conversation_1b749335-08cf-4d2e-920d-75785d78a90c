<template>
  <NDrawer
    v-model:show="drawerVisible"
    placement="right"
    width="50%"
    :on-mask-click="handleClickMask"
    :mask-closable="false"
    @after-leave="handleClose"
  >
    <NDrawerContent @click="handleDrawerContentClick">
      <!-- 头部区域 -->
      <template #header>
        <div class="bg-white flex-shrink-0">
          <div class="flex justify-between items-center mb-3">
            <h2 class="text-lg font-semibold m-0">
              {{ detailData?.title || '暂无标题' }}
            </h2>
          </div>
          <div class="flex items-center text-sm text-gray-600 mt-1">
            <span class="mr-3 flex items-center gap-2">
              <CAIcon icon="tabler:user" class="w-6 h-6 flex-shrink-0" />
              <strong>{{ detailData?.author?.name || '未知用户' }}</strong> 请求将
              <NTag type="warning" size="small">{{ detailData?.source_branch || '未知分支' }}</NTag>
              合并到
              <NTag type="success" size="small">{{ detailData?.target_branch || '未知分支' }}</NTag>
              <span class="text-xs text-gray-500 ml-auto">{{ formatToDateTime(detailData?.created_at) }}</span>
            </span>
          </div>
        </div>
      </template>
      <NSpin :show="dataLoading">
        <div v-if="detailData?.project_id" class="flex flex-col h-full overflow-hidden relative">
          <!-- Tab区域 -->
          <div class="flex-1 flex flex-col overflow-hidden bg-white border-b border-gray-200 transition-[padding-bottom] duration-300 ease-in-out" :style="{ paddingBottom: `${commentheight + 120}px` }">
            <div class="flex-1 flex flex-col overflow-hidden">
              <!-- Tab 头部固定 -->
              <div class="flex-shrink-0 bg-white border-b border-gray-200">
                <NTabs v-model:value="activeTab" type="line" @update:value="handleTabClick">
                  <NTab name="comment">
                    评论
                    <NBadge v-if="note_count > 0" :value="note_count" :max="99" class="ml-2" />
                  </NTab>
                  <NTab name="overview">
                    概览
                  </NTab>
                  <NTab name="commits">
                    提交 {{ commit_count }}
                  </NTab>
                  <NTab name="changes">
                    变更 {{ diff_count }}
                  </NTab>
                </NTabs>
              </div>

              <!-- Tab 内容区域 - 可滚动 -->
              <div class="flex-1 overflow-y-auto p-1">
                <div v-if="activeTab === 'comment'">
                  <div v-if="note_count > 0">
                    <div v-for="(comment, index) in notes" :key="index" class="bg-white rounded-xl p-0 mb-5 shadow-lg transition-all duration-300 ease-in-out border border-gray-100 hover:-translate-y-1 hover:shadow-xl last:mb-3">
                      <div class="flex items-center px-4 py-3 rounded-t-xl bg-gray-50 mb-0 border-b border-gray-200">
                        <span class="font-semibold text-gray-800 text-base">{{ comment.author.name }}</span>
                        <span class="text-gray-500 ml-3 text-xs">- {{ formatToDateTime(comment.created_at) }}</span>
                        <span class="mx-2 text-gray-300">·</span>
                        <NButton text type="primary" @click="handleEditComment(comment)">
                          编辑
                        </NButton>
                        <span class="mx-2 text-gray-300">·</span>
                        <NButton text type="error" @click="handleDeleteComment(comment)">
                          删除
                        </NButton>
                      </div>
                      <MdPreview class="text-gray-700 leading-relaxed rounded-b-xl mb-3 text-sm break-words p-2" :text="comment.body"></MdPreview>
                    </div>
                  </div>
                  <NEmpty v-else description="暂无评论" />
                </div>
                <div v-else-if="activeTab === 'overview'">
                  <!-- 概览内容 -->
                </div>
                <div v-else-if="activeTab === 'commits'">
                  <!-- 提交内容 -->
                </div>
                <div v-else-if="activeTab === 'changes'">
                  <!-- 变更内容 -->
                </div>
              </div>
            </div>
          </div>
        </div>
        <NEmpty v-else description="暂无数据" />
      </NSpin>
      <template #footer>
        <!-- 动态/评论区 -->
        <div class="absolute bottom-5 left-0 right-0 bg-white flex-shrink-0 p-2.5 border-t border-gray-200 transition-[height] duration-300 ease-in-out z-10" :style="{ height: `${commentheight + 90}px` }" @click.stop>
          <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 p-0 z-10">
            <div class="w-8 h-8 border border-gray-300 rounded-full bg-white flex items-center justify-center">
              <NButton
                text
                class="w-full h-full flex items-center justify-center"
                @click="toggleActivitySection"
              >
                <template #icon>
                  <CAIcon
                    icon="tabler:chevron-down"
                    :class="{ 'rotate-180': isActivityCollapsed }"
                  />
                </template>
              </NButton>
            </div>
          </div>
          <div class="flex justify-between items-center mb-1 pt-1">
            <h3>评论</h3>
          </div>

          <div class="mb-3">
            <MdEditor
              ref="mdEditorRef"
              v-model:content="newComment"
              placeholder="请输入评论内容..."
              :height="commentheight"
              @on-focus="handleEditorFocus"
            />
          </div>
          <div class="flex justify-start items-center gap-3">
            <NButton type="primary" :loading="btnLoading" :disabled="!!currentEventSourceDisconnect" @click="handleAIReview">
              大模型Review
            </NButton>
            <NButton :loading="btnLoading" @click="addComment">
              发表评论
            </NButton>
            <NButton @click="handleCancelEdit">
              取消
            </NButton>
          </div>
        </div>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<script setup lang="ts">
  import type {
    AddCommentParams,
    CodeReviewDetail,
    CodeReviewItem,
    CodeReviewNote,
    DeleteCommentParams,
  } from "-/codeReview";
  import { CAIcon } from "@celeris/components";
  import { formatToDateTime } from "@celeris/utils";
  import { addComment as addCommentApi, deleteComment, getCodeReviewDetail } from "~/apis/internal/codeReview";
  import { createModelEventSource } from "~/utils/eventSource";

  defineOptions({
    name: "TaskDetailDrawer",
  });

  // 响应式数据
  const drawerVisible = ref<boolean>(false);
  const detailData = ref<CodeReviewItem | null>();
  const activeTab = ref<string>("comment");
  const newComment = ref<string>("");
  const btnLoading = ref<boolean>(false);
  const editComment = ref<CodeReviewNote | null>(null);
  const isEditComment = ref<boolean>(false);
  const commit_count = ref<number>(0);
  const diff_count = ref<number>(0);
  const note_count = ref<number>(0);
  const notes = ref<CodeReviewNote[]>([]);
  const dataLoading = ref<boolean>(false);
  const commentheight = ref<number>(180);
  const mdEditorRef = ref();
  const currentEventSourceDisconnect = ref<(() => void) | null>(null);

  // 消息和对话框
  const message = useMessage();
  const dialog = useDialog();

  // 防抖定时器
  let heightChangeTimer: NodeJS.Timeout | null = null;

  // 计算属性
  const isActivityCollapsed = computed(() => commentheight.value !== 450);

  // 防抖处理高度变化
  function setEditorHeight(height: number) {
    if (heightChangeTimer) {
      clearTimeout(heightChangeTimer);
    }
    heightChangeTimer = setTimeout(() => {
      commentheight.value = height;
    }, 200); // 200ms 的延迟
  }

  // 方法
  function toggleActivitySection() {
    if (commentheight.value === 450) {
      setEditorHeight(180);
    } else {
      setEditorHeight(450);
      // 注意：在 Composition API 中需要通过 ref 访问组件实例
      mdEditorRef.value?.handleFocus();
    }
  }
  function show(item: CodeReviewItem) {
    getDetailData(item);
    drawerVisible.value = true;
    newComment.value = ""; // 每次打开抽屉时清空评论区
  }
  // 获取mr详情
  async function getDetailData(item: CodeReviewItem) {
    const params = {
      projectId: item.project_id,
      iid: item.iid,
    };

    detailData.value = {
      ...item,
      author: item.author || { name: "" },
    } as CodeReviewItem;

    dataLoading.value = true;

    try {
      const res = await getCodeReviewDetail(params);
      detailData.value = res.data;
      commit_count.value = res.commit_count || 0;
      diff_count.value = res.diff_count || 0;
      note_count.value = res.note_count || 0;
      notes.value = res.notes || [];
    } finally {
      dataLoading.value = false;
    }
  }

  // 点击遮罩层
  function handleClickMask(): Promise<boolean> {
    return new Promise((resolve) => {
      // 如果正在加载或评论不为空，需要确认
      if (btnLoading.value || newComment.value.trim()) {
        dialog.warning({
          title: "确认关闭",
          content: "关闭后生成的评论将丢失，是否继续？",
          positiveText: "确定",
          negativeText: "取消",
          onPositiveClick: () => {
            btnLoading.value = false;
            drawerVisible.value = false;
          },
          onNegativeClick: () => {
            resolve(false);
          },
        });
      } else {
        drawerVisible.value = false;
      }
    });
  }

  // 关闭
  function handleClose() {
    detailData.value = null; // 清理数据
    newComment.value = ""; // 清空评论区
    activeTab.value = "comment";
    note_count.value = 0;
    notes.value = [];
    diff_count.value = 0;
    commit_count.value = 0;
    isEditComment.value = false;
    editComment.value = null;
    setEditorHeight(180);
    if (currentEventSourceDisconnect.value) {
      currentEventSourceDisconnect.value();
      currentEventSourceDisconnect.value = null;
    }
    if (heightChangeTimer) {
      clearTimeout(heightChangeTimer);
      heightChangeTimer = null;
    }
  }
  // 添加评论
  async function addComment() {
    if (!newComment.value || !newComment.value.trim()) {
      message.warning("评论内容不能为空");
      return;
    }

    if (!detailData.value?.project_id || !detailData.value?.iid) {
      message.error("数据异常");
      return;
    }

    const params: AddCommentParams = {
      projectId: detailData.value.project_id,
      iid: detailData.value.iid,
      content: newComment.value,
    };

    const successMsg = isEditComment.value ? "评论已更新" : "评论已添加";

    if (isEditComment.value && editComment.value) {
      params.noteId = editComment.value.id;
      params.content = truncateComment(editComment.value.body, true) + newComment.value;
    }

    btnLoading.value = true;

    try {
      await addCommentApi(params);
      message.success(successMsg);
      setEditorHeight(180);
      activeTab.value = "comment";
      await getDetailData(detailData.value);
      handleCancelEdit();
      newComment.value = ""; // 清空输入框
    } catch {
      message.error("操作失败");
    } finally {
      btnLoading.value = false;
    }
  }

  // AI Review 功能
  async function handleAIReview() {
    if (!detailData.value) {
      message.error("数据异常");
      return;
    }

    newComment.value = "";
    mdEditorRef.value?.handleFocus();
    btnLoading.value = true;

    const url = `api/v1/merge_requests/cr/review?projectId=${detailData.value.project_id}&iid=${detailData.value.iid}`;

    // 使用封装的大模型 EventSource 连接方法
    const eventSourceConnection = createModelEventSource(url, {
      onContent: (content) => {
        // 处理接收到的内容
        newComment.value += content;
      },
      onError: (_error) => {
        // 处理错误
        btnLoading.value = false;
        currentEventSourceDisconnect.value = null;
        console.error("error event", _error);
        message.error("连接失败，请稍后重试");
      },
      onFinish: () => {
        // 连接完成
        btnLoading.value = false;
        currentEventSourceDisconnect.value = null;
      },
    });

    // 存储断开连接的方法，以便可以手动中止
    currentEventSourceDisconnect.value = () => {
      eventSourceConnection.disconnect();
      btnLoading.value = false;
      currentEventSourceDisconnect.value = null;
    };

    // 开始连接
    eventSourceConnection.connect();
  }

  // 手动中止 AI Review
  function handleStopAIReview() {
    if (currentEventSourceDisconnect.value) {
      currentEventSourceDisconnect.value();
      currentEventSourceDisconnect.value = null;
      btnLoading.value = false;
      message.info("已中止 AI Review");
    }
  }

  function handleTabClick(tabName: string) {
    // 如果是评论标签，不进行跳转
    if (tabName === "comment") {
      return;
    }

    // 阻止默认的标签页切换
    nextTick(() => {
      activeTab.value = "comment";
    });

    if (!detailData.value?.web_url) {
      return;
    }

    // 根据不同的 tab 跳转到不同的页面
    const routeMap = {
      overview: detailData.value.web_url,
      commits: `${detailData.value.web_url}/commits`,
      changes: `${detailData.value.web_url}/diffs`,
    };

    // 在新窗口打开对应页面
    const url = routeMap[tabName as keyof typeof routeMap];
    if (url) {
      window.open(url, "_blank");
    }
  }

  // 焦点事件
  function handleEditorFocus() {
    // 当编辑器获得焦点时，将评论区高度调整
    setEditorHeight(450);
  }

  // 点击抽屉内容区域时，收起评论区
  function handleDrawerContentClick() {
    // 如果评论区是展开状态且没有内容，则收起
    if (commentheight.value === 450) {
      setEditorHeight(180);
    }
  }

  // 编辑评论
  function handleEditComment(comment: CodeReviewNote) {
    isEditComment.value = true;
    editComment.value = comment;
    newComment.value = truncateComment(comment.body);
    setEditorHeight(450);
  }

  // 截取评论内容
  function truncateComment(comment: string, isReview = false): string {
    // 获取-- ---前或后的字符串
    const index = comment.indexOf("-- ---");
    if (index !== -1) {
      return isReview
        ? comment.substring(0, index + 6)
        : comment.substring(index + 6);
    }
    return comment;
  }

  // 取消编辑
  function handleCancelEdit() {
    isEditComment.value = false;
    editComment.value = null;
    newComment.value = "";
    handleStopAIReview();
    setEditorHeight(180);
  }

  // 删除评论
  function handleDeleteComment(comment: CodeReviewNote) {
    if (!detailData.value?.project_id || !detailData.value?.iid) {
      message.error("数据异常");
      return;
    }

    dialog.warning({
      title: "确认删除",
      content: "确认删除该评论吗？",
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          const params: DeleteCommentParams = {
            projectId: detailData.value!.project_id,
            iid: detailData.value!.iid,
            noteId: comment.id,
          };

          await deleteComment(params);
          message.success("评论已删除");
          await getDetailData(detailData.value!);
        } catch {
          message.error("删除失败");
        }
      },
    });
  }

  // 暴露方法给父组件
  defineExpose({
    show,
  });
</script>
