import type { App } from "vue";
import anchor from "./src/anchor";
import copy from "./src/copy";
import ripple from "./src/ripple";

export const AllDirectives = {
  // Custom directives
  copy,
  ripple,
  anchor,
};

/**
 * 注册所有指令
 * Register all directives
 * @param app - Vue应用实例
 * Vue application instance
 */
export function setupDirectives(app: App) {
  Object.keys(AllDirectives).forEach((key) => {
    app.directive(key, AllDirectives[key]);
  });
}
