<template>
  <NModal
    v-model:show="visible"
    preset="dialog"
    class="w-xl"
    title="覆盖机器"
    style="width: 650px; position: fixed; top: 100px; left: 50%; transform: translateX(-50%); background: #fff;"
    positive-text="确定"
    @positive-click="handleClose"
    @after-leave="handleClose"
  >
    <NDivider />
    <NForm :model="machQuery" inline :show-label="false">
      <NFormItem>
        <NInput v-model:value="machQuery.ip" placeholder="请输入IP" clearable />
      </NFormItem>
      <NFormItem>
        <NButton type="primary" @click="handleSearch">
          查询
        </NButton>
      </NFormItem>
    </NForm>
    <NDataTable :loading="loading" :data="tableData" :columns="columns" :max-height="300" />
    <NDivider />
  </NModal>
</template>

<script setup lang="ts">
  import { getCoverageHostApi } from "~/apis/internal/user";

  const props = defineProps({
    show: <PERSON><PERSON><PERSON>,
    uid: {
      type: Number,
      required: true,
    },
  });
  const emit = defineEmits<{
    "update:show": [show: boolean];
  }>();

  // 内部状态
  const loading = ref(false);
  const tableData = ref<{ ip: string; comment: string }[]>([]);
  const machQuery = reactive<{ ip: string }>({ ip: "" });
  const columns = [
    { title: "IP地址", key: "ip" },
    { title: "备注", key: "comment" },
  ];
  const visible = ref(props.show);

  /**
   * @description 监听弹窗显示状态，弹窗打开时自动请求覆盖机器列表，关闭时重置数据。
   * @param val 弹窗显示状态
   */
  watch(
    () => props.show,
    async (val) => {
      visible.value = val;
      if (val && props.uid) {
        await fetchMachList();
      } else if (!val) {
        tableData.value = [];
        machQuery.ip = "";
      }
    },
  );

  /**
   * @description 查询机器列表，带当前 IP 查询条件。
   * @returns {Promise<void>}
   */
  async function handleSearch(): Promise<void> {
    await fetchMachList();
  }

  /**
   * @description 拉取覆盖机器列表数据。
   * @returns {Promise<void>}
   */
  async function fetchMachList(): Promise<void> {
    loading.value = true;
    try {
      const res = await getCoverageHostApi(props.uid, { q: `search=${machQuery.ip}` });
      tableData.value = res.data || [];
    } catch (e) {
      tableData.value = [];
    } finally {
      loading.value = false;
    }
  }

  /**
   * @description 关闭弹窗，重置显示状态。
   * @returns {void}
   */
  function handleClose(): void {
    emit("update:show", false);
  }
</script>
