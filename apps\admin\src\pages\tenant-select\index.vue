<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
    <div class="w-full max-w-2xl">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          选择租户
        </h1>
        <p class="text-gray-600 dark:text-gray-300">
          请选择您要进入的租户环境
        </p>
      </div>

      <!-- 租户列表 -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 flex flex-col max-h-[70vh] min-h-[400px] scroll-container">
        <div v-if="loading" class="flex justify-center items-center py-12">
          <NSpin size="large" />
        </div>

        <div v-else-if="tenantList.length === 0" class="text-center py-12">
          <div class="text-gray-500 dark:text-gray-400 mb-4">
            <CAIcon icon="tabler:building-off" class="text-6xl mx-auto mb-4" />
            <p class="text-lg">
              暂无可用租户
            </p>
            <p class="text-sm">
              请联系管理员为您分配租户权限
            </p>
          </div>
        </div>

        <!-- 租户列表容器 - 可滚动区域 -->
        <div v-else class="flex-1 overflow-y-auto pr-2 -mr-2">
          <div class="space-y-3 pb-4 pt-2">
            <div
              v-for="tenant in tenantList"
              :key="tenant.id"
              class="group relative p-4 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-blue-500 hover:shadow-md transition-all duration-200 cursor-pointer"
              :class="{
                'border-blue-500 bg-blue-50 dark:bg-blue-900/20': selectedTenant?.id === tenant.id,
                'opacity-50 cursor-not-allowed': tenant.state === 0,
              }"
              @click="handleTenantSelect(tenant)"
            >
              <div class="m flex items-center justify-between">
                <div class="flex items-center space-x-4 flex-1 min-w-0">
                  <!-- 租户图标 -->
                  <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                      <CAIcon icon="tabler:building" class="text-white text-xl" />
                    </div>
                  </div>

                  <!-- 租户信息 -->
                  <div class="flex-1 min-w-0">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                      {{ tenant.name }}
                    </h3>
                    <div class="flex items-center space-x-4 mt-1">
                      <span class="text-sm text-gray-500 dark:text-gray-400" :title="tenant?.admin_name && tenant.admin_name?.length > 2 ? tenant?.admin_name?.join(',') : ''">
                        管理员: {{
                          !tenant.admin_name || !Array.isArray(tenant.admin_name) || tenant.admin_name.length === 0
                            ? '未设置'
                            : tenant.admin_name.length === 1
                              ? tenant.admin_name[0]
                              : `${tenant.admin_name.slice(0, 2).join('、')}${tenant.admin_name.length > 2 ? '等' : ''}`
                        }}
                      </span>
                      <NTag
                        :type="tenant.state === 1 ? 'success' : 'warning'"
                        size="small"
                      >
                        {{ tenant.state === 1 ? '已激活' : '未激活' }}
                      </NTag>
                    </div>
                  </div>
                </div>

                <!-- 选中状态 -->
                <div class="flex-shrink-0">
                  <div
                    v-if="selectedTenant?.id === tenant.id"
                    class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"
                  >
                    <CAIcon icon="tabler:check" class="text-white text-sm" />
                  </div>
                  <div
                    v-else
                    class="w-6 h-6 border-2 border-gray-300 dark:border-gray-600 rounded-full group-hover:border-blue-500 transition-colors"
                  />
                </div>
              </div>

              <!-- 默认租户标识 -->
              <div
                v-if="tenant.is_default"
                class="absolute -top-2 -right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full z-10"
              >
                默认
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-between items-center mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
          <NButton
            text
            type="primary"
            @click="handleLogout"
          >
            <template #icon>
              <CAIcon icon="tabler:logout" />
            </template>
            退出登录
          </NButton>

          <NButton
            type="primary"
            size="large"
            :disabled="!selectedTenant || selectedTenant.state === 0"
            :loading="entering"
            @click="handleEnterTenant"
          >
            <template #icon>
              <CAIcon icon="tabler:arrow-right" />
            </template>
            进入租户
          </NButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import type { TenantInfo } from "-/tenant";
  import { CAIcon } from "@celeris/components";
  import { PageConstants } from "@celeris/constants";
  import { NButton, NSpin, NTag, useMessage } from "naive-ui";
  import { useUserStore } from "~/store/modules/user";

  defineOptions({
    name: "TenantSelect",
  });

  const router = useRouter();
  const message = useMessage();
  const userStore = useUserStore();

  // 响应式数据
  const loading = ref(true);
  const entering = ref(false);
  const tenantList = ref<TenantInfo[]>([]);
  const selectedTenant = ref<TenantInfo | null>(null);

  // 生命周期
  onMounted(async () => {
    // 检查是否已经有当前租户，如果有则直接跳转到首页
    const currentTenant = userStore.getCurrentTenant;
    if (currentTenant) {
      router.replace(PageConstants.BASE_HOME);
      return;
    }

    await loadTenantList();
  });

  /**
   * 加载租户列表
   */
  async function loadTenantList() {
    try {
      loading.value = true;
      const tenants = await userStore.fetchAvailableTenants(true, true);
      tenantList.value = tenants;
      // 选择租户的优先级：当前租户 > 默认租户 > 第一个租户（如果只有一个）
      const currentTenant = userStore.getCurrentTenant;

      if (currentTenant && tenants.find(t => t.id === currentTenant.id)) {
        // 如果当前租户在可用租户列表中，选中它
        selectedTenant.value = currentTenant;
      } else if (tenants.length === 1) {
        // 如果只有一个租户，自动选中
        selectedTenant.value = tenants[0];
      } else {
        // 优先选中默认租户
        const defaultTenant = tenants.find(t => t.is_default);
        if (defaultTenant) {
          selectedTenant.value = defaultTenant;
        }
      }
      if (selectedTenant.value) {
        handleEnterTenant();
      }
    } catch (error) {
      console.error("加载租户列表失败:", error);
      message.error("加载租户列表失败，请重试");
    } finally {
      loading.value = false;
    }
  }

  /**
   * 选择租户
   */
  function handleTenantSelect(tenant: TenantInfo) {
    if (tenant.state === 0) {
      message.warning("该租户未激活，无法进入");
      return;
    }
    selectedTenant.value = tenant;
  }

  /**
   * 进入租户
   */
  async function handleEnterTenant() {
    if (!selectedTenant.value) {
      message.warning("请选择一个租户");
      return;
    }

    if (selectedTenant.value.state === 0) {
      message.warning("该租户未激活，无法进入");
      return;
    }

    try {
      entering.value = true;

      // 切换到选中的租户
      await userStore.switchTenant(selectedTenant.value.id!);

      // message.success(`已进入租户：${selectedTenant.value.name}`);

      // 跳转到首页
      router.replace(PageConstants.BASE_HOME);
    } catch (error) {
      console.error("进入租户失败:", error);
      message.error("进入租户失败，请重试");
    } finally {
      entering.value = false;
    }
  }

  /**
   * 退出登录
   */
  async function handleLogout() {
    try {
      await userStore.logout();
    } catch (error) {
      console.error("退出登录失败:", error);
      // 即使退出失败也要跳转到登录页
      router.replace(PageConstants.BASE_LOGIN);
    }
  }
</script>

<style scoped>
/* 自定义样式 */
.group:hover .group-hover\:border-blue-500 {
  border-color: #3b82f6;
}

/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 深色模式下的滚动条 */
.dark .overflow-y-auto::-webkit-scrollbar-track {
  background: #374151;
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 滚动区域渐变遮罩效果 - 固定在容器底部 */
.scroll-container {
  position: relative;
}

.scroll-container::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.8));
  pointer-events: none;
  border-radius: 0 0 12px 12px;
  z-index: 10;
}

.dark .scroll-container::after {
  background: linear-gradient(to bottom, transparent, rgba(31, 41, 55, 0.8));
}
</style>
