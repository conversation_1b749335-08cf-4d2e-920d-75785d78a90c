###
 # @Author: zy
 # @Date: 2024-12-31 14:49:11
 # @LastEditors: zy
 # @LastEditTime: 2025-03-11 18:38:09
 # @Description:
###
# Whether to use mock data (true/false)
# VITE_USE_MOCK=false

# Whether to enable build analyzer (true/false)
VITE_USE_BUILD_ANALYZER=false

# Whether to enable PWA (true/false)
VITE_USE_PWA=false

# The public path for assets in your application
VITE_PUBLIC_PATH=/

# Proxy settings for your development server (array of [string, string] pairs)
VITE_PROXY=[["/api", "https://done-dev.ctcdn.cn/"], ["/openapi", "https://done-dev.ctcdn.cn/"]]

VITE_APP_BASE_DOMAIN_ID = 'done'

VITE_APP_END_SESSION_ENDPOINT = 'https://devops-dev.ctcdn.cn/one/sso/oauth2/sessions/logout'

# Basic interface address SPA
VITE_GLOB_API_URL=

# Basic interface address SSR
VITE_GLOB_API_URL_SSR=

# Basic interface address prefix
VITE_GLOB_API_URL_PREFIX= '/api'

VITE_LOGOUT_URL= 'https://devops-dev.ctcdn.cn/one/sso/oauth2/sessions/logout'

# The title of your application (string)
VITE_GLOB_APP_TITLE="息壤翼效平台"

# The short name of your application (string)
VITE_GLOB_APP_SHORT_NAME="Done"

# Whether to use a CDN for assets (true/false)
VITE_USE_CDN=false

# Whether to drop console.log statements (true/false)
VITE_DROP_CONSOLE=true

# Whether to use HTTPS for your development server (true/false)
VITE_USE_HTTPS=false

# The compression algorithm to use when building your application (gzip/brotli/none)
VITE_BUILD_COMPRESS=gzip

# Whether to delete the original file after compressing it (true/false)
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE=false

# Whether to build your application in legacy mode (true/false)
VITE_LEGACY=true

# Whether to use imagemin to optimize images during build (true/false)
VITE_USE_IMAGEMIN=true

# Whether to generate a configuration file (true/false)
VITE_GLOB_BUILD_GENERATE_CONFIG=true

# EE助手地址
VITE_CHAT_URL='https://galaxy-dev.ctcdn.cn/'
#VITE_CHAT_URL='http://localhost:3000/'

#iconify图标代理API
VITE_ICONIFY_URL='https://devops.ctcdn.cn/iconify'