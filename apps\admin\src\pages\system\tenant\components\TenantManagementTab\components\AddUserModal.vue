<template>
  <NModal
    v-model:show="showModal"
    :mask-closable="false"
    preset="card"
    class="w-[800px]"
    title="添加用户"
    :bordered="false"
    size="large"
  >
    <!-- 搜索区域 -->
    <div class="mb-4">
      <NInput
        v-model:value="searchKeyword"
        placeholder="请输入用户账号或姓名搜索"
        clearable
        @input="handleSearch"
      >
        <template #prefix>
          <CAIcon icon="tabler:search" />
        </template>
      </NInput>
    </div>

    <!-- 用户表格 -->
    <NDataTable
      v-model:checked-row-keys="checkedRowKeys"
      :loading="tableLoading"
      :data="tableData"
      :columns="tableColumns"
      :pagination="tablePagination"
      :row-key="rowKey"
      :max-height="350"
      striped
      @update:checked-row-keys="handleCheckedRowKeysChange"
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
    >
      <template #empty>
        <div class="py-8 text-center text-gray-500">
          <CAIcon icon="tabler:users" class="text-4xl mb-2" />
          <div>{{ searchKeyword ? '未找到匹配的用户' : '请输入关键词搜索用户' }}</div>
        </div>
      </template>
    </NDataTable>

    <template #footer>
      <NFlex justify="space-between" align="center">
        <div class="text-sm text-gray-500 max-w-md">
          <span v-if="checkedRowKeys.length > 0" class="flex items-center gap-1">
            <span>已选择 {{ checkedRowKeys.length }} 个用户：</span>
            <NTooltip v-if="shouldShowTooltip" :show-arrow="false">
              <template #trigger>
                <span class="font-medium text-blue-600 cursor-help">{{ selectedUserNames }}</span>
              </template>
              <div class="max-w-xs">{{ fullSelectedUserNames }}</div>
            </NTooltip>
            <span v-else class="font-medium text-blue-600">{{ selectedUserNames }}</span>
          </span>
          <span v-else>
            请选择要添加的用户
          </span>
        </div>
        <NFlex>
          <NButton @click="handleCancel">
            取消
          </NButton>
          <NButton
            type="primary"
            :loading="confirmLoading"
            :disabled="checkedRowKeys.length === 0"
            @click="handleConfirm"
          >
            {{ checkedRowKeys.length > 0 ? `确定添加 (${checkedRowKeys.length})` : '确定添加' }}
          </NButton>
        </NFlex>
      </NFlex>
    </template>
  </NModal>
</template>

<script setup lang="ts">
  import type { UserListItem } from "-/user";
  import type { DataTableColumns } from "naive-ui";
  import { CAIcon } from "@celeris/components";
  import { useDebounceFn } from "@vueuse/core";
  import { NButton, NDataTable, NFlex, NInput, NModal, NTag, NTooltip } from "naive-ui";
  import { h } from "vue";
  import { userListApi } from "~/apis/internal/user";
  import { useTable } from "~/composables/useTable";

  defineOptions({
    name: "AddUserModal",
  });

  const props = withDefaults(defineProps<Props>(), {
    show: false,
    tenantId: null,
    excludeUsers: () => [],
  });

  const emit = defineEmits<Emits>();

  interface Props {
    show: boolean;
    tenantId?: number | null;
    excludeUsers?: string[]; // 排除已存在的用户
  }

  interface Emits {
    (e: "update:show", value: boolean): void;
    (e: "confirm", users: UserListItem[]): void;
  }

  // 响应式数据
  const showModal = computed({
    get: () => props.show,
    set: value => emit("update:show", value),
  });

  const searchKeyword = ref("");
  const checkedRowKeys = ref<string[]>([]);
  const confirmLoading = ref(false);

  // 使用 useTable 钩子
  const {
    tableLoading,
    tableData,
    tablePagination,
    loadData,
    handlePageChange,
    handlePageSizeChange,
  } = useTable<{ q: string }, UserListItem, UserListItem>();

  // 计算选中用户的名称列表
  const selectedUserNames = computed(() => {
    const selectedUsers = tableData.value.filter(user =>
      checkedRowKeys.value.includes(user.username),
    );
    const names = selectedUsers.map(user => user.nickname || user.username);

    // 如果名称太多，只显示前几个，然后用省略号
    if (names.length <= 3) {
      return names.join("、");
    } else {
      return `${names.slice(0, 3).join("、")}等`;
    }
  });

  // 计算完整的用户名称列表（用于 tooltip）
  const fullSelectedUserNames = computed(() => {
    const selectedUsers = tableData.value.filter(user =>
      checkedRowKeys.value.includes(user.username),
    );
    return selectedUsers.map(user => user.nickname || user.username).join("、");
  });

  // 判断是否需要显示 tooltip
  const shouldShowTooltip = computed(() => {
    return checkedRowKeys.value.length > 3;
  });

  // 表格配置
  const rowKey = (row: UserListItem) => row.username;

  const tableColumns: DataTableColumns<UserListItem> = [
    {
      type: "selection",
      // disabled: (row: UserListItem) => props.excludeUsers.includes(row.username),
    },
    {
      title: "账号",
      key: "username",
      width: 120,
    },
    {
      title: "姓名",
      key: "nickname",
      width: 120,
    },
    {
      title: "邮箱",
      key: "email",
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "手机号",
      key: "telephone",
      width: 120,
    },
    {
      title: "状态",
      key: "state",
      width: 80,
      render(row) {
        return h(NTag, {
          type: !row.state ? "success" : "error",
          size: "small",
        }, {
          default: () => !row.state ? "正常" : "禁用",
        });
      },
    },
  ];

  // 搜索处理
  const handleSearch = useDebounceFn(async () => {
    await loadTableData();
  }, 300);

  // 加载表格数据
  async function loadTableData() {
    await loadData({
      getQueryCriteria: () => {
        const criteria: any = {
          domain_id: "one",
        };
        if (searchKeyword.value) {
          criteria.q = `nickname=~${searchKeyword.value}`;
        }
        return criteria;
      },
      withPagination: true,
      dataPath: "data.value",
      totalPath: "data.total",
      tableRequest: userListApi as any,
      handleTableData: (data: UserListItem[]) => {
        return data.map((item: UserListItem) => ({
          ...item,
          key: item.username,
        })) as UserListItem[];
      },
    });
  }

  // 选中行变化处理
  function handleCheckedRowKeysChange(keys: string[]) {
    checkedRowKeys.value = keys;
  }

  // 取消操作
  function handleCancel() {
    showModal.value = false;
    checkedRowKeys.value = [];
    searchKeyword.value = "";
  }

  // 确认添加
  function handleConfirm() {
    const selectedUsers = tableData.value.filter(user =>
      checkedRowKeys.value.includes(user.username),
    );
    emit("confirm", selectedUsers);
  }

  // 监听弹窗显示状态
  watch(() => props.show, (newShow) => {
    if (newShow) {
      loadTableData();
    } else {
      checkedRowKeys.value = [];
      searchKeyword.value = "";
    }
  });
</script>

<style scoped>
:deep(.n-data-table-th) {
  background-color: var(--n-th-color);
}

:deep(.n-data-table-td) {
  border-bottom: 1px solid var(--n-border-color);
}
</style>
