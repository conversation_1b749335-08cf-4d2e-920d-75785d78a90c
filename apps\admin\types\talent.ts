export interface CommonDescriptContent {
  key: string;
  value: string;
}
export interface CommonDescriptData {
  type: string;
  title: string;
  titleDesc: string;
  extra: string;
  median: string;
  content: CommonDescriptContent[];
}

export interface HistoricalPerformanceEvaluationInfo {
  id: string;
  activity_id: string;
  team_name: string;
  production_line_name: string;
  team_id: string;
  job_level: string;
  department: string;
  plan_a?: number;
  plan_b_plus?: number;
  plan_b?: number;
  plan_cd?: number;
  actual_a?: number;
  actual_b_plus?: number;
  actual_b?: number;
  actual_cd?: number;
}

export type HistoricalPerformanceEvaluationExpandedInfo = HistoricalPerformanceEvaluationInfo & {
  key: string;
};
