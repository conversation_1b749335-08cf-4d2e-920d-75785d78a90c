<template>
  <CardModal
    v-bind="$attrs"
    class="w-[700px] max-w-[60vw]"
    :empty-func="() => false"
    :mask-closable="true"
    @update:show="handleHide"
    @after-enter="handleAfterEnter"
  >
    <template #header>
      <div class="flex items-center">
        <h4 class="text-lg font-semibold text-gray-800 dark:text-gray-100 mr-6">
          个人信息
        </h4>
      </div>
    </template>
    <div class="p-6">
      <div class="space-y-6">
        <div class="grid grid-cols-1 gap-4">
          <div class="flex items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full mr-4">
              <svg class="text-blue-600 dark:text-blue-400 i-line-md:account" />
            </div>
            <div class="flex-1 flex items-center justify-between">
              <span class="text-sm text-gray-500 dark:text-gray-400">账号</span>
              <span class="text-base font-medium text-gray-900 dark:text-gray-100">
                {{ userInfo?.username || '-' }}
              </span>
            </div>
          </div>

          <div class="flex items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="flex items-center justify-center w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full mr-4">
              <svg class="text-green-600 dark:text-green-400 i-line-md:person" />
            </div>
            <div class="flex-1 flex items-center justify-between">
              <span class="text-sm text-gray-500 dark:text-gray-400">姓名</span>
              <span class="text-base font-medium text-gray-900 dark:text-gray-100">
                {{ userInfo?.nickname || '-' }}
              </span>
            </div>
          </div>

          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="flex items-center p-4">
              <div class="flex items-center justify-center w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full mr-4">
                <svg class="text-purple-600 dark:text-purple-400 i-line-md:email" />
              </div>
              <div class="flex-1 flex items-center justify-between">
                <span class="text-sm text-gray-58907890700 dark:text-gray-400">邮箱</span>
                <span class="text-base font-medium text-gray-900 dark:text-gray-100">
                  {{ userInfo?.email || '-' }}
                </span>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="flex items-center p-4">
              <div class="flex items-center justify-center w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-full mr-4">
                <svg class="text-orange-600 dark:text-orange-400 i-line-md:phone" />
              </div>
              <div class="flex-1 flex items-center justify-between">
                <span class="text-sm text-gray-500 dark:text-gray-400">手机号</span>
                <span class="text-base font-medium text-gray-900 dark:text-gray-100">
                  {{ userInfo?.telephone || '-' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <NFlex justify="center">
        <NButton type="primary" @click="handleHide">
          关闭
        </NButton>
      </NFlex>
    </template>
  </CardModal>
</template>

<script lang="ts" setup>
  import type { UserInfo } from "-/user";
  import CardModal from "~/component/CardModal/src/CardModal.vue";
  import { useUserStore } from "~/store/modules/user";

  defineOptions({
    name: "UserInfoModal",
    inheritAttrs: false,
  });

  const emit = defineEmits<{
    "update:show": [show: boolean];
  }>();

  const userStore = useUserStore();
  const { getUserInfo } = storeToRefs(userStore);

  const userInfo = ref<UserInfo | null>(null);

  /**
   * @description 弹窗进入动画后初始化用户信息
   */
  function handleAfterEnter() {
    userInfo.value = getUserInfo.value;
  }

  /**
   * @description 关闭弹窗，通知父组件更新显示状态
   */
  function handleHide() {
    emit("update:show", false);
  }
</script>

<style scoped>
/* 自定义样式 */
:deep(.n-card-header) {
  padding-bottom: 0;
}

:deep(.n-card__content) {
  padding-top: 0;
}
</style>
