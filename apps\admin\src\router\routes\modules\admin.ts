import type { RouteRecordRaw } from "vue-router";
import { LAYOUT } from "~/router/constant";

const admin: RouteRecordRaw = {
  path: "/admin",
  name: "admin",
  component: LAYOUT,
  redirect: "/admin/authz",
  meta: {
    title: "租户配置",
    icon: "i-tabler-settings",
    orderNumber: 60,
    shouldVerifyVisiblePermission: false,
  },
  children: [
    {
      path: "user",
      name: "user",
      component: () => import("~/pages/system/User.vue"),
      meta: {
        title: "用户列表",
        shouldVerifyVisiblePermission: false,
      },
    },
    {
      path: "authz",
      name: "authz",
      component: () => import("~/pages/system/Authz.vue"),
      meta: {
        title: "用户授权",
        breadcrumb: true,
      },
    },
    {
      path: "role",
      name: "role",
      component: () => import("~/pages/system/Role.vue"),
      meta: {
        title: "角色管理",
        shouldVerifyVisiblePermission: false,
      },
    },
    {
      path: "permission",
      name: "Permission",
      component: () => import("~/pages/system/Permission.vue"),
      meta: {
        title: "策略管理",
        shouldVerifyVisiblePermission: false,
      },
    },
    {
      path: "user-audit",
      name: "userAudit",
      component: () => import("~/pages/system/UserAudit.vue"),
      meta: {
        title: "登录审计",
        breadcrumb: true,
      },
    },
  ],
};

export default admin;
