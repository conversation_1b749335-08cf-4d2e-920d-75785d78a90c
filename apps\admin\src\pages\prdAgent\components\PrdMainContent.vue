<template>
  <PrdContent
    id="prd-content-main"
    title="PRD正文"
    :show-empty-state="!prdData.content"
    empty-state-text="请先填写左侧表单并点击生成按钮"
  >
    <template v-if="prdData.content" #button>
      <div class="flex items-center gap-2">
        <!-- 四个按钮 导出、复制、重新生成、显示脑图 -->
        <NButton size="small" type="primary" ghost :loading="exportLoading" @click="handleExport">
          <template #icon>
            <CAIcon icon="tabler:download" />
          </template>
          导出
        </NButton>
        <NButton size="small" type="primary" ghost :loading="copyLoading" @click="handleCopy">
          <template #icon>
            <CAIcon icon="tabler:copy" />
          </template>
          复制
        </NButton>
        <NButton size="small" type="primary" ghost :loading="regenerateLoading" @click="handleRegenerate">
          <template #icon>
            <CAIcon icon="tabler:wand" />
          </template>
          重新生成
        </NButton>
        <NButton size="small" type="primary" ghost @click="handleShowMindMap">
          <template #icon>
            <CAIcon icon="tabler:sitemap" />
          </template>
          显示脑图
        </NButton>
      </div>
    </template>

    <div class="h-full overflow-hidden">
      <!-- PRD内容 -->
      <div class="markdown-body h-full overflow-y-auto p-4" style="max-height: 100%;">
        <MdPreview :text="prdData.content" />
      </div>
    </div>
  </PrdContent>

  <!-- 脑图模态框 -->
  <PrdMindMapModal
    v-model:show="showMindMapModal"
    :prd-data="prdData"
  />
</template>

<script setup lang="ts">
  import { CAIcon } from "@celeris/components";
  import { NButton, useMessage } from "naive-ui";
  import { ref } from "vue";
  import { MdPreview } from "~/component/MdPreview";
  import PrdContent from "./PrdContent.vue";
  import PrdMindMapModal from "./PrdMindMapModal.vue";

  interface PrdData {
    title?: string;
    content?: string;
  }

  interface Props {
    prdData: PrdData;
  }

  const props = defineProps<Props>();
  const emits = defineEmits<{
    (e: "regenerate"): void;
  }>();

  const message = useMessage();
  const exportLoading = ref(false);
  const copyLoading = ref(false);
  const regenerateLoading = ref(false);
  const showMindMapModal = ref(false);

  // 导出PRD功能
  async function handleExport() {
    if (!props.prdData.content) {
      message.warning("暂无内容可导出");
      return;
    }

    exportLoading.value = true;
    try {
      // 构建导出内容
      const title = props.prdData.title || "PRD正文";
      const content = `# ${title}\n\n${props.prdData.content}`;

      // 清理文件名中的特殊字符
      const safeFileName = title.replace(/[<>:"/\\|?*]/g, "_").trim();

      // 创建并下载文件
      const blob = new Blob([content], { type: "text/markdown;charset=utf-8" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${safeFileName}.md`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success("导出成功");
    } catch (error) {
      console.error("导出失败:", error);
      message.error("导出失败，请重试");
    } finally {
      exportLoading.value = false;
    }
  }

  // 复制PRD内容到剪贴板
  async function handleCopy() {
    if (!props.prdData.content) {
      message.warning("暂无内容可复制");
      return;
    }

    copyLoading.value = true;
    try {
      // 使用原始markdown内容而不是渲染后的文本
      const content = props.prdData.content;
      await navigator.clipboard.writeText(content);
      message.success("内容已复制到剪贴板");
    } catch (error) {
      console.error("复制失败:", error);
      message.error("复制失败，请重试");
    } finally {
      copyLoading.value = false;
    }
  }

  // 重新生成PRD
  function handleRegenerate() {
    regenerateLoading.value = true;
    try {
      // 通知父组件触发重新生成
      emits("regenerate");
      message.info("正在重新生成PRD内容...");
    } catch (error) {
      console.error("重新生成失败:", error);
      message.error("重新生成失败，请重试");
      // 只有在发生错误时才重置loading状态
      regenerateLoading.value = false;
    }
    // 注意：正常情况下不重置loading状态，应该由父组件在生成完成后重置
  }
  // 设置loading状态的函数，并暴露给父组件
  function setRegenerateLoading(value: boolean) {
    regenerateLoading.value = value;
  };

  defineExpose({
    setRegenerateLoading,
  });

  // 显示脑图
  function handleShowMindMap() {
    if (!props.prdData.content) {
      message.warning("暂无内容可生成脑图");
      return;
    }

    showMindMapModal.value = true;
  }
</script>
