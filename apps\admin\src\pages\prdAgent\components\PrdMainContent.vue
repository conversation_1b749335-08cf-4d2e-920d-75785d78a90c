<template>
  <PrdContent
    id="prd-content-main"
    title="PRD正文"
    :show-empty-state="!prdData.content"
    empty-state-text="请先填写左侧表单并点击生成按钮"
  >
    <template v-if="prdData.content" #button>
      <div class="flex items-center gap-2">
        <!-- 四个按钮 导出、复制、重新生成、显示脑图 -->
        <NButton size="small" type="primary" ghost @click="handleExport">
          <template #icon>
            <CAIcon icon="tabler:download" />
          </template>
          导出
        </NButton>
        <NButton size="small" type="primary" ghost @click="handleCopy">
          <template #icon>
            <CAIcon icon="tabler:copy" />
          </template>
          复制
        </NButton>
        <NButton size="small" type="primary" ghost @click="handleRegenerate">
          <template #icon>
            <CAIcon icon="tabler:wand" />
          </template>
          重新生成
        </NButton>
        <NButton size="small" type="primary" ghost @click="handleShowMindMap">
          <template #icon>
            <CAIcon icon="tabler:sitemap" />
          </template>
          显示脑图
        </NButton>
      </div>
    </template>

    <div class="h-full overflow-hidden">
      <!-- PRD内容 -->
      <div class="markdown-body h-full overflow-y-auto p-4" style="max-height: 100%;">
        <MdPreview :text="prdData.content" />
      </div>
    </div>
  </PrdContent>
</template>

<script setup lang="ts">
  import { CAIcon } from "@celeris/components";
  import { NButton } from "naive-ui";
  import { MdPreview } from "~/component/MdPreview";
  import PrdContent from "./PrdContent.vue";

  interface PrdData {
    title?: string;
    content?: string;
  }

  interface Props {
    prdData: PrdData;
  }

  defineProps<Props>();

  // 导出PRD功能
  function handleExport() {
    // TODO: 实现PRD导出功能
    // 可以导出为PDF、Word等格式
    console.warn("导出PRD功能待实现");
  }

  // 复制PRD内容到剪贴板
  async function handleCopy() {
    try {
      const prdContent = document.querySelector(".markdown-body")?.textContent || "";
      await navigator.clipboard.writeText(prdContent);
      // TODO: 显示成功提示
      console.warn("PRD内容已复制到剪贴板");
    } catch (error) {
      console.error("复制失败:", error);
    }
  }

  // 重新生成PRD
  function handleRegenerate() {
    // TODO: 实现PRD重新生成功能
    // 可能需要调用API重新生成内容
    console.warn("重新生成PRD功能待实现");
  }

  // 显示脑图
  function handleShowMindMap() {
    // TODO: 实现显示脑图功能
    // 可能需要打开新窗口或模态框显示脑图
    console.warn("显示脑图功能待实现");
  }
</script>
