<template>
  <div
    ref="floatWrapperRef"
    class="shadow-[0_2px_10px_rgba(0,0,0,0.2)]"
    pf
    z-9
    p-2px
    bg-white
    rounded-2xl
    cursor-move
    :style="floatWrapperStyle"
    @mousedown="handleMouseDown"
  >
    <slot :set-slot-ref="setSlotRef" />
  </div>
</template>

<script lang="ts" setup>
  import type { ComponentPublicInstance } from "vue";
  import { isNumber } from "@celeris/utils";

  const props = withDefaults(defineProps<{
    initialTop?: number;
    initialLeft?: number;
    initialRight?: number;
    initialBottom?: number;
    dragable?: boolean
  }>(), {
    initialTop: 80,
    initialRight: 36,
    dragable: true
  });

  const emit = defineEmits<{
    (e: "dragStart", x: number, y: number, el: HTMLDivElement | null | undefined): void;
    (e: "dragging", x: number, y: number, el: HTMLDivElement | null | undefined): void;
    (e: "dragEnd", x: number, y: number, el: HTMLDivElement | null | undefined): void;
  }>();

  const floatWrapperRef = useTemplateRef<HTMLDivElement>("floatWrapperRef");
  const slotRef = ref<ComponentPublicInstance | HTMLElement | null>();
  function setSlotRef(el: ComponentPublicInstance | HTMLElement | null) {
    slotRef.value = el || null;
  }

  const top = ref<number | undefined>(props.initialTop);
  const left = ref<number | undefined>(props.initialLeft);
  const right = ref<number | undefined>(props.initialRight);
  const bottom = ref<number | undefined>(props.initialBottom);
  const floatWrapperStyle = computed(() => {
    return {
      top: isNumber(top.value) ? `${top.value}px` : undefined,
      left: isNumber(left.value) ? `${left.value}px` : undefined,
      bottom: isNumber(bottom.value) ? `${bottom.value}px` : undefined,
      right: isNumber(right.value) ? `${right.value}px` : undefined,
    };
  });

  const isDragging = ref(false);
  const offsetX = ref(0);
  const offsetY = ref(0);
  let mask: HTMLDivElement | null = null;

  function handleMouseDown(e: MouseEvent) {
    if (!props.dragable) {
      return
    }

    // target 是点击的元素（可能是子元素）、currentTarget 是当前事件绑定的元素
    if (e.target !== e.currentTarget) {
      // 不响应子元素的 mousedown 事件
      return;
    }

    const el = e.target as HTMLElement;
    const rect = el.getBoundingClientRect();
    isDragging.value = true;
    offsetX.value = e.clientX - rect.left;
    offsetY.value = e.clientY - rect.top;
    createMask();

    document.onselectstart = () => false; // 解决拖拽本组件时，页面全部元素都会被选中的问题
    document.ondragstart = () => false; // 解决拖拽本组件时，页面全部元素都会被选中的问题
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);

    emit("dragStart", e.clientX, e.clientY, floatWrapperRef.value);
  }

  function handleMouseMove(e: MouseEvent) {
    if (isDragging.value) {
      left.value = e.clientX - offsetX.value;
      top.value = e.clientY - offsetY.value;
      bottom.value = undefined;
      right.value = undefined;

      emit("dragging", e.clientX, e.clientY, floatWrapperRef.value);
    }
  }

  function handleMouseUp(e: PointerEvent) {
    const el = floatWrapperRef.value as HTMLDivElement;
    const rect = el.getBoundingClientRect();

    if ((left.value as number) < 0) {
      left.value = 0;
    } else if (left.value as number + rect.width > document.body.clientWidth) {
      left.value = document.body.clientWidth - rect.width;
    }
    if ((top.value as number) < 0) {
      top.value = 0;
    } else if (top.value as number + rect.height > document.body.clientHeight) {
      top.value = document.body.clientHeight - rect.height;
    }

    isDragging.value = false;
    removeMask();

    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);

    emit("dragEnd", e.clientX, e.clientY, floatWrapperRef.value);
  }

  function createMask() {
    mask = document.createElement("div");
    mask.style.cssText = `
      position: fixed;
      inset: 0;
      z-index: 8;
    `;
    document.body.appendChild(mask);
  }

  function removeMask() {
    if (mask) {
      mask.remove();
      mask = null;
    }
  }
</script>
