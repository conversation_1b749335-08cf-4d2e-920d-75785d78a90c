import { mount } from "@vue/test-utils";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { nextTick } from "vue";
import { deleteUserAuthz, getUserAuthzList } from "~/apis/internal/authz";
import { getRoleList } from "~/apis/internal/role";
import { useTable } from "~/composables/useTable";
import Index from "../Authz/index.vue";

// mock 依赖
const messageMock = {
  success: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
  warning: vi.fn(),
};
vi.mock("~/apis/internal/authz", () => ({
  getUserAuthzList: vi.fn().mockResolvedValue([]),
  deleteUserAuthz: vi.fn().mockResolvedValue({}),
}));
vi.mock("~/apis/internal/role", () => ({
  getRoleList: vi.fn().mockResolvedValue({ data: [] }),
}));
vi.mock("../Authz/components/AuthzFormModal.vue", () => ({
  default: {
    name: "AuthzFormModal",
    props: ["show"],
    template: "<div v-if=\"show\" class=\"mock-authz-form-modal\"></div>",
  },
}));
vi.mock("@celeris/ca-components", () => ({
  useMessage: () => messageMock,
  useDialog: () => ({
    info: vi.fn().mockImplementation(({ onPositiveClick }) => {
      if (onPositiveClick) {
        onPositiveClick();
      }
    }),
  }),
}));
vi.mock("~/composables/useTable", () => {
  const loadDataMock = vi.fn();
  const refreshDataMock = vi.fn();
  const reloadDataMock = vi.fn();
  return {
    useTable: () => ({
      tableLoading: false,
      tableData: [
        {
          id: "1",
          user: "userA",
          role: "管理员",
          domain_id: "domain-1",
          key: "1",
          deleteLoading: false,
        },
      ],
      tablePagination: { page: 1, pageSize: 10, itemCount: 1 },
      loadData: loadDataMock,
      refreshData: refreshDataMock,
      reloadData: reloadDataMock,
      handlePageChange: vi.fn(),
      handlePageSizeChange: vi.fn(),
      handleScroll: vi.fn(),
    }),
  };
});

function getCurrentMessageMock() {
  return messageMock;
}

describe("authz 授权管理页", () => {
  let wrapper;
  let tableApi;
  beforeEach(async () => {
    wrapper = mount(Index, {
      props: { domainId: "test-domain" },
    });
    tableApi = useTable();
    await nextTick();
  });

  it("应渲染主要结构和表格数据", async () => {
    expect(wrapper.findComponent({ name: "PageWrapper" }).exists()).toBe(true);
    expect(wrapper.findComponent({ name: "NDataTable" }).exists()).toBe(true);
    expect(wrapper.text()).toContain("userA");
    expect(wrapper.text()).toContain("管理员");
  });

  // loadTableData
  it("loadTableData 应调用 loadData 并格式化数据", () => {
    wrapper.vm.loadTableData();
    expect(tableApi.loadData).toHaveBeenCalled();
  });

  // getRoles
  it("getRoles 应调用 getRoleList 并设置 roleOptions", async () => {
    await wrapper.vm.getRoles();
    expect(getRoleList).toHaveBeenCalled();
  });

  // searchConfirm
  it("searchConfirm 应调用 reloadData", () => {
    wrapper.vm.searchConfirm();
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  // searchReset
  it("searchReset 应清空筛选项并 reloadData", () => {
    wrapper.vm.searchModel.user = "xxx";
    wrapper.vm.searchReset();
    expect(wrapper.vm.searchModel.user).toBeNull();
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  // handleAdd
  it("handleAdd 应弹出新增弹窗", () => {
    wrapper.vm.authzFormModalShow = false;
    wrapper.vm.currAuthz = { id: "1" };
    wrapper.vm.handleAdd();
    expect(wrapper.vm.authzFormModalShow).toBe(true);
    expect(wrapper.vm.currAuthz).toBeNull();
  });

  // handleEdit
  it("handleEdit 应弹出编辑弹窗并设置当前授权", () => {
    const row = { id: "2", user: "userB" };
    wrapper.vm.handleEdit(row);
    expect(wrapper.vm.authzFormModalShow).toBe(true);
    expect(wrapper.vm.currAuthz).toEqual(row);
  });

  // handleUpdateAuthz
  it("handleUpdateAuthz 应调用 searchConfirm", () => {
    const spy = vi.spyOn(wrapper.vm, "searchConfirm");
    wrapper.vm.handleUpdateAuthz();
    expect(spy).toHaveBeenCalled();
  });

  // handleDelete
  it("handleDelete: 删除成功应提示并刷新", async () => {
    const row = { id: "1", user: "userA" };
    (deleteUserAuthz as any).mockResolvedValue({});
    await wrapper.vm.handleDelete(row);
    expect(deleteUserAuthz).toHaveBeenCalled();
    expect(getCurrentMessageMock().success).toHaveBeenCalledWith("删除成功");
    expect(tableApi.refreshData).toHaveBeenCalled();
  });

  it("handleDelete: 删除失败应提示", async () => {
    const row = { id: "1", user: "userA" };
    (deleteUserAuthz as any).mockRejectedValue(new Error("fail"));
    await wrapper.vm.handleDelete(row);
    expect(getCurrentMessageMock().error).toHaveBeenCalledWith("删除失败");
  });

  // UI交互
  it("点击新增授权按钮应弹出表单", async () => {
    expect(wrapper.find(".mock-authz-form-modal").exists()).toBe(false);
    await wrapper.findAllComponents({ name: "NButton" })[0].trigger("click");
    await nextTick();
    expect(wrapper.find(".mock-authz-form-modal").exists()).toBe(true);
  });

  it("点击查询按钮应调用 reloadData", async () => {
    await wrapper.findAllComponents({ name: "NButton" })[1].trigger("click");
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  it("点击重置按钮应清空筛选并 reloadData", async () => {
    await wrapper.findAllComponents({ name: "NButton" })[2].trigger("click");
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  it("点击删除按钮应弹出确认框并调用删除接口和刷新", async () => {
    await wrapper.findAllComponents({ name: "NButton" })[4].trigger("click");
    await nextTick();
    expect(deleteUserAuthz).toHaveBeenCalled();
    expect(tableApi.refreshData).toHaveBeenCalled();
  });
});
