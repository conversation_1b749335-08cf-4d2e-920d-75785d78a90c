<template>
  <NPopover ref="popoverRef" trigger="click" :show-arrow="false">
    <template #trigger>
      <ToolTipper tooltip-text="用户信息">
        <NEl tag="div" class="btn-icon rounded-3xl p-2 pr-2 justify-between bg-[var(--action-color)] hover:bg-[var(--hover-color)]">
          <div class="flex flex-row">
            <!-- <div class="flex-1 flex items-center justify-center">
              <NAvatar round :src="userInfo?.avatarUrl" />
            </div> -->
            <div class="flex-1 flex items-center justify-center">
              <svg class="text-center text-[var(--text-color-base)]  i-line-md:account" />
            </div>
          </div>
        </NEl>
      </ToolTipper>
    </template>

    <template #header>
      <h4 class="mb-1">
        <span class="font-bold">{{ t('layouts.userInfo.greeting') }}, </span> <span class="">{{ getNickName }} </span>
      </h4>
      <!-- <p>
        {{ t('layouts.userInfo.rolesList', { roles: userInfo?.roles }) }}
      </p> -->
    </template>

    <!--    <NButton block quaternary @click="router.push({ name: 'Profile' })">
      {{ t('routes.profile.profile') }}
    </NButton> -->

    <NButton v-if="allowUpdatePassword" block quaternary @click="handleUpdatePassword">
      {{ t('layouts.userInfo.updatePassword') }}
    </NButton>

    <template #footer>
      <NButton block quaternary class="!w-32" @click="handleShowUserInfo">
        <template #icon>
          <svg class="i-tabler-user text-base" />
        </template>
        个人信息
      </NButton>

      <!-- 租户切换按钮 -->
      <TenantSwitcher @tenant-switched="handleTenantSwitched" />

      <NButton block quaternary class="!w-32" @click="handleLogout">
        <template #icon>
          <svg class="i-tabler-logout text-base" />
        </template>
        退出登录
      </NButton>
    </template>
  </NPopover>

  <UpdatePasswordModal v-model:show="updatePasswordModalShow" />
  <UserInfoModal v-model:show="userInfoModalShow" />
</template>

<script setup lang="ts">
  import { ToolTipper } from "~/component/ActionIcon";
  import { useUserStore } from "~/store/modules/user";
  import TenantSwitcher from "./TenantSwitcher.vue";
  import UpdatePasswordModal from "./UpdatePasswordModal.vue";
  import UserInfoModal from "./UserInfoModal.vue";

  const { t, locale } = useI18n();
  locale.value = "zh";

  const message = useMessage();
  const dialog = useDialog();

  const userStore = useUserStore();
  const { getNickName, getLoginMode } = storeToRefs(userStore);

  const allowUpdatePassword = computed(() => getLoginMode.value === LoginModeConst.local);
  const updatePasswordModalShow = ref(false);
  const userInfoModalShow = ref(false);
  const popoverRef = ref();
  /*
  * 处理修改密码
   * 1. 显示修改密码表单
   * 2. 提交表单
   * 3. 处理表单提交结果
   */
  function handleUpdatePassword() {
    updatePasswordModalShow.value = true;
  }

  /*
  * 显示用户信息页面
   */
  function handleShowUserInfo() {
    // 不用路由跳转，左侧的菜单不变，显示用户信息模态框
    userInfoModalShow.value = true;
  }

  /*
  * 处理租户切换事件
   */
  function handleTenantSwitched() {
    // 关闭 NPopover
    if (popoverRef.value) {
      popoverRef.value.setShow(false);
    }
  }

  /*
  * 处理退出登录
   * 1. 显示退出登录确认对话框
   * 2. 处理确认结果
   */
  function handleLogout() {
    dialog.warning({
      title: t("layouts.logoutConfirmation.title"),
      content: t("layouts.logoutConfirmation.content"),
      positiveText: t("layouts.logoutConfirmation.positiveText"),
      negativeText: t("layouts.logoutConfirmation.negativeText"),
      onPositiveClick: () => {
        userStore.logout();
        message.success(t("layouts.logoutConfirmation.onPositiveClickMessage"));
      },
      onNegativeClick: () => {
        message.info(t("layouts.logoutConfirmation.onNegativeClickMessage"));
      },
    });
  }
</script>

<style scoped>

</style>
