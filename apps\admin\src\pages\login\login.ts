import { PageConstants } from "@celeris/constants";
import { router } from "~/router";
import { useUserStore } from "~/store/modules/user";

/**
 * 所有登录，用户，权限逻辑封装
 * 处理登录成功后的逻辑
 */
export async function afterLogin(res: any) {
  const userStore = useUserStore();
  userStore.setToken(res.data.token);
  userStore.setUserInfo(res.data.user);
  userStore.setUserName(res.data.user.user_name);

  if (res.code !== 0) {
    return res;
  }

  if (res.data.user.state === 0) {
    router.replace(PageConstants.BASE_HOME);
  } else {
    // 跳转到修改密码
    router.push("/reset-password"); // 相对于当前
  }
}

/**
 * 初始化用户信息和菜单
 */
export async function initUserInfo() {
  // console.log('初始化用户信息和菜单');
  const userStore = useUserStore();
  /**
  const permissionStore = usePermissionStore();
   
  const resMenu = await permissionStore.getAsyncRoutesFun();
  permissionStore.setAsyncRoutes(resMenu);
   */
  await userStore.fetchUserInfo();
}

/**
 * 处理租户选择逻辑
 * 根据用户可用租户数量决定跳转路径
 */
export async function handleTenantSelection() {
  const userStore = useUserStore();

  try {
    // 检查是否已经存在当前租户信息
    const currentTenant = userStore.getCurrentTenant;
    if (currentTenant) {
      // 已有当前租户，直接进入首页
      router.replace(PageConstants.BASE_HOME);
      return;
    }

    // 获取用户可用租户列表
    const tenants = await userStore.fetchAvailableTenants(true);

    if (tenants.length === 1) {
      // 只有一个租户，自动选中并进入
      const tenant = tenants[0];
      await userStore.switchTenant(tenant.id!);
      router.replace(PageConstants.BASE_HOME);
    } else if (tenants.length === 0) {
      // 无租户，跳转到租户选择页面
      router.replace("/tenant-select");
    } else {
      // 判断是否有默认租户
      const defaultTenant = tenants.find(t => t.is_default);
      if (defaultTenant) {
        // 有默认租户，自动选中并进入
        await userStore.switchTenant(defaultTenant.id!);
        router.replace(PageConstants.BASE_HOME);
        return;
      }
      // 跳转到租户选择页面
      router.replace("/tenant-select");
    }
  } catch (error) {
    console.error("处理租户选择失败:", error);
    // 出错时跳转到首页
    router.replace(PageConstants.BASE_HOME);
  }
}
