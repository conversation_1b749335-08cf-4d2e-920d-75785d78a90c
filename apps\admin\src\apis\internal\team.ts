import type { TreeItem, TreeWrapper } from "-/common";
import type { TeamParam } from "-/team";
import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";
import { replaceUrlPathParams } from "@celeris/utils";

enum API {
  Team = "/v1/teams",
  DeleteTeam = "/v1/teams/{id}",
  TeamTree = "/v2/team/getTeamByIds",
  UpdateTeam = "/v2/team/updateTeam",
}

/**
 * 获取团队组织树
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<TreeItem[]> 用户树列表
 */
export function allTeamTreeApi(params?: AnyObject, errorMessageMode: MessageMode = "message"): Promise<TreeItem[]> {
  return request.get<TreeItem>({ url: API.Team, params }, { errorMessageMode })
    .then((res) => {
      return res ? [res] : [];
    });
}

/**
 * 创建团队
 * @param params - 团队创建参数
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<any> 创建结果
 */
export function createTeamApi(params: TeamParam, errorMessageMode: MessageMode = "message") {
  return request.post({ url: API.Team, params }, { errorMessageMode });
}

/**
 * 更新团队
 * @param params - 团队更新参数
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<any> 更新结果
 */
export function updateTeamApi(params: TeamParam, errorMessageMode: MessageMode = "message") {
  return request.put({ url: API.Team, params }, { errorMessageMode });
}

/**
 * 删除团队
 * @param id - 团队ID
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<any> 删除结果
 */
export function deleteTeamApi(id: number, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.DeleteTeam, { id });
  return request.delete({ url }, { errorMessageMode });
}
