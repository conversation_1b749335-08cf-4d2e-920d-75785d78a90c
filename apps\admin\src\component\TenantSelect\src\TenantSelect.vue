<template>
  <UserTreeSelect
    v-bind="$attrs"
    v-model:value="modelValue"
    check-strategy="parent"
    :require-permission="false"
    :handle-data-source="handleUserTree"
    @update:value="handleUpdateTenant"
    @load-success="handleUserTreeLoadSuccess"
  />
</template>

<script setup lang="ts">
  import type { TreeItem, TreeSelectMeta } from "-/common";
  import UserTreeSelect from "~/component/UserTreeSelect/src/UserTreeSelect.vue";

  const props = withDefaults(defineProps<{
    allowDefault?: boolean;
  }>(), {
    allowDefault: true,
  });

  const emit = defineEmits<{
    (e: "change", value: string, option: TreeItem, meta: TreeSelectMeta<TreeItem>): void;
  }>();

  const modelValue = defineModel("value", { type: String, default: null });

  /*
   * 处理树形结构
   * 1. 删除 children 属性
   */
  function handleUserTree(dataSource: TreeItem[]) {
    dataSource.forEach((item) => {
      Reflect.deleteProperty(item, "children");
    });
    return dataSource;
  }

  /*
   * 默认选中第一个
   */
  function handleUserTreeLoadSuccess(dataSource: TreeItem[]) {
    if (props.allowDefault && !modelValue.value && dataSource?.[0]) {
      modelValue.value = dataSource[0].id;
      handleUpdateTenant(dataSource[0].id, dataSource[0], { action: "select", node: dataSource[0] });
    }
  }

  /*
   * 选中事件
   */
  function handleUpdateTenant(value: string, option: TreeItem, meta: TreeSelectMeta<TreeItem>) {
    emit("change", value, option, meta);
  }
</script>

<style scoped>

</style>
