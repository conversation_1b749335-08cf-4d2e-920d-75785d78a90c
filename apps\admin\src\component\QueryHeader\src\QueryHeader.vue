<template>
  <NPageHeader v-bind="$attrs">
    <template v-for="(slot, key) in $slots" #[key]="slotData">
      <slot :name="key" v-bind="slotData || {}" />
    </template>

    <NFlex class="pr flex-1" :wrap="false">
      <slot name="primary">
        <NButton v-if="showPrimary" type="primary" @click="handleCreate">
          <template #icon>
            <CAIcon name="tabler:square-rounded-plus" />
          </template>
          新增
        </NButton>
      </slot>

      <NFlex v-if="hasQuery" ref="formContainerRef" class="flex-1 ml-auto" justify="end" :wrap="false">
        <NForm
          class="!w-auto"
          inline
          :show-feedback="false"
          :show-label="showLabel"
          :label-placement="labelPlacement"
          :model="formModel"
        >
          <NFormItem v-for="item in visibleFormItems" :key="item.path" :path="item.path" :label="item.label">
            <div :style="{ width: `${componentWidth}px` }">
              <component
                :is="item.component"
                v-model:[modelKey(item.modelKey)]="formModel[item.path]"
                v-bind="item.props"
                class="w-full"
              />
            </div>
          </NFormItem>
        </NForm>

        <NPopover v-if="!hasSpace" class="!p-0" placement="bottom" trigger="click">
          <template #trigger>
            <NButton square>
              <template #icon>
                <CAIcon name="tabler:dots" />
              </template>
            </NButton>
          </template>

          <NScrollbar class="max-h-500px">
            <NFlex vertical :size="0">
              <NForm
                class="!w-auto px-4 pt-6"
                label-width="auto"
                :show-label="showLabel"
                :label-placement="labelPlacement"
                :model="formModel"
              >
                <NFormItem v-for="item in hiddenFormItems" :key="item.path" :path="item.path" :label="item.label">
                  <div :style="{ width: `${componentWidth}px` }">
                    <component
                      :is="item.component"
                      v-model:[modelKey(item.modelKey)]="formModel[item.path]"
                      v-bind="item.props"
                      class="w-full"
                    />
                  </div>
                </NFormItem>
              </NForm>

              <!--            <NDivider class="!m-0" />

              <NFlex class="p-2" justify="center">
                <NButton type="primary" size="small">查询</NButton>
                <NButton size="small">重置</NButton>
              </NFlex> -->
            </NFlex>
          </NScrollbar>
        </NPopover>
      </NFlex>
      <slot name="search"></slot>
      <NButton v-if="showRefresh" square :class="{ 'ml-auto': !hasQuery }" @click="handleRefresh">
        <template #icon>
          <CAIcon name="tabler:refresh" />
        </template>
      </NButton>
    </NFlex>
  </NPageHeader>
</template>

<script lang="ts" setup>
  import type { LabelPlacement } from "naive-ui/es/form/src/interface";
  import type { QueryHeaderItem } from "./types";
  import { NFlex } from "@celeris/ca-components";
  import { isArray } from "@celeris/utils";
  import { NForm } from "naive-ui";

  defineOptions({
    name: "QueryHeader",
    inheritAttrs: false,
  });

  const props = withDefaults(defineProps<{
    items?: QueryHeaderItem[];
    showPrimary?: boolean;
    showRefresh?: boolean;
    showLabel?: boolean;
    labelPlacement?: LabelPlacement;
    componentWidth?: number;
    handleCreate?: () => void;
    handleRefresh?: () => void;
  }>(), {
    showPrimary: true,
    showRefresh: true,
    labelPlacement: "left",
    componentWidth: 200,
    handleCreate: () => {},
    handleRefresh: () => {},
  });

  const modelValue = defineModel("value", { type: Object, default: null });

  const formItems = ref<QueryHeaderItem[]>([]);
  const hasQuery = computed(() => isArray(formItems.value) && formItems.value.length > 0);
  const visibleFormItems = computed(() => formItems.value?.filter?.(item => !item.hidden) || []);
  const hiddenFormItems = computed(() => formItems.value?.filter?.(item => item.hidden) || []);

  watchEffect(() => {
    formItems.value = (props.items || []).map((item) => {
      item.hidden = false;
      return item;
    });
  });

  type NFlexInstance = InstanceType<typeof NFlex>;
  const formContainerRef = useTemplateRef<NFlexInstance>("formContainerRef");
  const hasSpace = ref(true);
  const { stop } = useResizeObserver(formContainerRef, (entries) => {
    nextTick(() => {
      // 表单容器宽度
      const { width: formContainerWidth } = entries[0].contentRect;
      // 平均宽度，如果显示 label 的话，设定 100 像素，在 14 像素下，100 像素大约 6 个字（100 像素中包含了 label 自带的 padding-right）
      const avgItemWidth = props.componentWidth + (props.showLabel ? 100 : 0);
      // 所有宽度
      const allItemsWidth = formItems.value.length * avgItemWidth;
      // 如果所有的表单项宽度 <= 表单容器的宽度，说明还有空间，是放得下的，不显示更多表单项下拉组件
      if (allItemsWidth <= formContainerWidth) {
        hasSpace.value = true;
        formItems.value.forEach(item => item.hidden = !hasSpace.value);
      } else {
        // 如果所有的表单项宽度 > 表单容器的宽度，说明没有空间，是放不下的，显示更多表单项下拉组件
        // 并计算超出的宽度大概是多少个表单项，将后面超出的表单项收到更多表单项组件中
        hasSpace.value = false;
        const beyondCount = Math.ceil((allItemsWidth - formContainerWidth) / avgItemWidth);
        const beyondIndexes = Array.from({ length: formItems.value.length }, (_, i) => i).slice(-beyondCount);
        for (let i = 0; i < formItems.value.length; i++) {
          formItems.value[i].hidden = beyondIndexes.includes(i);
        }
      }
    });
  });
  onUnmounted(stop);

  const formModel = ref({});

  onBeforeMount(initData);

  watch(modelValue, () => {
    initData();
  }, { deep: true });

  watch(formModel, (newVal) => {
    modelValue.value = { ...modelValue.value, ...newVal };
  }, { deep: true });

  function initData() {
    formItems.value.forEach((item) => {
      formModel.value[item.path] = modelValue?.value?.[item.path];
    });
  }

  function modelKey(key) {
    return key || "value";
  }
</script>

<style scoped>

</style>
