import type { LoginParam } from "-/login";
import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";
import { replaceUrlPathParams } from "@celeris/utils";

enum API {
  GetUserInfoApi = "/v1/user",
  GetCodeApi = "/v1/code",
  GetLoginModeApi = "/v1/mode",
  LoginApi = "/v1/login",
  LoginAoneApi = "/v1/login/aone",
  LogoutApi = "/v2/authn/logout",
  OtherLoginApi = "/v2/user/login/oidc",
  PasswdApi = "/v2/user/passwd",
}

/**
 *  用户信息
 * @returns
 */
export function getUserInfo(errorMessageMode: MessageMode = "message") {
  return request.get({ url: API.GetUserInfoApi }, { errorMessageMode });
}

/**
 * 获取验证码
 * @param params - 用户信息
 * @returns
 */
export function getCode(params: { username: string }, errorMessageMode: MessageMode = "message") {
  return request.get({ url: API.GetCodeApi, params }, { errorMessageMode });
}

/**
 * 获取登录模式
 * @param projectId - 项目id
 * @returns
 */
export function getLoginMode(projectId: number | string = 1111, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.GetLoginModeApi, { projectId });
  return request.get({ url }, { errorMessageMode });
}

/**
 * 登录 用户信息
 * @param data
 * @returns
 */
export function login(data: LoginParam = {}, errorMessageMode: MessageMode = "message") {
  return request.post({ url: API.LoginApi, data }, { errorMessageMode, shouldTransformResponse: false });
}

/**
 * 重定向到aone登录
 * @param data
 * @returns
 */
export function loginAone(errorMessageMode: MessageMode = "message") {
  return request.get({ url: API.LoginAoneApi }, { errorMessageMode });
}

/**
 * 登出
 * @returns
 */
export function logout(errorMessageMode: MessageMode = "message") {
  return request.get({ url: API.LogoutApi }, { errorMessageMode });
}

/**
 * 修改密码
 * @param params
 * @param token
 * @returns
 */
export function resetPassword(params: LoginParam = {}, token: string, errorMessageMode: MessageMode = "message") {
  return request.put({
    url: API.PasswdApi,
    headers: { Authorization: `Bearer ${token}` },
    params,
  }, { errorMessageMode });
}

/**
 * 获取其他令牌
 * @param challenge
 * @returns
 */
export function otherLogin(challenge?: string, errorMessageMode: MessageMode = "message") {
  return request.get({ url: API.OtherLoginApi, params: { login_challenge: challenge } }, { errorMessageMode });
}

/**
 * 用户修改密码前校验
 * @param data - 用户信息 {username, code}
 * @returns
 */
export function checkPasswd(data: LoginParam = {}, errorMessageMode: MessageMode = "message") {
  return request.post({ url: API.PasswdApi, data }, { errorMessageMode });
}

/**
 * 重置密码
 * @param data - 用户信息 {username, password}
 * @returns
 */
export function setPasswd(data: LoginParam = {}, errorMessageMode: MessageMode = "message") {
  return request.patch({ url: API.PasswdApi, data }, { errorMessageMode });
}

export { API };
