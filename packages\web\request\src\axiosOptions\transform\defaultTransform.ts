import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from "axios";
import type { AxiosTransform, CreateAxiosOptions } from "../../axiosTransform";
import type { RequestOptions, RequestResult } from "../../types";
import { HttpStatusConstants, RequestConstants, ResultConstants } from "@celeris/constants";
import { downloadFile, extractContentDispositionFilename, isEmpty, isString } from "@celeris/utils";
import { HttpRequestConfiguration } from "../../../requestConfiguration";
import { formatRequestDate, joinTimestamp, setObjToUrlParams } from "../../utils";

export const defaultTransform: AxiosTransform = {
  /**
   * A function that is called before a request is sent. It can modify the request configuration as needed.
   * 在发送请求之前调用的函数。它可以根据需要修改请求配置。
   */
  beforeRequest(config: AxiosRequestConfig, options: RequestOptions): AxiosRequestConfig {
    const { apiUrl, shouldJoinPrefix, shouldJoinParamsToUrl, shouldFormatDate, shouldJoinTime = true, urlPrefix } = options;

    if (shouldJoinPrefix) {
      config.url = `${urlPrefix ?? ""}${config.url ?? ""}`;
    }

    if (apiUrl && isString(apiUrl)) {
      config.url = `${apiUrl}${config.url ?? ""}`;
    }
    const params = config.params || {};
    const data = config.data || false;
    // Format date in request data if shouldFormatDate is true
    shouldFormatDate && data && !isString(data) && formatRequestDate(data);

    if (config.method?.toUpperCase() === RequestConstants.GET) {
      if (!isString(params)) {
        // Add timestamp parameter to GET requests to avoid caching
        // 给 get 请求加上时间戳参数，避免从缓存中拿数据。
        config.params = { ...params, ...joinTimestamp(shouldJoinTime, false) };
      } else {
        // Support REST style
        // 兼容restful风格
        config.url = `${config.url ?? ""}${params}${joinTimestamp(shouldJoinTime, true)}`;
        config.params = undefined;
      }
    } else {
      if (!isString(params)) {
        shouldFormatDate && formatRequestDate(params);
        // Append params and data to request configuration if they exist
        if (
          Reflect.has(config, "data")
          && config.data
          && (Object.keys(config.data).length > 0 || config.data instanceof FormData)
        ) {
          config.data = data;
          config.params = params;
        } else {
          // If data is not provided for non-GET requests, treat params as data
          // 非GET请求如果没有提供data，则将params视为data
          config.data = params;
          config.params = undefined;
        }

        // Append params and data to url query parameters if shouldJoinParamsToUrl is true
        if (shouldJoinParamsToUrl) {
          config.url = setObjToUrlParams(
            config.url as string,
            Object.assign({}, config.params, config.data),
          );
        }
      } else {
        // Support REST style
        // 兼容restful风格
        config.url = `${config.url ?? ""}${params}`;
        config.params = undefined;
      }
    }
    return config;
  },

  /**
   * A function that is called after a response is received. It can transform the response data as needed.
   * 在接收到响应后调用的函数。它可以根据需要转换响应数据。
   */
  afterResponse(response: AxiosResponse | AxiosResponse<RequestResult>, options: RequestOptions): any {
    // 直接返回响应头信息
    if (options.shouldReturnNativeResponseHeaders) {
      return response;
    }

    // 不转换 response.data，直接返回原始数据
    if (!options.shouldTransformResponse) {
      return response.data;
    }

    const error = (response as any).error;

    // 有 401 未登录的情况，优先处理，后面还转换干什么？
    // 401 处理
    if (error?.status === HttpStatusConstants.Unauthorized) {
      HttpRequestConfiguration.unauthorizedHandler(error);
      throw error;
    }

    // 403 处理
    if (error?.status === HttpStatusConstants.Forbidden) {
      const message = "用户无权访问";
      HttpRequestConfiguration.errorMessageHandler(message, options.errorMessageMode);
      throw error;
    }

    // 转换 response.data
    const responseData = response.data;
    const contentDisposition = response.headers["Content-Disposition"] || response.headers["content-disposition"];
    const isFile = contentDisposition?.includes?.("attachment");

    if (isFile) {
      if (!responseData) {
        HttpRequestConfiguration.errorMessageHandler("没有获取到文件数据", options.errorMessageMode);
        throw new Error("接口请求失败，请稍后重试！");
      }

      const frontendFilename = response.config.data?.filename || response.config.params?.filename;
      const backendFilename = extractContentDispositionFilename(response.headers["content-disposition"]);
      downloadFile(frontendFilename || backendFilename || "file", responseData);
      return responseData;
    } else {
      if (!responseData) {
        throw new Error("接口请求失败，请稍后重试！");
      }

      // Depending on the project, you may need to adjust the logic here
      // 根据项目的不同，您可能需要调整这里的逻辑
      const { code, data: _data, message } = responseData;
      const isSuccessful = responseData && Reflect.has(responseData, "code") && code === ResultConstants.SUCCESS;

      if (isSuccessful) {
        let successMessage = message;

        if (isEmpty(successMessage)) {
          successMessage = "操作成功";
        }
        HttpRequestConfiguration.successMessageHandler(successMessage, options.successMessageMode);
        return _data;
      }

      // 401 处理
      if (code === ResultConstants.UNAUTHORIZED) {
        HttpRequestConfiguration.errorMessageHandler(message, "dialog");
        HttpRequestConfiguration.unauthorizedHandler(responseData);
      } else {
        HttpRequestConfiguration.errorMessageHandler(message, options.errorMessageMode);
      }

      throw new Error(message);
    }
  },

  /**
   * A function that is called when an error occurs during the request. It can handle the error as needed.
   * 在请求期间发生错误时调用的函数。它可以根据需要处理错误。
   */
  onRequestError(error: Error, options: RequestOptions): any {
    return {
      error,
      options,
    };
  },

  /**
   * A function that is called before a request interceptors is executed. It can modify the request configuration as needed.
   * 在执行请求拦截器之前调用的函数，可以根据需要修改请求配置。
   */
  requestInterceptors(config: InternalAxiosRequestConfig, options: CreateAxiosOptions): InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig> {
    // const token = HttpRequestConfiguration.getToken();
    // if (token && options.requestOptions?.shouldSendTokenInHeader) {
    //   config.headers.Authorization = options.authenticationScheme ? `${options.authenticationScheme} ${<string>token}` : <string>token;
    // }

    // 添加租户头信息
    try {
      // 从 HttpRequestConfiguration 中获取租户信息
      const getCurrentTenant = HttpRequestConfiguration.getCurrentTenant;
      if (getCurrentTenant) {
        const currentTenant = getCurrentTenant();
        if (currentTenant?.id) {
          config.headers["x-done-tenant"] = currentTenant.id;
        }
      }
    } catch (error) {
      // 如果获取租户信息失败，不影响请求继续执行
      console.warn("Failed to get current tenant for request header:", error);
    }

    return config;
  },

  /**
   * A function that is called after a response interceptors is executed. It can transform the response data as needed.
   * 在执行响应拦截器之后调用的函数，可以根据需要转换响应数据。
   */
  responseInterceptors(response: AxiosResponse): AxiosResponse | Promise<AxiosResponse> {
    return response;
  },

  /**
   * A function that is called when an error occurs during the request interceptors. It can handle the error as needed.
   * 在执行请求拦截器时发生错误时调用的函数，可以根据需要处理错误。
   */
  requestInterceptorsError(error: Error): any {
    return error;
  },

  /**
   * A function that is called when an error occurs during the response interceptors. It can handle the error as needed.
   * 在执行响应拦截器时发生错误时调用的函数，可以根据需要处理错误。
   */
  responseInterceptorsError(error: Error, axiosInstance: AxiosInstance): any {
    return { error, instance: axiosInstance };
  },

};
