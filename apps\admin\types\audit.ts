import type { Paging } from "./common";

export interface Audit {
  id: number;
  nickname: string;
  username: string;
  email: string;
  path: string;
  method: string;
  ip: string;
  time: string;
  description?: string;
  domain_id?: string;
}

export type AuditExpanded = Audit & {
  key?: string;
  deleteLoading?: boolean;
};

export interface AuditParam extends Paging {
  from?: string;
  to?: string;
  user?: string;
  path?: string;
}
