<template>
  <NModal
    v-model:show="showModal"
    :mask-closable="false"
    preset="card"
    class="w-[90vw] max-w-[1200px]"
    :title="modalTitle"
    :bordered="false"
    size="huge"
    role="dialog"
    aria-modal="true"
  >
    <template #header-extra>
      <NFlex>
        <NButton type="primary" size="small" @click="handleAddUser">
          <template #icon>
            <CAIcon icon="tabler:user-plus" />
          </template>
          添加用户
        </NButton>
        <!-- <NButton type="primary" size="small" @click="handleImportUser">
          <template #icon>
            <CAIcon icon="tabler:upload" />
          </template>
          导入用户
        </NButton> -->
      </NFlex>
    </template>

    <!-- 用户表格 -->
    <NDataTable
      :loading="tableLoading"
      :data="tableData"
      :columns="tableColumns"
      :pagination="tablePagination"
      :scroll-x="800"
      :max-height="tableMaxHeight"
      striped
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
    />
  </NModal>

  <!-- 添加用户弹窗 -->
  <AddUserModal
    v-model:show="showAddUserModal"
    :tenant-id="props.data?.id ? Number(props.data.id) : null"
    :exclude-users="existingUsernames"
    @confirm="handleAddUsersConfirm"
  />
</template>

<script setup lang="ts">
  import type { TenantInfoExpanded } from "-/tenant";
  import type { UserListItem } from "-/user";
  import type { DataTableColumns } from "naive-ui";
  import { CAIcon } from "@celeris/components";
  import { NButton, NDataTable, NFlex, NModal, NTag, useMessage } from "naive-ui";
  import { addUserToTenantApi, getTenantUsersApi, removeUserFromTenantApi } from "~/apis/internal/tenant";
  import { useTable } from "~/composables/useTable";
  import AddUserModal from "./AddUserModal.vue";

  defineOptions({
    name: "TenantUserManageModal",
  });

  const props = withDefaults(defineProps<Props>(), {
    show: false,
    data: null,
  });

  const emit = defineEmits<Emits>();
  const dialog = useDialog();

  interface Props {
    show: boolean;
    data: TenantInfoExpanded | null;
  }

  interface Emits {
    (e: "update:show", value: boolean): void;
  }

  interface UserInfo {
    id: string;
    name: string;
    username: string;
    email: string;
    phone: string;
    status: number;
    key?: string;
  }

  const message = useMessage();

  // 响应式数据
  const showModal = computed({
    get: () => props.show,
    set: value => emit("update:show", value),
  });

  // 添加用户弹窗相关
  const showAddUserModal = ref(false);

  const modalTitle = computed(() => {
    return props.data ? `${props.data.name} - 用户管理` : "用户管理";
  });

  // 响应式窗口高度
  const windowHeight = ref(window.innerHeight);

  // 计算表格最大高度，基于屏幕高度自适应
  const tableMaxHeight = computed(() => {
    // 屏幕高度的60%，最小400px，最大800px
    const calculatedHeight = Math.floor(windowHeight.value * 0.6);
    return Math.max(400, Math.min(800, calculatedHeight));
  });

  // 监听窗口大小变化
  function handleResize() {
    windowHeight.value = window.innerHeight;
  }

  onMounted(() => {
    window.addEventListener("resize", handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener("resize", handleResize);
  });

  // 使用 useTable 钩子
  const {
    tableLoading,
    tableData,
    tablePagination,
    loadData,
    handlePageChange,
    handlePageSizeChange,
  } = useTable<{ id?: string }, UserInfo, UserInfo>();

  // 计算已存在的用户名列表，用于排除已添加的用户
  const existingUsernames = computed(() => {
    return tableData.value.map(user => user.username);
  });

  // 表格列配置
  const tableColumns: DataTableColumns = [
    {
      title: "姓名",
      key: "nickname",
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "账号",
      key: "username",
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "邮箱",
      key: "email",
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "手机",
      key: "telephone",
      width: 140,
    },
    {
      title: "状态",
      key: "status",
      width: 100,
      render(row: any) {
        return h(NTag, {
          type: !row.status ? "success" : "error",
        }, {
          default: () => !row.status ? "正常" : "禁用",
        });
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 150,
      render(row: any) {
        return h(NFlex, { size: "small" }, {
          default: () => [
            h(NButton, {
              text: true,
              type: "error",
              size: "small",
              onClick: () => handleRemoveUser(row),
            }, { default: () => "移除" }),
          ],
        });
      },
    },
  ];

  // 事件处理函数
  function handleAddUser() {
    showAddUserModal.value = true;
  }

  function handleImportUser() {
    message.info("导入用户功能待实现");
  }

  function handleRemoveUser(row: any) {
    if (!props.data?.id) {
      message.error("租户信息不完整");
      return;
    }
    dialog.warning({
      title: "移除确认",
      content: `确认要从租户中移除用户 "${row.nickname}" 吗？`,
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          await removeUserFromTenantApi(row.id, props.data!.id!);
          message.success("用户移除成功");
          // 重新加载表格数据
          await loadTableData();
        } catch (error) {
          console.error("移除用户失败:", error);
          message.error("移除用户失败，请重试");
        }
      },
    });
  }

  // 添加用户弹窗相关函数
  async function handleAddUsersConfirm(users: UserListItem[]) {
    try {
      if (!props.data?.id) {
        message.error("租户信息不完整");
        return;
      }

      // 批量添加用户到租户
      const params = {
        id: props.data!.id!,
        user_list: users.map(user => user.username),
      };
      await addUserToTenantApi(params);

      message.success(`成功添加 ${users.length} 个用户`);
      showAddUserModal.value = false;

      // 重新加载表格数据
      await loadTableData();
    } catch (error) {
      console.error("添加用户失败:", error);
      message.error("添加用户失败，请重试");
    }
  }
  async function loadTableData() {
    await loadData({
      getQueryCriteria: () => (
        props.data ? { id: props.data.id } : {}
      ),
      withPagination: true,
      dataPath: "value",

      tableRequest: getTenantUsersApi as any,
      handleTableData: (data: UserInfo[]) => {
        return data.map((item: UserInfo) => ({
          ...item,
        })) as UserInfo[];
      },
    });
  }

  // 监听弹框显示状态，加载数据
  watch(() => props.show, (newShow) => {
    if (newShow && props.data) {
      // 使用 useTable 的 loadData 方法加载数据
      loadTableData();
    }
  });
</script>

<style scoped>
/* 自定义样式 */
:deep(.n-card-header) {
  padding-bottom: 16px;
  border-bottom: 1px solid var(--n-border-color);
}

:deep(.n-data-table) {
  --n-th-padding: 12px 16px;
  --n-td-padding: 12px 16px;
}
</style>
