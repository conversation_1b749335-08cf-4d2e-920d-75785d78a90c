import type { AddModelResultParam, BehaviorParam, GetActionDetailParam, GetModelResultParam } from "-/behavior";
import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";

enum API {
  Behaviors = "/v2/risk_detection/behavior/list",
  GetActionDetailList = "/v2/risk_detection/behavior/user",
  ModelResult = "/v2/risk_detection/behavior/analysis",
}

/**
 * 获取用户行为分析列表
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function getUserBehaviors(params: BehaviorParam = {}, errorMessageMode: MessageMode = "message") {
  return request.get({ url: API.Behaviors, params }, { errorMessageMode, shouldTransformResponse: false });
}

/**
 * 获取用户行为详情列表
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function getActionDetailList(params: GetActionDetailParam = {}, errorMessageMode: MessageMode = "message") {
  return request.get({ url: API.GetActionDetailList, params }, { errorMessageMode });
}

/**
 * 新增大模型解读结果
 * @param data - 新增参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function addModelResult(data: AddModelResultParam, errorMessageMode: MessageMode = "message") {
  return request.post({ url: API.ModelResult, data }, { errorMessageMode });
}

/**
 * 获取大模型解读结果列表
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function getModelResultList(params: GetModelResultParam = {}, errorMessageMode: MessageMode = "message") {
  return request.get({ url: API.ModelResult, params }, { errorMessageMode, shouldTransformResponse: false });
}

/**
 * 删除大模型解读结果
 * @param data - 包含待删除结果的 id
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function deleteModelResult(data: { id: string }, errorMessageMode: MessageMode = "message") {
  return request.delete({ url: API.ModelResult, data }, { errorMessageMode });
}
