import type { TreeItem } from "-/common";
import type { LoginParam } from "-/login";
import type { TenantInfo } from "-/tenant";
import type { UserInfo } from "-/user";
import { PermissionCacheTypeConstants } from "@celeris/constants";
import { getUserInfo, login } from "~/apis/internal/login";
import { allTeamTreeApi } from "~/apis/internal/team";
import { switchTenantApi } from "~/apis/internal/tenant";
import { router } from "~/router";
import { TOKEN_KEY, TOKEN_KEY_SIDE } from "~/router/constant";
import { removeRouteChangeListener } from "~/router/mitt/routeChange";
import { DEFAULT_PROJECT_SETTING } from "~/setting/projectSetting";
import { useAppStore } from "~/store/modules/app";
import { usePermissionStore } from "~/store/modules/permission";
import { useTabsStore } from "~/store/modules/tabs";
import { APP_USER_STORE_ID } from "../constants";

// 定义用户状态接口
interface UserState {
  // 用户基本信息
  userInfo: UserInfo;
  username: string;
  nickname: string;
  token: string;
  // 扩展字段，参照 user.ts 的结构
  // expiresAt: number;
  loginMode: string;
  // 组织树
  allUserTree: TreeItem[];
  allUserTreeLoading: boolean;
  // 租户相关
  currentTenant: TenantInfo | null;
  availableTenants: TenantInfo[];
  availableTenantsLoading: boolean;
}

const cookies = useCookies([], { autoUpdateDependencies: true });

let fetchUserInfoPromise: Promise<UserInfo> | null = null;
let fetchAllUserTreePromise: Promise<TreeItem[]> | null = null;

// 重置所有状态
function resetStores(this: any) {
  const appStore = useAppStore();
  const permissionStore = usePermissionStore();
  const tabsStore = useTabsStore();
  appStore.resetAPPState();
  permissionStore.resetPermissionState();
  tabsStore.resetTabsState();
  this.resetUserState();
  removeRouteChangeListener();
}

export const useUserStore = defineStore({
  id: APP_USER_STORE_ID,
  persist: {
    storage: DEFAULT_PROJECT_SETTING.permissionCacheType === PermissionCacheTypeConstants.LOCAL_STORAGE ? localStorage : sessionStorage,
    omit: ["allUserTree", "allUserTreeLoading", "availableTenantsLoading"],
  },
  state: (): UserState => ({
    userInfo: {
      state: 0,
      id: null,
      username: "",
      nickname: "",
      email: "",
      telephone: "",
      tenant: [],
    },
    username: "",
    nickname: "",
    token: "", // 用户唯一标识token

    // expiresAt: -1,
    loginMode: "",
    allUserTree: [],
    allUserTreeLoading: false,
    // 租户相关
    currentTenant: null,
    availableTenants: [],
    availableTenantsLoading: false,
  }),
  getters: {
    // 获取用户信息
    getUserInfo(state): UserInfo {
      return state.userInfo;
    },
    // 获取用户名
    getUsername(state): string {
      return state.username;
    },
    // 获取昵称
    getNickName(state): string {
      return state.nickname;
    },
    // 获取 token
    getToken(state): string {
      // http-only 属性使得前端无法获取 cookie
      /*
      const cookieToken = cookies.get(TOKEN_KEY);
      const ramToken = state.token;
      const token = cookieToken || ramToken;
      if (cookieToken && !ramToken) {
        state.token = cookieToken;
      }
      return token;
      */
      return state.token;
    },
    // 获取过期时间
    // getExpiresAt(state): number {
    //   return state.expiresAt;
    // },
    // 获取登录模式
    getLoginMode(state): string {
      return state.loginMode;
    },
    // 是否已登录
    isLoggedIn(state): boolean {
      return !!this.getToken;
    },
    // 是否有用户信息
    hasUserInfo(state): boolean {
      return !!state.userInfo && !!state.userInfo.username;
    },
    getAllUserTree(state): TreeItem[] {
      return state.allUserTree;
    },
    getAllUserTreeLoading(state): boolean {
      return state.allUserTreeLoading;
    },
    // 获取当前租户
    getCurrentTenant(state): TenantInfo | null {
      return state.currentTenant;
    },
    // 获取可用租户列表
    getAvailableTenants(state): TenantInfo[] {
      return state.availableTenants;
    },
    // 获取可用租户加载状态
    getAvailableTenantsLoading(state): boolean {
      return state.availableTenantsLoading;
    },
  },
  actions: {
    // 设置用户信息
    setUserInfo(userInfo?: UserInfo) {
      this.userInfo = userInfo || {
        state: 0,
        id: null,
        username: "",
        nickname: "",
        email: "",
        telephone: "",
        tenant: [],
      };
    },
    // 设置用户名
    setUserName(userName?: string) {
      this.username = userName || "";
    },
    // 设置昵称
    setNickName(nickName?: string) {
      this.nickname = nickName || "";
    },
    // 设置 token
    setToken(token?: string) {
      this.token = token || "";
      // setCookieToken(token || '');
    },
    // 设置过期时间
    // setExpiresAt(expiresAt?: number) {
    //   expiresAt = Number(expiresAt);
    //   this.expiresAt = !Number.isNaN(expiresAt) && expiresAt >= 0 ? expiresAt : -1;
    // },
    // 设置登录模式
    setLoginMode(loginMode?: string) {
      this.loginMode = loginMode || "";
    },
    setAllUserTree(tree?: TreeItem[]) {
      this.allUserTree = Array.isArray(tree) ? tree : [];
    },
    setAllUserTreeLoading(loading?: boolean) {
      this.allUserTreeLoading = !!loading;
    },
    // 设置当前租户
    setCurrentTenant(tenant?: TenantInfo | null) {
      this.currentTenant = tenant || null;
    },
    // 设置可用租户列表
    setAvailableTenants(tenants?: TenantInfo[]) {
      this.availableTenants = Array.isArray(tenants) ? tenants : [];
    },
    // 设置可用租户加载状态
    setAvailableTenantsLoading(loading?: boolean) {
      this.availableTenantsLoading = !!loading;
    },
    // 获取用户信息
    async fetchUserInfo(refresh: boolean = false): Promise<UserInfo> {
      if (!refresh && this.hasUserInfo) {
        return this.userInfo as UserInfo;
      }
      if (refresh) {
        fetchUserInfoPromise = null;
      }
      try {
        fetchUserInfoPromise = fetchUserInfoPromise || getUserInfo();
        const res = await fetchUserInfoPromise;
        fetchUserInfoPromise = null;
        this.setUserInfo(res);
        this.setNickName(res.nickname);
        this.setUserName(res.username);
        // this.setToken(res.token);
        return this.userInfo as UserInfo;
      } catch (error) {
        console.error("获取用户信息失败:", error);
        throw error;
      }
    },
    // 登录
    async login(data: LoginParam) {
      return login(data);
    },
    // 退出登录
    async logout() {
      resetStores.call(this);
      localStorage.clear();
      cookies.remove(TOKEN_KEY_SIDE);

      // 跳到登录页
      router.push("/login");
      // if (window.location.host.indexOf("localhost") === 0) {
      //   router.push("/login");
      // } else {
      //   // 使用环境变量或默认值
      //   const logoutUrl = import.meta.env.VITE_APP_END_SESSION_ENDPOINT;
      //   window.location.href = logoutUrl;
      // }
    },
    /**
     * 获取所有用户组织树数据
     *
     * 本函数负责从API获取所有用户的数据，并以树状结构返回在首次加载时或强制重新加载时，
     * 会发起网络请求，并缓存结果以提高性能
     *
     * @param reload 是否强制重新从API加载数据，默认为false
     * @returns 返回一个Promise对象，解析为组织树状结构数据
     */
    fetchAllUserTree(reload: boolean = false) {
      const loadData = () => {
        this.setAllUserTreeLoading(true);
        fetchAllUserTreePromise = allTeamTreeApi();
        return fetchAllUserTreePromise
          .then((res) => {
            this.setAllUserTree(res);
          })
          .finally(() => {
            this.setAllUserTreeLoading(false);
            nextTick(() => fetchAllUserTreePromise = null);
          });
      };

      if (this.getAllUserTree.length > 0) {
        if (reload) {
          fetchAllUserTreePromise = null;
          return loadData();
        }
        return this.getAllUserTree;
      }

      if (fetchAllUserTreePromise) {
        return fetchAllUserTreePromise;
      }

      return loadData();
    },
    // 获取用户可用租户列表
    async fetchAvailableTenants(reload: boolean = false, refreshUserInfo: boolean = false): Promise<TenantInfo[]> {
      if (!reload && this.availableTenants.length > 0) {
        return this.availableTenants;
      }

      if (refreshUserInfo) {
        await this.fetchUserInfo(true);
      }

      try {
        this.setAvailableTenantsLoading(true);
        // 从用户信息中获取租户列表
        const tenants = this.userInfo?.tenant || [];
        this.setAvailableTenants(tenants);

        // 如果没有当前租户且有可用租户，优先设置默认租户为当前租户
        if (!this.getCurrentTenant && tenants.length > 0) {
          const defaultTenant = tenants.find((t: TenantInfo) => t.is_default);
          this.setCurrentTenant(defaultTenant);
        }

        return tenants;
      } catch (error) {
        console.error("获取租户列表失败:", error);
        this.setAvailableTenants([]);
        throw error;
      } finally {
        this.setAvailableTenantsLoading(false);
      }
    },
    // 切换租户
    async switchTenant(tenantId: string): Promise<void> {
      // 支持通过 id 或 tenant_id 查找租户
      const tenant = this.availableTenants.find(t => t.id === tenantId || t.tenant_id === tenantId);
      if (!tenant) {
        throw new Error(`租户 ${tenantId} 不存在`);
      }

      // 调用切换租户API，使用 tenant_id 字段
      // const apiTenantId = tenant.tenant_id || tenant.id!;
      // await switchTenantApi(apiTenantId);

      this.setCurrentTenant(tenant);

      // 切换租户后可能需要重新获取用户权限等信息
      // 这里可以触发其他相关的状态更新
    },
    // 重置用户状态
    resetUserState() {
      this.setUserInfo();
      this.setUserName();
      this.setNickName();
      this.setToken();
      // this.setExpiresAt();
      this.setLoginMode();
      this.setAllUserTree();
      this.setCurrentTenant();
      this.setAvailableTenants();
    },
  },
});

// 设置token
function setCookieToken(token: string) {
  if (token) {
    cookies.set(TOKEN_KEY, token);
  } else {
    cookies.remove(TOKEN_KEY);
  }
}

// 需要在设置之外使用的函数
export function useUserStoreWithOut() {
  return useUserStore(store);
}
