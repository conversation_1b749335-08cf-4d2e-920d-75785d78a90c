import type { MockMethod } from "vite-plugin-mock";

// 模拟用户可用的租户列表（通常比全部租户列表少）
const userTenants = [
];

export default [
  {
    url: "/api/v2/team/tenantsQuery",
    method: "get",
    response: () => {
      return {
        code: 0,
        data: [
            {
                "id": "1",
                "name": "阿里云租户",
                "admin": "张三",
                "tenant_id": "tenant_001",
                "status": "active",
                "create_time": "2024-01-15T08:30:00Z",
                "update_time": "2024-01-20T10:15:00Z"
              },
              {
                "id": "2",
                "name": "腾讯云租户",
                "admin": "李四",
                "tenant_id": "tenant_002",
                "status": "inactive",
                "create_time": "2024-01-10T14:20:00Z",
                "update_time": "2024-01-18T16:45:00Z"
              },
                          {
                "id": "3",
                "name": "阿里云租户",
                "admin": "张三",
                "tenant_id": "tenant_001",
                "status": "active",
                "create_time": "2024-01-15T08:30:00Z",
                "update_time": "2024-01-20T10:15:00Z"
              },
              {
                "id": "4",
                "name": "腾讯云租户",
                "admin": "李四",
                "tenant_id": "tenant_002",
                "status": "inactive",
                "create_time": "2024-01-10T14:20:00Z",
                "update_time": "2024-01-18T16:45:00Z"
              },
            ],
        message: "mock-数据获取成功",
      };
    },
  },
  {
    url: "/api/v2/team/ApprovelQuery",
    method: "get",
    response: () => {
      return {
        code: 0,
        data: [
            {
                "id": "1",
                "applicant": "张三",
                "tenant_name": "阿里云租户",
                "application_type": "create",
                "apply_time": "2024-01-15T08:30:00Z",
                "status": "pending",
                "approve_time": "2024-01-15T08:30:00Z",
                "approver": "管理员",
                "reason": "无",
                "description": "申请创建新租户"
              },
              {
                "id": "2",
                "applicant": "李四",
                "tenant_name": "腾讯云租户",
                "application_type": "update",
                "apply_time": "2024-01-10T14:20:00Z",
                "status": "approved",
                "approve_time": "2024-01-10T14:20:00Z",
                "approver": "管理员",
                "reason": "无",
                "description": "申请更新租户信息"
              },
              {
                "id": "3",
                "applicant": "王五",
                "tenant_name": "百度云租户",
                "application_type": "delete",
                "apply_time": "2024-01-05T09:10:00Z",
                "status": "rejected",
                "approve_time": "2024-01-05T09:10:00Z",
                "approver": "管理员",
                "reason": "无",
                "description": "申请删除租户"
              },
              {
                "id": "4",
                "applicant": "赵六",
                "tenant_name": "华为云租户",
                "application_type": "create",
                "apply_time": "2024-01-20T11:20:00Z",
                "status": "pending",
                "approve_time": "2024-01-20T11:20:00Z",
                "approver": "管理员",
                "reason": "无",
                "description": "申请创建新租户"
              },
            ],
        message: "mock-数据获取成功",
      };
    },
  },
  // 获取用户可用租户列表
  {
    url: "/api/v2/user/tenants",
    method: "get",
    response: () => {
      return {
        code: 0,
        data: userTenants,
        message: "获取用户租户列表成功",
      };
    },
  },
  // 获取租户用户列表
  {
    url: "/api/v1/tenant/users",
    method: "get",
    response: () => {
      return {
        code: 0,
        data: [
            {
                "id": "1",
                "name": "张三",
                "username": "zhangsan",
                "email": "<EMAIL>",
                "phone": "13800138001",
                "status": 1,
              },
              {
                "id": "2",
                "name": "李四",
                "username": "lisi",
                "email": "<EMAIL>",
                "phone": "13800138002",
                "status": 1,
              },
              {
                "id": "3",
                "name": "王五",
                "username": "wangwu",
                "email": "<EMAIL>",
                "phone": "13800138003",
                "status": 0,
              },
              {
                "id": "4",
                "name": "赵六",
                "username": "zhaoliu",
                "email": "<EMAIL>",
                "phone": "13800138004",
                "status": 1,
              },
              {
                "id": "5",
                "name": "周七",
                "username": "zhouqi",
                "email": "<EMAIL>",
                "phone": "13800138005",
                "status": 1,
              },
              {
                "id": "6",
                "name": "吴八",
                "username": "wuba",
                "email": "<EMAIL>", 
                "phone": "13800138006",
                "status": 1,
              },
              {
                "id": "7",
                "name": "郑九",
                "username": "zhengjiu",
                "email": "<EMAIL>",
                "phone": "13800138007",
                "status": 1,
              },
              {
                "id": "8",
                "name": "王十",
                "username": "wangshi",
                "email": "<EMAIL>",
                "phone": "13800138008",
                "status": 1,
              },
              {
                "id": "9",
                "name": "张十一",
                "username": "zhangshiyi",
                "email": "<EMAIL>",
                "phone": "13800138009",
                "status": 1,
              },
              {
                "id": "10",
                "name": "李十二",
                "username": "lisiyisan",
                "email": "<EMAIL>",
                "phone": "13800138010",
                "status": 1,
              },
              {
                "id": "11",
                "name": "王十三",
                "username": "wangshisan",
                "email": "<EMAIL>",
                "phone": "13800138011",
                "status": 1,
              },
              {
                "id": "12",
                "name": "赵十四",
                "username": "zhaosishi",
                "email": "<EMAIL>",
                "phone": "13800138012",
                "status": 1,
              },
            ],
        message: "mock-数据获取成功",
      };
    },
  },
] as MockMethod[];
