import type { HeaderSetting, MenuSetting, ProjectSetting, TransitionSetting } from "@celeris/types";
import type { DeepPartial } from "unocss";
import { deepMerge } from "@celeris/utils";
import { resetRouter } from "~/router";
import { DEFAULT_PROJECT_SETTING } from "~/setting/projectSetting";
import { APP_STORE_ID } from "../constants";

interface AppState {
  // project config
  projectSetting: ProjectSetting;
  // Page loading status
  pageLoading: boolean;
}

let pageLoadingTimeout: ReturnType<typeof setTimeout>;
export const useAppStore = defineStore({
  id: APP_STORE_ID,
  persist: {},
  state: (): AppState => ({
    projectSetting: DEFAULT_PROJECT_SETTING,
    pageLoading: false,
  }),
  getters: {
    // 获取页面加载状态
    getPageLoading(state): boolean {
      return state.pageLoading;
    },

    // 获取项目配置
    getProjectSetting(state): ProjectSetting {
      return state.projectSetting || ({} as ProjectSetting);
    },

    // 获取菜单配置
    getMenuSetting(): MenuSetting {
      return this.getProjectSetting.menuSetting;
    },

    // 获取头部配置
    getHeaderSetting(): HeaderSetting {
      return this.getProjectSetting.headerSetting;
    },

    // 获取动画配置
    getTransitionSetting(): TransitionSetting {
      return this.getProjectSetting.transitionSetting;
    },

  },
  actions: {
    // 设置页面加载状态
    setPageLoading(loading: boolean): void {
      this.pageLoading = loading;
    },

    // 设置项目配置
    setProjectSetting(config: DeepPartial<ProjectSetting>): void {
      this.projectSetting = deepMerge(this.projectSetting || {}, config);
    },

    // 设置菜单配置
    setMenuSetting(menuSetting: Partial<MenuSetting>): void {
      this.setProjectSetting({ menuSetting });
    },

    // 设置头部配置
    setHeaderSetting(headerSetting: Partial<HeaderSetting>): void {
      this.setProjectSetting({ headerSetting });
    },

    // 设置动画配置
    setTransitionSetting(transitionSetting: Partial<TransitionSetting>): void {
      this.setProjectSetting({ transitionSetting });
    },

    // 设置页面加载状态
    setPageLoadingAction(loading: boolean) {
      clearTimeout(pageLoadingTimeout);
      if (loading) {
        // Prevent flicker by delaying the setPageLoading call
        pageLoadingTimeout = setTimeout(() => {
          this.setPageLoading(loading);
        }, 50);
      } else {
        this.setPageLoading(loading);
      }
    },

    // 重置状态
    resetAPPState() {
      resetRouter();
      this.setProjectSetting(DEFAULT_PROJECT_SETTING);
    },
  },
});

// Need to be used outside the setup
export function useAppStoreWithOut() {
  return useAppStore(store);
}
