import CryptoJS from "crypto-js";

const { AES, enc, pad, mode, MD5, SHA256, SHA512 } = CryptoJS;

// Define an interface for encryption
// 定义一个加密器的接口
export interface Encryption {
  encrypt: (plainText: string) => string;
  decrypt: (cipherText: string) => string;
}

// Define an interface for Hashing
// 定义一个哈希算法的接口
export interface Hashing {
  hash: (data: string) => string;
}

export interface EncryptionParams {
  key: string;
  iv: string;
}

class AesEncryption implements Encryption {
  private readonly key;
  private readonly iv;

  /*
   * AES encryption
   * AES加密
   * @param key 密钥
   * @param iv 初始化向量
   */
  constructor({ key, iv }: EncryptionParams) {
    this.key = enc.Utf8.parse(key);
    this.iv = enc.Utf8.parse(iv);
  }

  /*
   * 获取加密选项
   * Get the encryption options
   */
  get getOptions() {
    return {
      mode: mode.CBC,
      padding: pad.Pkcs7,
      iv: this.iv,
    };
  }

  /*
   * 加密
   * Encrypt
   * @param plainText 明文
   * @returns 密文
   */
  encrypt(plainText: string) {
    return AES.encrypt(plainText, this.key, this.getOptions).toString();
  }

  /*
   * 解密
   * Decrypt
   * @param cipherText 密文
   * @returns 明文
   */
  decrypt(cipherText: string) {
    return AES.decrypt(cipherText, this.key, this.getOptions).toString(enc.Utf8);
  }
}

// Define a singleton class for Base64 encryption
class Base64Encryption implements Encryption {
  private static instance: Base64Encryption;

  // Private constructor to prevent instantiation from outside
  private constructor() {}

  // Get the singleton instance
  // 获取单例实例
  public static getInstance(): Base64Encryption {
    if (!Base64Encryption.instance) {
      Base64Encryption.instance = new Base64Encryption();
    }
    return Base64Encryption.instance;
  }

  // Encrypt
  // 加密
  encrypt(plainText: string) {
    return enc.Utf8.parse(plainText).toString(enc.Base64);
  }

  // Decrypt
  // 解密
  decrypt(cipherText: string) {
    return enc.Base64.parse(cipherText).toString(enc.Utf8);
  }
}

// Define a singleton class for MD5 Hashing
class MD5Hashing implements Hashing {
  private static instance: MD5Hashing;

  // Private constructor to prevent instantiation from outside
  private constructor() {}

  // Get the singleton instance
  // 获取单例实例
  public static getInstance(): MD5Hashing {
    if (!MD5Hashing.instance) {
      MD5Hashing.instance = new MD5Hashing();
    }
    return MD5Hashing.instance;
  }

  hash(plainText: string) {
    return MD5(plainText).toString();
  }
}

// Define a singleton class for SHA256 Hashing
class SHA256Hashing implements Hashing {
  private static instance: SHA256Hashing;

  // Private constructor to prevent instantiation from outside
  private constructor() {}

  // Get the singleton instance
  // 获取单例实例
  public static getInstance(): SHA256Hashing {
    if (!SHA256Hashing.instance) {
      SHA256Hashing.instance = new SHA256Hashing();
    }
    return SHA256Hashing.instance;
  }

  // Hash
  hash(plainText: string) {
    return SHA256(plainText).toString();
  }
}

// Define a singleton class for SHA512 Hashing
class SHA512Hashing implements Hashing {
  private static instance: SHA512Hashing;

  private constructor() {}

  // Get the singleton instance
  // 获取单例实例
  public static getInstance(): SHA512Hashing {
    if (!SHA512Hashing.instance) {
      SHA512Hashing.instance = new SHA512Hashing();
    }
    return SHA512Hashing.instance;
  }

  // Hash
  hash(plainText: string) {
    return SHA512(plainText).toString();
  }
}

export class EncryptionFactory {
  /*
   * 创建AES加密器
   * Create AES encryption
   * @param params 加密参数
   * @returns 加密器
   */
  public static createAesEncryption(params: EncryptionParams): Encryption {
    return new AesEncryption(params);
  }

  /*
   * 创建Base64加密器
   * Create Base64 encryption
   * @returns 加密器
   */
  public static createBase64Encryption(): Encryption {
    return Base64Encryption.getInstance();
  }
}

export class HashingFactory {
  /*
   * 创建MD5哈希算法
   * Create MD5 hashing
   * @returns 哈希算法
   */
  public static createMD5Hashing(): Hashing {
    return MD5Hashing.getInstance();
  }

  /*
   * 创建SHA256哈希算法
   * Create SHA256 hashing
   * @returns 哈希算法
   */
  public static createSHA256Hashing(): Hashing {
    return SHA256Hashing.getInstance();
  }

  /*
   * 创建SHA512哈希算法
   * Create SHA512 hashing
   * @returns 哈希算法
   */
  public static createSHA512Hashing(): Hashing {
    return SHA512Hashing.getInstance();
  }
}
