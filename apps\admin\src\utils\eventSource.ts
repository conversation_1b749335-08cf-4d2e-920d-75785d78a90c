import type { Ref } from "vue";

interface EventSourceOptions {
  url: string;
  onMessage?: (data: any) => void;
  onError?: (error: any) => void;
  onClose?: () => void;
  parseJSON?: boolean;
}

interface ModelEventSourceCallbacks {
  onContent?: (content: string) => void;
  onError?: (error: any) => void;
  onFinish?: () => void;
}

interface ModelEventSourceReturn {
  connect: () => void;
  disconnect: () => void;
  isConnected: Ref<boolean>;
}

class EventSourceHelper {
  private eventSource: EventSource | null = null;
  private isConnected = ref<boolean>(false);

  constructor(private options: EventSourceOptions) {}

  connect() {
    if (this.eventSource) {
      this.disconnect();
    }

    this.eventSource = new EventSource(this.options.url);
    this.isConnected.value = true;

    this.eventSource.onmessage = (event) => {
      try {
        const data = this.options.parseJSON ? JSON.parse(event.data) : event.data;
        this.options.onMessage?.(data);
      } catch (error) {
        this.options.onError?.(error);
      }
    };

    this.eventSource.onerror = (error) => {
      this.options.onError?.(error);
      this.disconnect();
    };
  }

  disconnect() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
      this.isConnected.value = false;
      this.options.onClose?.();
    }
  }

  getConnectionStatus() {
    return this.isConnected;
  }
}

// 创建用于大模型接口的专用方法 - Vue 3 版本
export function createModelEventSource(url: string, callbacks: ModelEventSourceCallbacks): ModelEventSourceReturn {
  const helper = new EventSourceHelper({
    url,
    parseJSON: true,
    onMessage: (data) => {
      if (data.event === "message") {
        callbacks.onContent?.(data.answer);
      } else if (data.event === "message_end") {
        callbacks.onFinish?.();
        // 关闭连接
        helper.disconnect();
      } else {
        console.error("error event", data);
        callbacks.onError?.(data);
      }
    },
    onError: callbacks.onError,
    onClose: callbacks.onFinish,
  });

  // 组件销毁时自动清理
  onBeforeUnmount(() => {
    helper.disconnect();
  });

  return {
    connect: () => helper.connect(),
    disconnect: () => helper.disconnect(),
    isConnected: helper.getConnectionStatus(),
  };
}
