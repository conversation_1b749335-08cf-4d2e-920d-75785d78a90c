import type { Dayjs } from "dayjs";
import dayjs from "dayjs";

// Date-time and date format constants
export const DATE_TIME_FORMAT = "YYYY-MM-DD HH:mm:ss";
export const DATE_FORMAT = "YYYY-MM-DD";

/**
 * Formats a date to a specific date-time format.
 * @param date - The date to be formatted.
 * @param format - The desired date-time format.
 * @returns The formatted date-time string.
 */
export function formatToDateTime(date?: dayjs.ConfigType, format = DATE_TIME_FORMAT): string {
  return dayjs(date).format(format);
}

/**
 * Formats a date to a specific date format.
 * @param date - The date to be formatted.
 * @param format - The desired date format.
 * @returns The formatted date string.
 */
export function formatToDate(date?: dayjs.ConfigType, format = DATE_FORMAT): string {
  return dayjs(date).format(format);
}

/**
 * 格式化日期为指定格式
 * @param {Dayjs | Date | string | number} date - 当前日期
 * @param {string} formatType - 目标格式，例如 YYYY-Q, YYYY-M, YYYY-M-W
 * @returns {string} 格式化后的字符串
 */
export function formatDateToQMW(date: Dayjs | Date | string | number, formatType: string): string {
  const d = dayjs(date);
  const year = d.year();
  const month = d.month() + 1; // 月份从 0 开始，需加 1

  if (formatType === "YYYY-Q") {
    // 年-季度
    const quarter = Math.ceil(month / 3);
    return `${year}-Q${quarter}`;
  } else if (formatType === "YYYY-M") {
    // 年-月份
    return `${year}-M${month}`;
  } else if (formatType === "YYYY-M-W") {
    // 年-月份-周
    // 找到日期所在周的周一
    // @ts-expect-error 这个是误报
    const weekStart = d.startOf("isoWeek");
    const weekStartYear = weekStart.year();
    const weekStartMonth = weekStart.month() + 1;

    // 找到该月 1 号
    const firstDay = dayjs(`${weekStartYear}-${weekStartMonth}-01`);
    const firstDayOfWeek = firstDay.day(); // 0: 周日, 1: 周一, ..., 6: 周六

    // 计算该月第一个周一
    let firstMonday;
    if (firstDayOfWeek === 1) {
      firstMonday = firstDay; // 1 号是周一
    } else {
      // 1 号后的第一个周一
      const daysToAdd = (1 - firstDayOfWeek + 7) % 7;
      firstMonday = firstDay.add(daysToAdd, "day");
    }

    // 计算周数
    const daysDiff = weekStart.diff(firstMonday, "day");
    const weekNumber = Math.floor(daysDiff / 7) + 1;

    return `${weekStartYear}-M${weekStartMonth}-W${weekNumber}`;
  } else {
    throw new Error("无效的格式类型，仅支持 YYYY-Q, YYYY-M, YYYY-M-W");
  }
}

export const dateUtil = dayjs;
