import type { AxiosError } from "axios";
import { createDiscreteApi } from "@celeris/ca-components";
import { HttpStatusConstants, ResultConstants } from "@celeris/constants";
import { LocalesEngine } from "@celeris/locale";
import { HttpRequestEngine } from "@celeris/request";
import { field, logger } from "@celeris/utils";
import { useAppSetting, useNaiveUIConfigProvider } from "~/composables";
import { useUserStoreWithOut } from "~/store/modules/user";

// 初始化 HttpRequestEngine
function initializeHttpRequest() {
  const { configProviderProps } = useNaiveUIConfigProvider();

  const { message: _message, notification, dialog } = createDiscreteApi(
    ["message", "dialog", "notification", "loadingBar"],
    {
      configProviderProps,
    },
  );
  HttpRequestEngine.initRequest(() => ({
    getToken: () => {
      const userStore = useUserStoreWithOut();
      return userStore.getToken;
    },
    getCurrentTenant: () => {
      const userStore = useUserStoreWithOut();
      return userStore.getCurrentTenant;
    },
  }));
  HttpRequestEngine.setTimeoutHandler(() => {
    const userStore = useUserStoreWithOut();
    userStore.logout();
  });
  // 401 处理
  HttpRequestEngine.setUnauthorizedHandler((error: any | AxiosError<any>) => {
    const status = error?.status;
    const code = error?.code;
    if (status !== HttpStatusConstants.Unauthorized && code !== ResultConstants.ERROR) {
      return;
    }

    const userStore = useUserStoreWithOut();
    userStore.logout(true);
  });
  HttpRequestEngine.setMessageHandler((message, mode) => {
    if (mode === "message") {
      _message.info(message);
    } else if (mode === "dialog") {
      dialog.info({ title: "Information", content: message });
    } else if (mode === "notification") {
      notification.info({ title: "Information", content: message });
    } else if (mode === undefined || mode === "none") {
      logger.info(`HttpRequestEngine MessageHandler: ${message}`);
    }
  });
  HttpRequestEngine.setSuccessMessageHandler((message, mode) => {
    if (mode === "message") {
      _message.success(message);
    } else if (mode === "dialog") {
      dialog.success({ title: "Success", content: message });
    } else if (mode === "notification") {
      notification.success({ title: "Success", content: message });
    } else if (mode === undefined || mode === "none") {
      logger.info(`HttpRequestEngine SuccessHandler: ${message}`);
    }
  });
  HttpRequestEngine.setErrorMessageHandler((message, mode) => {
    if (mode === "message") {
      _message.error(message);
    } else if (mode === "dialog") {
      dialog.error({ title: "Error", content: message });
    } else if (mode === "notification") {
      notification.error({ title: "Error", content: message });
    } else if (mode === undefined || mode === "none") {
      logger.error("HttpRequestEngine ErrorHandler", field("content:", message));
    }
  });
}
// 初始化 initializeI18n
function initializeI18n() {
  const { getLocale } = useAppSetting();
  const messages = Object.fromEntries(
    Object.entries(
      import.meta.glob<{ default: any }>("./locales/*.json", { eager: true }),
    ).map(([key, value]) => {
      return [key.slice(10, -5), value.default];
    }),
  );
  LocalesEngine.initLocales(() => ({
    locale: getLocale.value,
    fallbackLocale: "zh",
    messagesHandler: () => {
      return messages;
    },
    otherOptions: {
      sync: true,
      availableLocales: Object.keys(messages),
      silentTranslationWarn: true,
      missingWarn: false,
      silentFallbackWarn: true,
    },
  }));
}

/*
 * 初始化配置
 * Initialize configuration
 */
export function initializeConfiguration() {
  initializeHttpRequest();
  initializeI18n();
}
