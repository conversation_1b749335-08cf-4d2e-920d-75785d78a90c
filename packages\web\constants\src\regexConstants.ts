/**
 * [\w.%+-]{1,64}           # 本地部分（支持常见符号），1-64字符（RFC标准）
 * [a-zA-Z0-9-]{1,63}       # 主域名（支持连字符），1-63字符（域名规范）
 * (\.[a-zA-Z0-9-]{1,63})*  # 子域名（可选多个）
 * \.[a-zA-Z]{2,}           # 顶级域名（至少2字母）
 *
 * 邮箱
 * @type {RegExp}
 */
// eslint-disable-next-line regexp/use-ignore-case
export const EMAIL_REGEX = /^[\w.%+-]{1,64}@[a-zA-Z0-9-]{1,63}(\.[a-zA-Z0-9-]{1,63})*\.[a-zA-Z]{2,}$/;

/**
 * 中国大陆手机号
 * @type {RegExp}
 */
export const CHINESE_PHONE_REGEX = /^1(3\d|4[5-9]|5\d|66|7\d|8\d|9[89])\d{8}$/;

/**
 * 12-18位，包含大写字母、小写字母、数字、特殊符号四种类型，缺一不可
 * 特殊符号为：!@#$%^&*()
 * 密码
 * @type {RegExp}
 */
export const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()])[\w!@#$%^&*()]{12,18}$/;
