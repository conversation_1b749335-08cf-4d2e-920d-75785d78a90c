apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    run: {{DEPLOYMENT_NAME}}
  name: {{DEPLOYMENT_NAME}}
  namespace: {{NAMESPACE}}
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      run: {{DEPLOYMENT_NAME}}
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        run: {{DEPLOYMENT_NAME}}
    spec:
      dnsConfig:
        options:
          - name: single-request-reopen
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: zcxt
                    operator: In
                    values:
                      - "true"
      containers:
        - image: {{imageUrl}}
          imagePullPolicy: IfNotPresent
          name: {{DEPLOYMENT_NAME}}
      imagePullSecrets:
        - name: harboreg
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}

