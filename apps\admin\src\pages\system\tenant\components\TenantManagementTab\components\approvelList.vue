<template>
  <NFlex vertical size="large">
    <!-- 表格 -->
    <NDataTable
      class="fixed-right-table h-[calc(100vh-260px)] [&_.ca-data-table-resize-button::after]:(!bg-[var(--primary-color)])"
      scroll-x="min-content"
      remote
      flex-height
      :striped="true"
      :bordered="false"
      :single-line="false"
      :loading="tableLoading"
      :data="tableData"
      :columns="tableColumns"
      :pagination="tablePagination"
      @scroll="handleScroll"
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
    />

    <!-- 申请详情弹窗 -->
    <ApprovalDetailModal
      v-model:show="detailModalShow"
      :data="selectedApproval"
      @approve="handleModalApprove"
      @reject="handleModalReject"
      @cancel="handleModalCancel"
      @refresh="loadTableData"
    />
  </NFlex>
</template>

<script setup lang="ts">
  import type { ApprovalInfo, ApprovalInfoExpanded, QueryCriteria } from "-/tenant";
  import type { DataTableColumns } from "naive-ui";
  import { CAIcon } from "@celeris/components";
  import { formatToDate } from "@celeris/utils";
  import { cloneDeep } from "lodash-es";
  import { NButton, NDropdown, NFlex, NTag, useDialog, useMessage } from "naive-ui";
  import { approvelListApi } from "~/apis/internal/tenant";
  import { useTable } from "~/composables/useTable";
  import ApprovalDetailModal from "./ApprovalDetailModal.vue";

  const props = withDefaults(defineProps<{
    queryCriteria: QueryCriteria;
  }>(), {
    queryCriteria: () => ({}) as QueryCriteria,
  });

  // ========== 消息提示 ==========
  const message = useMessage();
  const dialog = useDialog();

  // ========== 注入触发器 ==========
  const searchTrigger = inject<Ref<number>>("searchTrigger");

  // ========== 弹窗相关 ==========
  const detailModalShow = ref(false);
  const selectedApproval = ref<ApprovalInfoExpanded | null>(null);

  // ========== 表格相关 ==========
  const {
    tableLoading,
    tableData,
    tablePagination,
    loadData,
    handlePageChange,
    handlePageSizeChange,
    handleScroll,
  } = useTable<QueryCriteria, ApprovalInfo, ApprovalInfoExpanded>();

  // ========== 表格列定义 ==========
  const tableColumns: DataTableColumns<ApprovalInfoExpanded> = [
    {
      title: "申请人",
      key: "applicant",
      width: 120,
      fixed: "left",
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "租户名称",
      key: "tenant_name",
      width: 180,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return h("span", row.tenant_name || "--");
      },
    },
    {
      title: "申请类型",
      key: "application_type",
      width: 120,
      render(row) {
        const typeMap = {
          create: "创建租户",
          update: "更新租户",
          delete: "删除租户",
          reset: "重置密码",
        } as const;

        const typeText = typeMap[row.application_type as keyof typeof typeMap] || row.application_type;
        return h("span", typeText);
      },
    },
    {
      title: "状态",
      key: "status",
      width: 100,
      render(row) {
        const status = row.status;
        if (!status) {
          return h("span", "--");
        }

        const statusMap = {
          pending: { type: "warning", text: "待审核" },
          approved: { type: "success", text: "已通过" },
          rejected: { type: "error", text: "已拒绝" },
          cancelled: { type: "info", text: "已取消" },
        } as const;

        const statusInfo = statusMap[status as keyof typeof statusMap];
        if (!statusInfo) {
          return h("span", status);
        }

        return h(
          NTag,
          {
            type: statusInfo.type,
            bordered: false,
          },
          {
            default: () => statusInfo.text,
          },
        );
      },
    },
    {
      title: "申请时间",
      key: "apply_time",
      width: 160,
      render(row) {
        return h("span", row.apply_time ? formatToDate(row.apply_time) : "--");
      },
    },
    {
      title: "审核时间",
      key: "approve_time",
      width: 160,
      render(row) {
        return h("span", row.approve_time ? formatToDate(row.approve_time) : "--");
      },
    },
    {
      title: "审核人",
      key: "approver",
      width: 120,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return h("span", row.approver || "--");
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 200,
      fixed: "right",
      render(row) {
        const isPending = row.status === "pending";

        return h(NFlex, {}, [
          h(NButton, {
            text: true,
            type: "info",
            onClick: () => handleView(cloneDeep(row)),
          }, { default: () => "查看" }),

          ...(isPending
            ? [
              h(NButton, {
                text: true,
                type: "success",
                loading: row.approveLoading,
                onClick: () => handleApprove(cloneDeep(row)),
              }, { default: () => "通过" }),

              h(NButton, {
                text: true,
                type: "error",
                loading: row.rejectLoading,
                onClick: () => handleReject(cloneDeep(row)),
              }, { default: () => "拒绝" }),
            ]
            : []),

          h(NDropdown, {
            options: getDropdownOptions(row),
            onSelect: (key: string) => handleDropdownSelect(key, row),
            trigger: "hover",
          }, {
            default: () => h(NButton, {
              text: true,
              type: "info",
            }, {
              default: () => [
                "更多",
                h("n-icon", {}, h(CAIcon, { icon: "tabler:chevron-down", size: 15 })),
              ],
            }),
          }),
        ]);
      },
    },
  ];

  // ========== 操作相关 ==========
  /**
   * 获取下拉菜单选项
   */
  function getDropdownOptions(row: ApprovalInfoExpanded) {
    const options: Array<{
      label: string;
      key: string;
      props?: { style: string };
    }> = [];

    if (row.status === "pending") {
      options.push({
        label: "取消申请",
        key: "cancel",
        props: {
          style: "color: #d03050;",
        },
      });
    }

    options.push({
      label: "查看详情",
      key: "detail",
    });

    return options;
  }

  /**
   * 处理查看操作
   */
  function handleView(row: ApprovalInfoExpanded) {
    selectedApproval.value = row;
    detailModalShow.value = true;
  }

  /**
   * 处理审核通过操作
   */
  function handleApprove(row: ApprovalInfoExpanded) {
    dialog.info({
      title: "确认审核",
      content: `确认通过 "${row.applicant}" 的 "${row.tenant_name}" 申请吗？`,
      positiveText: "确认通过",
      negativeText: "取消",
      async onPositiveClick() {
        try {
          row.approveLoading = true;
          // TODO: 调用审核通过API
          // await approveApplicationApi(row.id);
          message.success("审核通过成功");
          await loadTableData();
        } catch {
          message.error("审核通过失败，请重试");
        } finally {
          row.approveLoading = false;
        }
      },
    });
  }

  /**
   * 处理审核拒绝操作
   */
  function handleReject(row: ApprovalInfoExpanded) {
    dialog.info({
      title: "确认审核",
      content: `确认拒绝 "${row.applicant}" 的 "${row.tenant_name}" 申请吗？`,
      positiveText: "确认拒绝",
      negativeText: "取消",
      async onPositiveClick() {
        try {
          row.rejectLoading = true;
          // TODO: 调用审核拒绝API
          // await rejectApplicationApi(row.id);
          message.success("审核拒绝成功");
          await loadTableData();
        } catch {
          message.error("审核拒绝失败，请重试");
        } finally {
          row.rejectLoading = false;
        }
      },
    });
  }

  /**
   * 处理下拉菜单选择
   */
  function handleDropdownSelect(key: string, row: ApprovalInfoExpanded) {
    switch (key) {
      case "cancel":
        handleCancel(row);
        break;
      case "detail":
        handleViewDetail(row);
        break;
    }
  }

  /**
   * 处理取消申请操作
   */
  function handleCancel(row: ApprovalInfoExpanded) {
    dialog.warning({
      title: "确认取消",
      content: `确认取消 "${row.applicant}" 的 "${row.tenant_name}" 申请吗？`,
      positiveText: "确认取消",
      negativeText: "保留",
      async onPositiveClick() {
        try {
          // TODO: 调用取消申请API
          // await cancelApplicationApi(row.id);
          message.success("取消申请成功");
          await loadTableData();
        } catch {
          message.error("取消申请失败，请重试");
        }
      },
    });
  }

  /**
   * 处理查看详情操作
   */
  function handleViewDetail(row: ApprovalInfoExpanded) {
    selectedApproval.value = row;
    detailModalShow.value = true;
  }

  // ========== 数据加载 ==========
  /**
   * 包装API调用以适配useTable的类型要求
   */
  async function wrappedApprovalListApi(queryCriteria?: QueryCriteria) {
    return await approvelListApi((queryCriteria || {}) as AnyObject);
  }

  /**
   * 加载表格数据
   */
  async function loadTableData() {
    await loadData({
      getQueryCriteria: () => ({
        ...props.queryCriteria,
      }),
      withPagination: true,
      tableRequest: wrappedApprovalListApi,
      handleTableData: (data: ApprovalInfo[]) => {
        return data.map(item => ({
          ...item,
          key: item.id,
          approveLoading: false,
          rejectLoading: false,
        }));
      },
    });
  }

  // ========== 弹窗事件处理 ==========
  /**
   * 处理弹窗中的审核通过操作
   */
  async function handleModalApprove(_row: ApprovalInfoExpanded) {
    try {
      // TODO: 调用审核通过API
      // await approveApplicationApi(_row.id);
      // 模拟API调用成功
    } catch (error) {
      console.error("审核通过失败:", error);
      throw error;
    }
  }

  /**
   * 处理弹窗中的审核拒绝操作
   */
  async function handleModalReject(_row: ApprovalInfoExpanded) {
    try {
      // TODO: 调用审核拒绝API
      // await rejectApplicationApi(_row.id);
      // 模拟API调用成功
    } catch (error) {
      console.error("审核拒绝失败:", error);
      throw error;
    }
  }

  /**
   * 处理弹窗中的取消申请操作
   */
  async function handleModalCancel(_row: ApprovalInfoExpanded) {
    try {
      // TODO: 调用取消申请API
      // await cancelApplicationApi(_row.id);
      // 模拟API调用成功
    } catch (error) {
      console.error("取消申请失败:", error);
      throw error;
    }
  }

  // ========== 生命周期 ==========
  // 监听查询条件变化，重新加载数据
  watchEffect(() => {
    loadTableData();
  });

  // 监听搜索触发器
  if (searchTrigger) {
    watch(searchTrigger, (newVal, oldVal) => {
      if (newVal > oldVal) {
        // 重新加载表格数据
        loadTableData();
      }
    });
  }
</script>

<style scoped>
.fixed-right-table :deep(.n-data-table-th--fixed-right) {
  background-color: var(--n-th-color);
}

.fixed-right-table :deep(.n-data-table-td--fixed-right) {
  background-color: var(--n-td-color);
}
</style>
