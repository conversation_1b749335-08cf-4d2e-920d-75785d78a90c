import Decimal from "decimal.js";

/**
 * 获取一个数字的基数。例如，10 的基数是 10，100 的基数是 100，1000 的基数是 1000。
 * @param num - 要获取基数的数字。
 * @returns {number} - 基数。
 */
export function getBaseNumber(num) {
  const absNum = Math.abs(num).toFixed(0);
  const digits = absNum.toString().length;
  return 10 ** (digits - 1);
}

/**
 * 将数字四舍五入到指定基数
 * @param num - 要四舍五入的数字，例如：787
 * @param base - 四舍五入的基数，传 10 那么 787 -> 790，传 100 那么 787 -> 800，传 1000 那么 787 -> 1000
 * @param {string} mode - 四舍五入模式，可选值为 "round"、"ceil" 和 "floor"。
 * @returns {number} - 四舍五入后的数字。
 */
export function roundTo(num, base, mode = "round") {
  if (base <= 0) {
    throw new Error("基数必须为正数");
  }
  const quotient = num / base;
  let rounded;
  switch (mode) {
    case "round":
      // 手动处理负数四舍五入，避免 Math.round 的“向偶数取整”问题
      rounded = num >= 0 ? Math.round(quotient) : -Math.round(-quotient);
      break;
    case "ceil":
      rounded = Math.ceil(quotient);
      break;
    case "floor":
      rounded = Math.floor(quotient);
      break;
    default:
      throw new Error("模式需为 round/ceil/floor");
  }
  return rounded * base;
}

/**
 * 数字添加单位，例如 1000 -> 1K
 * @param {number} value - 要添加单位的数字
 * @returns {number | string} - 添加单位的数字或字符串
 */
export function unitNumber(value: number) {
  if (typeof value !== "number" || Number.isNaN(value)) {
    return value;
  }
  const units = ["", "K", "M", "B", "T"];
  let unitIndex = 0;
  while (value >= 1000 && unitIndex < units.length - 1) {
    value /= 1000;
    unitIndex++;
  }

  return value.toFixed(2) + units[unitIndex];
}

export function numberToChinese(num, isTraditional = false) {
  const simplified = {
    digits: ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"],
    units: ["", "十", "百", "千"],
    bigUnits: ["", "万", "亿", "兆"],
  };

  const traditional = {
    digits: ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"],
    units: ["", "拾", "佰", "仟"],
    bigUnits: ["", "萬", "億", "兆"],
  };

  const lang = isTraditional ? traditional : simplified;
  let isNegative = false; // 是否负数

  // 处理负数
  if (num < 0) {
    isNegative = true;
    num = -num;
  }

  const numStr = String(num);

  // 特殊情况：数字为 0
  if (numStr === "0") {
    return lang.digits[0];
  }

  // 因为中文都是四位一组，所以分割数字也是四位一组
  const groups = splitNumber(numStr);
  // 转换每组的数字为中文，并加上单位
  const converted = groups.map(group => convertNumber(group, lang));

  let result = "";
  let needZero = false;
  // 从高位到低位组合结果
  for (let i = 0; i < converted.length; i++) {
    if (converted[i] !== "") {
      // 如果需要加“零”，则添加
      if (needZero) {
        result += lang.digits[0];
      }
      // 添加当前段的转换结果和大单位
      result += converted[i] + lang.bigUnits[groups.length - 1 - i];
      // 判断后续是否需要“零”
      needZero = !converted[i].endsWith(lang.digits[0]);
    } else {
      // 当前段为空，则可能需要“零”
      needZero = true;
    }
  }

  // 添加负号
  if (isNegative) {
    result = `负${result}`;
  }

  return result;
}

/**
 * 将数字字符串按四位一组分割，从低位到高位
 * @param numStr - 数字字符串
 * @returns {any[]} - 分割后的数字数组，例如 "123456789" -> ["1", "2345", "6789"]
 */
function splitNumber(numStr: string = ""): string[] {
  const groups: string[] = [];
  while (numStr.length > 0) {
    groups.unshift(numStr.slice(-4));
    numStr = numStr.slice(0, -4);
  }
  return groups;
}

/**
 * 将四位数字转换为中文，例如 "1234" -> "一千二百三十四"
 * @param num - 四位数字字符串
 * @param lang - 语言配置对象，包含 digits、units 和 bigUnits 数组
 * @returns {string} - 转换后的中文数字字符串
 */
function convertNumber(num, lang) {
  const digits = lang.digits; // ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"]
  const units = lang.units; // ["", "十", "百", "千"]
  // 去掉前导零，若全为零则返回空字符串
  num = num.replace(/^0+/, "");
  if (num === "") {
    return "";
  }
  // 将数字字符串转换为数字数组，不够 4 位的前面补 0，并转换为数字数组
  // 例如 "1234" -> [1, 2, 3, 4]
  const numArr = num.padStart(4, "0").split("").map(Number);
  let result = "";
  let hasNonZero = false;
  numArr.forEach((num, index) => {
    if (num !== 0) {
      result += digits[num] + units[3 - index];
      hasNonZero = true;
    } else if (hasNonZero && index < 3 && numArr.slice(index + 1).some(n => n !== 0)) {
      // 避免连续添加“零”
      if (result.slice(-1) !== digits[0]) {
        result += digits[0];
      }
    }
  });
  return result;
}

/**
 * 使用 Decimal.js 库进行精确的加减乘除运算
 */
export function decimalAdd(a: number, b: number): number {
  return new Decimal(a).plus(b).toNumber();
}
/**
 * 使用 Decimal.js 库进行精确的加减乘除运算
 */
export function decimalSubtract(a: number, b: number): number {
  return new Decimal(a).minus(b).toNumber();
}

/**
 * 使用 Decimal.js 库进行精确的加减乘除运算
 */
export function decimalMultiply(a: number, b: number): number {
  return new Decimal(a).times(b).toNumber();
}

/**
 * 使用 Decimal.js 库进行精确的加减乘除运算
 */
export function decimalDivide(a: number, b: number): number {
  return new Decimal(a).dividedBy(b).toNumber();
}

/**
 * 使用 Decimal.js 库进行精确的加减乘除运算
 */
export function decimalRound(value: number, precision: number): number {
  return new Decimal(value).toDecimalPlaces(precision).toNumber();
}
