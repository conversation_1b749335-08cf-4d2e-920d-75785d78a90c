<script setup lang="ts">
import { ActionIcon } from "~/component/ActionIcon";
import { useMenuSetting } from "~/composables/setting/useMenuSetting";

const { t } = useI18n();

const { toggleCollapsed, getCollapsed } = useMenuSetting();
</script>

<template>
  <ActionIcon :tooltip-text="t('layouts.header.toggleCollapsed')" :icon="getCollapsed ? 'tabler:layout-sidebar-right-collapse' : 'tabler:layout-sidebar-left-collapse'" @click="toggleCollapsed()" />
</template>
