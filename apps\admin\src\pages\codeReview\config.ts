export interface TypeOption {
  value: string;
  label: string;
  bgColor: string;
  textColor: string;
  borderColor: string;
}

export interface UserTypeOption {
  value: number;
  label: string;
}

export const TYPE_LIST: Record<string, TypeOption> = Object.freeze({
  opened: {
    value: 'opened',
    label: '待处理',
    bgColor: '#fff7e6',      // 浅橙色背景
    textColor: '#fa8c16',    // 橙色文字
    borderColor: '#ffd591'   // 橙色边框
  },
  merged: {
    value: 'merged',
    label: '已合并',
    bgColor: '#f6ffed',      // 浅绿色背景
    textColor: '#52c41a',    // 绿色文字
    borderColor: '#b7eb8f'   // 绿色边框
  },
  closed: {
    value: 'closed',
    label: '已关闭',
    bgColor: '#fff1f0',      // 浅红色背景
    textColor: '#f5222d',    // 红色文字
    borderColor: '#ffa39e'   // 红色边框
  },
});

export const USER_TYPE_LIST: UserTypeOption[] = [
  { value: 1, label: '指派人' },
  { value: 2, label: '审核人' },
];