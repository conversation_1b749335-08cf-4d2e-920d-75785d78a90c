/*
 * @Author: zy
 * @Date: 2024-12-31 14:49:11
 * @LastEditors: zy
 * @LastEditTime: 2025-01-16 10:10:17
 * @Description:
 */
import type { GlobConfig, GlobEnvConfig } from "@celeris/types";
import { version } from "../package.json";

/*
 * 获取配置文件名称
 * Get the configuration file variable name
 */
export function getAppConfigFileName(env: GlobEnvConfig): string {
  const shortName: string = env?.VITE_GLOB_APP_SHORT_NAME || "__APP";
  return `__PRODUCTION__${shortName}__CONF__`.toUpperCase().replace(/\s/g, "");
}

/*
 * 获取全局配置
 * Get the global configuration
 */
export function getAppGlobalConfig(env: GlobEnvConfig): GlobConfig {
  const { VITE_GLOB_APP_TITLE = "", VITE_GLOB_API_URL = "", VITE_GLOB_APP_SHORT_NAME = "", VITE_GLOB_API_URL_SSR = "", VITE_GLOB_API_URL_PREFIX = "" }
    = getAppGlobalEnvConfig(env);

  if (!/^[a-z_]*$/i.test(VITE_GLOB_APP_SHORT_NAME)) {
    console.warn(
      "VITE_GLOB_APP_SHORT_NAME Variables can only be characters/underscores, please modify in the environment variables and re-running.",
    );
  }

  return {
    APP_TITLE: VITE_GLOB_APP_TITLE || "",
    API_URL: VITE_GLOB_API_URL || "",
    APP_SHORT_NAME: VITE_GLOB_APP_SHORT_NAME || "",
    API_URL_SSR: VITE_GLOB_API_URL_SSR || "",
    API_URL_PREFIX: VITE_GLOB_API_URL_PREFIX || "",
  };
}

/*
 * 获取存储键前缀
 * Get the storage key prefix
 */
function createStorageKeyPrefix(env: GlobEnvConfig): string {
  const AppConfig: GlobEnvConfig = getAppGlobalEnvConfig(env);
  const mode = AppConfig?.MODE || "";
  return `${AppConfig?.VITE_GLOB_APP_SHORT_NAME}_${mode}`.toUpperCase();
}
/*
 * 获取存储名称
 * Get the storage name
 */
export function createStorageName(env: GlobEnvConfig): string {
  return `${createStorageKeyPrefix(env)}_${version}_`.toUpperCase();
}

/*
 * 获取全局环境配置
 * Get the global environment configuration
 */
function getAppGlobalEnvConfig(env: GlobEnvConfig): GlobEnvConfig {
  if (JSON.parse(String(env.VITE_GLOB_BUILD_GENERATE_CONFIG || false))) {
    const envName = getAppConfigFileName(env);
    return env?.DEV ? env : window[envName];
  }
  return env;
}
