{"name": "@celeris/vite", "type": "module", "version": "0.0.3", "description": "vite for <PERSON><PERSON>is", "author": "<PERSON> (https://github.com/kirklin)", "license": "MIT", "homepage": "https://github.com/kirklin/celeris-web", "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "scripts": {"clean": "pnpm rimraf node_modules && pnpm rimraf dist", "postinstall": "pnpm unbuild --stub"}, "dependencies": {"@celeris/utils": "workspace:*", "marked": "^15.0.12", "mockjs": "^1.1.0", "vite": "^5.4.11"}, "devDependencies": {"@celeris/styles": "workspace:*", "@celeris/types": "workspace:*", "@vitejs/plugin-vue-jsx": "^4.1.0", "rollup-plugin-visualizer": "^5.12.0", "typescript": "^5.7.2", "unplugin-auto-import": "^0.18.5", "unplugin-config": "^0.1.5", "unplugin-vue-components": "^0.27.4", "vite-plugin-inspect": "^0.8.8", "vite-plugin-mock": "^3.0.2", "vite-plugin-pwa": "^0.21.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.6.4"}, "volta": {"node": "18.20.4"}}