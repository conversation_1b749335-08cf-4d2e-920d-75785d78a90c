<template>
  <CardModal
    v-bind="$attrs"
    class="w-xl"
    :title="modalTitle"
    :empty-func="() => false"
    @update:show="handleHide"
    @after-enter="handleAfterEnter"
    @after-leave="handleAfterLeave"
  >
    <div class="p-4">
      <NForm ref="formRef" :model="formModel" :rules="formRules" label-width="60" label-placement="left" require-mark-placement="left">
        <NFormItem label="资源" path="resource">
          <NInput v-model:value="formModel.resource" :disabled="isEdit" placeholder="请输入资源名称" />
        </NFormItem>
        <NFormItem label="动作" path="action">
          <NInput v-model:value="formModel.action" :disabled="isEdit" placeholder="请输入动作" />
        </NFormItem>
        <NFormItem label="角色" path="role">
          <NSelect
            v-model:value="formModel.role"
            :options="roleOptions"
            placeholder="请选择角色"
          />
        </NFormItem>
      </NForm>
    </div>
    <template #footer>
      <NFlex justify="center">
        <NButton @click="handleHide">
          取消
        </NButton>
        <NButton type="primary" :loading="submitting" @click="handleCreate">
          确定
        </NButton>
      </NFlex>
    </template>
  </CardModal>
</template>

<script lang="ts" setup>
  import type { PermissionExpanded } from "-/permission";
  import type { FormInst, FormRules, SelectOption } from "naive-ui";
  import { createPermission, updatePermission } from "~/apis/internal/permission";
  import CardModal from "~/component/CardModal/src/CardModal.vue";

  defineOptions({
    name: "PermissionFormModal",
    inheritAttrs: false,
  });

  const props = withDefaults(defineProps<{
    data: PermissionExpanded | null;
    roleOptions: SelectOption[];
    project: string;
  }>(), {
    data: () => ({}) as PermissionExpanded,
  });

  const emit = defineEmits<{
    "update:show": [show: boolean];
    "positiveClick": [success: boolean];
  }>();

  /**
   * @description 权限策略表单初始值
   */
  const INIT_PERMISSION_FORM_MODEL: PermissionExpanded = {
    id: 0,
    resource: "",
    action: "",
    role: null,
    project: "",
  };

  const isEdit = computed(() => !!(props.data && props.data.id));

  const modalTitle = computed(() => {
    return isEdit.value ? "编辑策略" : "新增策略";
  });

  const message = useMessage();

  const submitting = ref(false);

  const formRef = useTemplateRef<FormInst>("formRef");
  const formModel = ref<PermissionExpanded>({ ...INIT_PERMISSION_FORM_MODEL });
  /**
   * @description 权限策略表单校验规则
   */
  const formRules: FormRules = {
    resource: [
      { required: true, message: "请输入资源名称", trigger: "blur" },
    ],
    action: [
      { required: true, message: "请输入动作", trigger: "blur" },
    ],
    role: [
      { required: true, message: "请选择角色", trigger: "blur" },
    ],
  };

  /**
   * @description 弹窗进入动画后初始化表单数据。
   * @returns {void}
   */
  function handleAfterEnter() {
    props.data ? initFormModel() : clearFormModel();
  }

  /**
   * @description 弹窗关闭动画后清空表单数据。
   * @returns {void}
   */
  function handleAfterLeave() {
    clearFormModel();
  }

  /**
   * @description 关闭弹窗。
   * @returns {void}
   */
  function handleHide() {
    emit("update:show", false);
  }

  /**
   * @description 根据 props.data 初始化表单模型。
   * @returns {void}
   */
  function initFormModel() {
    const data = props.data;
    Object.keys(INIT_PERMISSION_FORM_MODEL).forEach((key) => {
      formModel.value[key] = data?.[key] || INIT_PERMISSION_FORM_MODEL[key];
    });
  }

  /**
   * @description 重置表单模型为初始值，并恢复校验状态。
   * @returns {void}
   */
  function clearFormModel() {
    formModel.value = { ...INIT_PERMISSION_FORM_MODEL, project: props.project };
    formRef.value?.restoreValidation?.();
  }

  /**
   * @description 提交表单，创建或更新权限策略。
   * @returns {Promise<void>}
   */
  async function handleCreate() {
    await formRef.value?.validate();
    submitting.value = true;
    try {
      if (formModel.value.id) {
        await updatePermission(props.project, [formModel.value]);
      } else {
        await createPermission(props.project, [formModel.value]);
      }
      handleHide();
      emit("positiveClick", true);
    } catch (e) {
      message.error("操作失败");
    } finally {
      submitting.value = false;
    }
  }
</script>
