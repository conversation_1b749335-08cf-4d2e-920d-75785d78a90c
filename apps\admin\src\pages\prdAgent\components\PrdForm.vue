<template>
  <NScrollbar class="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-md flex flex-col h-full relative pl-4">
    <NFlex vertical>
      <div class="absolute top-1/5 left-2 transform -translate-y-1/2 -translate-x-1/2 bg-gradient-to-br from-blue-600 to-indigo-700 text-white text-sm font-semibold font-sans px-0 py-4 rounded-tr-xl rounded-br-xl !z-[100] transition-all duration-500 hover:shadow-xl hover:from-blue-700 hover:to-indigo-800 hover:scale-105 hover:shadow-indigo-500/20 text-shadow-md shadow-lg min-h-[100px] flex items-center justify-center border border-indigo-500/30" style="writing-mode: vertical-rl; min-width: 24px;" @click="showHistoryList">
        历史记录
      </div>
      <!-- 生成完成后的界面 -->
      <div v-if="showGeneratedView" class="flex flex-col h-full max-h-full overflow-hidden">
        <!-- 标题区域 -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
          <div class="flex items-start space-x-3">
            <img :src="editIcon" alt="编辑图标" class="text-blue-600 mt-0.5 w-12 h-12" />
            <div>
              <h2 class="text-base font-semibold text-gray-800 dark:text-gray-200">
                PRD生成
              </h2>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                阅读《使用说明》，快速上手 →
              </p>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center gap-3 mt-4">
            <!-- <NButton type="primary" size="medium" class="bg-blue-600 hover:bg-blue-700 rounded-full px-6">
              <template #icon>
                <CAIcon icon="tabler:wand" />
              </template>
              AI修改
            </NButton> -->
            <NButton type="default" size="medium" class="border-blue-600 text-blue-600 hover:bg-blue-50 rounded-full px-6" @click="returnToEdit">
              返回修改
            </NButton>
            <NButton type="default" size="medium" class="border-blue-600 text-blue-600 hover:bg-blue-50 rounded-full px-6" @click="createNewPrd">
              新建PRD
            </NButton>
          </div>
        </div>

        <!-- 目录区域 -->
        <div class="flex-1 p-4 overflow-y-auto max-h-full" style="min-height: 0; max-height: calc(100% - 120px);">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
            目录
          </h3>
          <ul class="space-y-3 text-sm">
            <!-- PRD正文主章节 -->
            <li
              class="flex items-center cursor-pointer hover:bg-blue-50 dark:hover:bg-gray-700 p-1 rounded transition-colors"
              @click="scrollToSection('PRD正文')"
            >
              <span class="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
              <span class="text-gray-700 dark:text-gray-300">PRD 正文</span>
            </li>

            <!-- 动态解析的子章节（PRD正文中的一级标题） -->
            <!-- <template v-for="subSection in dynamicSubSections" :key="`sub-${subSection}`">
              <li
                class="flex items-center ml-4 cursor-pointer hover:bg-blue-50 dark:hover:bg-gray-700 p-1 rounded transition-colors"
                @click="scrollToSection(subSection)"
              >
                <span class="w-1.5 h-1.5 bg-gray-400 rounded-full mr-3"></span>
                <span class="text-gray-600 dark:text-gray-400 text-xs">{{ subSection }}</span>
              </li>
            </template> -->

            <!-- 固定章节（基于配置文件） -->
            <template v-for="config in contentConfigs" :key="`config-${config.id}`">
              <li
                class="flex items-center cursor-pointer hover:bg-blue-50 dark:hover:bg-gray-700 p-1 rounded transition-colors"
                @click="scrollToSection(config.title)"
              >
                <span class="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                <span class="text-gray-700 dark:text-gray-300">{{ config.title }}</span>
              </li>
            </template>
          </ul>
        </div>
      </div>

      <!-- 原始表单界面 -->
      <div v-else class="flex flex-col h-full max-h-full overflow-hidden">
        <div class="p-2 pl-3 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
          <div class="flex items-start space-x-3">
            <img :src="editIcon" alt="编辑图标" class="text-blue-600 mt-0.5 w-12 h-12" />
            <div>
              <h2 class="text-base font-semibold text-gray-800 dark:text-gray-200">
                PRD生成
              </h2>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                阅读《使用说明》，快速上手 →
              </p>
            </div>
          </div>
        </div>

        <div class="p-3 space-y-5 flex-1 overflow-y-auto max-h-full" style="min-height: 0; max-height: calc(100% - 80px);">
          <div class="space-y-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300 flex justify-between">
              <span>功能名</span>
              <span class="text-xs text-gray-500">{{ formData.title.length }}/50</span>
            </label>
            <NInput
              v-model:value="formData.title"
              placeholder="请输入功能名"
              class="w-full"
            >
            </NInput>
          </div>

          <div class="space-y-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300 flex justify-between">
              <span>需求描述</span>
              <span class="text-xs text-gray-500">{{ formData.description.length }}/100</span>
            </label>
            <NInput
              v-model:value="formData.description"
              placeholder="请描述功能需求"
              :rows="2"
              type="textarea"
              class="w-full"
            >
            </NInput>
          </div>

          <div class="space-y-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300 flex justify-between">
              <span>需求详情</span>
              <span class="text-xs text-gray-500">{{ formData.requirements.length }}/500</span>
            </label>
            <NInput
              v-model:value="formData.requirements"
              placeholder="请描述需求详情"
              :rows="6"
              type="textarea"
              class="w-full"
            >
            </NInput>
          </div>

          <div class="pt-2">
            <NButton
              type="primary"
              size="large"
              class="w-full bg-gradient-to-r from-blue-600 to-indigo-700 text-white shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-0.5 hover:from-blue-700 hover:to-indigo-800"
              :loading="props.loading"
              :disabled="props.loading"
              @click="onGenerate"
            >
              {{ props.loading ? '生成中...' : '生成文档' }}
            </NButton>
          </div>
        </div>
      </div>
      <!-- 历史记录抽屉 -->
      <HistoryDrawer
        v-model:visible="historyDrawerVisible"
        @select="selectHistoryItem"
      />
    </NFlex>
  </NScrollbar>
</template>

<script setup lang="ts">
  import { editIcon } from "@celeris/assets";
  import { NButton, NInput, NTooltip } from "naive-ui";
  import { computed, defineEmits, ref } from "vue";
  import { contentConfigs } from "../config";
  import HistoryDrawer from "./HistoryDrawer.vue";

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
    sections: () => ({}),
    prdContent: "",
  });

  const emits = defineEmits<{
    (e: "generate", value: object): void;
    (e: "generated", value: boolean): void;
    (e: "scrollToSection", sectionTitle: string): void;
    (e: "showHisDetail", value: object): void;
    (e: "createNew"): void;
    (e: "returnToEdit"): void;
  }>();

  interface Props {
    loading: boolean;
    sections?: Record<string, string>;
    prdContent?: string; // PRD正文内容，用于解析一级标题
  }

  const message = useMessage();

  const formData = ref({
    title: "",
    description: "",
    requirements: "",
  });

  // 控制显示生成后的界面
  const showGeneratedView = ref(false);

  // 解析PRD正文中的一级标题作为动态子章节
  const dynamicSubSections = computed(() => {
    if (!props.prdContent) {
      return [];
    }

    const headingRegex = /^# (.+)$/gm;
    const headings: string[] = [];
    let match: RegExpExecArray | null;

    while ((match = headingRegex.exec(props.prdContent)) !== null) {
      headings.push(match[1].trim());
    }

    return headings;
  });

  function onGenerate() {
    if (props.loading) {
      return;
    }
    // 表单验证
    if (!formData.value.title || !formData.value.description || !formData.value.requirements) {
      message.warning("请填写所有字段");
      return;
    }
    emits("generate", formData.value);
  };

  // 返回编辑模式
  function returnToEdit() {
    showGeneratedView.value = false;
    emits("generated", false);
    emits("returnToEdit");
  }

  // 设置生成完成状态
  function setGeneratedView(value: boolean) {
    showGeneratedView.value = value;
  }

  // 点击目录项滚动到对应位置
  function scrollToSection(sectionTitle: string) {
    emits("scrollToSection", sectionTitle);
  }

  // 暴露方法给父组件
  defineExpose({
    setGeneratedView,
  });

  // 历史记录相关状态
  const historyDrawerVisible = ref(false);

  // 显示历史记录抽屉
  function showHistoryList() {
    historyDrawerVisible.value = true;
  }

  // 选择历史记录
  function selectHistoryItem(item: any) {
    // 填充表单数据
    formData.value = {
      title: item.title,
      description: item.description,
      requirements: item.requirements || "",
    };
    emits("showHisDetail", item);
  }

  // 新建PRD
  function createNewPrd() {
    // 清空表单数据
    formData.value = {
      title: "",
      description: "",
      requirements: "",
    };
    // 返回编辑模式
    showGeneratedView.value = false;
    // 通知父组件创建新PRD
    emits("createNew");
  }
</script>

<style scoped>
:deep(.n-input__prefix),
:deep(.n-textarea__prefix) {
  margin-right: 8px;
}

:deep(.n-button--primary) {
  height: 40px;
  font-weight: 500;
}
</style>
