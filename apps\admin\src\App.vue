<template>
  <NConfigProvider v-bind="configProviderProps" class="size-full">
    <CAAppNaiveUIProvider>
      <RouterView />

      <NFloatButton v-if="!getPageLoading && visible" :right="10" :bottom="300" shape="circle" @click="showChatModal">
        <CAIcon class="chat-icon cursor-pointer text-28px">
          <img src="/robot-40.svg" alt="">
        </CAIcon>
      </NFloatButton>
    </CAAppNaiveUIProvider>
  </NConfigProvider>
</template>

<script setup lang="ts">
  import type { Emitter, EventType } from "@celeris/utils";
  import type { RouteLocationNormalizedLoaded } from "vue-router";
  import { logger } from "@celeris/utils";
  import ChatModal from "~/component/ChatModal";
  import { useNaiveUIConfigProvider } from "~/composables";
  import { EventNameConst } from "~/constants/eventConst";
  import { useAppStore } from "~/store/modules/app";
  import { usePermissionStore } from "~/store/modules/permission";
  import { subscribeStore } from "~/store/subscribe";

  /**
   * Subscribe to the store.
   * 订阅 store。
   */
  subscribeStore();
  logger.info("I'm ready!  ⸜(๑'ᵕ'๑)⸝⋆*");
  const { configProviderProps } = useNaiveUIConfigProvider();

  const appStore = useAppStore();
  // 获取页面加载状态
  // Get page loading status
  const getPageLoading = toRef(() => appStore.getPageLoading);

  const emitter = inject<Emitter<Record<EventType, unknown>>>("eventBus");
  const chatOptions = ref<AnyObject | null | undefined>();

  emitter?.on?.(EventNameConst["COMP:CHAT_CONFIG_CHANGED"], chatConfigChanged);

  /**
   * 处理聊天配置变化
   * Handle chat config changed
   */
  function chatConfigChanged(data: any) {
    chatOptions.value = data;
    if (ChatModal.isVisible()) {
      ChatModal.changeTarget(data.params);
    }
  }

  const visible = ref(false);
  function showChatModal() {
    // if (chatOptions.value.allowChat === false) {
    //   const { message } = createDiscreteApi(["message"]);
    //   return message.warning("正在同步人员数据，请稍候～");
    // }
    ChatModal.show(chatOptions.value);
  }

  const route = useRoute();
  const permissionStore = usePermissionStore();
  // 获取是否显示小助手
  // Get whether to display the assistant
  const assistant = computed(() => permissionStore.getPermissionInfo?.assistant);
  /*
  * 监听路由变化
  * Watch route change
  */
  watch(route, (newVal: RouteLocationNormalizedLoaded) => {
    const inPerformance = !!newVal.matched.find(item => item.path === "/performance");
    visible.value = inPerformance && !!assistant.value;
    if (visible.value) {
      chatOptions.value = {
        position: "right",
        params: {
          title: "小翼同学-绩效助手",
        },
      };
    } else {
      ChatModal.hide();
    }
  }, { immediate: true });
</script>
