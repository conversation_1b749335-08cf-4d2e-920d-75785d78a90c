<template>
  <NFlex class="p-4 pt-0 space-y-4" vertical>
    <NTabs v-model:value="activeTab" :x-gap="12">
      <NTabPane
        v-for="item in tabList"
        :key="item.value"
        :name="item.value"
        :tab="item.label"
      >
        <component
          :is="componentsMap[item.value]"
          :query-criteria="props.queryCriteria"
          @add-tenant="handleAddTenant"
        />
      </NTabPane>
    </NTabs>
  </NFlex>
</template>

<script lang="ts" setup>
  import type { QueryCriteria } from "-/tenant";
  import { tenantListApi } from "~/apis/internal/tenant";
  import ApprovelList from "./components/approvelList.vue";
  import TenantList from "./components/tenantList.vue";

  const props = withDefaults(defineProps<{
    queryCriteria: QueryCriteria;
  }>(), {
    queryCriteria: () => ({}) as QueryCriteria,
  });

  const emit = defineEmits<{
    (e: "tabChange", tab: string): void;
    (e: "addTenant"): void;
  }>();

  const tabList = ref([
    {
      label: "租户列表",
      value: "TenantList",
    },
    // {
    //   label: "审批列表",
    //   value: "ApprovelList",
    // },
  ]);

  const activeTab = ref(tabList.value[0].value);

  const componentsMap = shallowRef({
    TenantList,
    ApprovelList,
  });

  // ========== 监听器 ==========
  // ========== 方法 ==========
  function handleAddTenant() {
    emit("addTenant");
  }

  // ========== 监听器 ==========
  // 监听activeTab变化，通知父组件
  watch(activeTab, (newTab) => {
    emit("tabChange", newTab);
  }, { immediate: true });
</script>

<style scoped>

</style>
