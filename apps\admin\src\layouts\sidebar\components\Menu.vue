<template>
  <div :class="collapsed ? 'w-14' : 'w-60'" class="transition-width h-full shrink-0 flex-col overflow-hidden duration-90">
    <NScrollbar class="max-h-[calc(100vh_-_4rem)]">
      <NMenu
        v-model:value="activeMenu"
        :collapsed="collapsed"
        :root-indent="20"
        :indent="30"
        :collapsed-icon-size="20"
        :collapsed-width="collapsedWidth"
        :mode="mode"
        :options="menuList"
      />
    </NScrollbar>
  </div>
</template>

<script setup lang="ts">
  import type { Menu } from "@celeris/types";
  import type { RouteLocationNormalizedLoaded } from "vue-router";
  import { renderIcon } from "@celeris/components";
  import { mapTreeStructure } from "@celeris/utils";
  import { useI18n } from "vue-i18n";
  import { RouterLink } from "vue-router";
  import { REDIRECT_NAME } from "~/router/constant";
  import { getMenus } from "~/router/menus";
  import { listenToRouteChange } from "~/router/mitt/routeChange";
  import { usePermissionStore } from "~/store/modules/permission";

  defineOptions({
    name: "MenuLayout",
  });
  const props = withDefaults(
    defineProps<{
      mode?: "vertical" | "horizontal";
      collapsed?: boolean;
    }>(),
    { mode: "vertical", collapsed: false },
  );
  const { mode, collapsed } = toRefs(props);

  const { te, t } = useI18n();
  const activeMenu = ref();
  const permissionStore = usePermissionStore();
  const { currentRoute } = useRouter();
  const menuList = ref<any[]>([]);

  // 监听路由变化
  listenToRouteChange((route) => {
    if (route.name === REDIRECT_NAME) {
      return;
    }
    const currentActiveMenu = route.meta?.currentActiveMenu;
    handleMenuChange(route as RouteLocationNormalizedLoaded);
    if (currentActiveMenu) {
      activeMenu.value = currentActiveMenu;
    }
  });
  /*
   * 处理菜单变化
   * Handle menu change
   */
  async function handleMenuChange(route?: RouteLocationNormalizedLoaded) {
    const menu = route || unref(currentRoute);
    activeMenu.value = menu.path;
  }
  /*
   * 国际化渲染
   * Internationalization rendering
   */
  function i18nRender(key: string) {
    return te(key) ? t(key) : key;
  }

  /*
   * 将项目菜单转换为 naive-ui 菜单
   * Transform project menu to naive-ui menu
   */
  function transformProjectMenuToNaiveUIMenu(menu: Menu) {
    const { path, meta, icon, shouldDisabled, children } = menu;
    return {
      label: () => {
        if (children) {
          return i18nRender(meta?.title as string);
        }
        return shouldDisabled
          ? meta?.title
          : h(
            RouterLink,
            {
              to: {
                path,
              },
            },
            { default: () => i18nRender(meta?.title as string) },
          );
      },
      key: path,
      icon: renderIcon(icon || meta?.icon as string),
      collapseTitle: i18nRender(meta?.title as string),
      disabled: shouldDisabled,
    };
  }
  // Generate menu
  // 生成菜单
  function generateMenu() {
    const menus = getMenus();
    menuList.value = mapTreeStructure(menus, menu => transformProjectMenuToNaiveUIMenu(menu));
  }
  // Menu changes
  watch(
    [() => permissionStore.getLastMenuBuildTime, () => permissionStore.getBackendMenuList],
    /*
     * 生成菜单
     * Generate menu
     */
    () => {
      generateMenu();
    },
    {
      immediate: true,
    },
  );
  // Collapsed width
  const collapsedWidth = computed<number>(() => collapsed.value ? 56 : 224);
</script>

<style scoped>
</style>
