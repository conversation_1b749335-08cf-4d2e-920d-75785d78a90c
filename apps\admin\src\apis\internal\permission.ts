import type { Permission, PermissionExpanded, PermissionParam } from "-/permission";
import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";
import { replaceUrlPathParams } from "@celeris/utils";

enum API {
  Permissions = "/v1/projects/{projectId}/permissions",
  MenuPermissions = "/v1/projects/{projectId}/permissions/menu",
}

/**
 * 获取用户在one领域的权限（ 菜单、按钮权限）
 * @param params
 * @returns
 */
export function getPermissions(errorMessageMode: MessageMode = "message") {
  // projectId 是常量
  const projectId = import.meta.env.VITE_APP_BASE_DOMAIN_ID;
  const url = replaceUrlPathParams(API.MenuPermissions, { projectId });
  return request.get({
    url,
    params: { resource: "menu,btn" },
  }, { errorMessageMode });
}

/**
 * 获取策略列表
 * @param projectId - 项目/领域ID
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function getPermissionList(projectId: string, params: PermissionParam = {}, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.Permissions, { projectId });
  return request.get({ url, params }, { errorMessageMode, shouldTransformResponse: false });
}

/**
 * 新增策略
 * @param projectId - 项目/领域ID
 * @param data - 策略数据
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function createPermission(projectId: string, data: Permission[], errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.Permissions, { projectId });
  return request.post({ url, data }, { errorMessageMode });
}

/**
 * 删除策略
 * @param projectId - 项目/领域ID
 * @param data - 策略数据
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function deletePermission(projectId: string, data: PermissionExpanded[], errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.Permissions, { projectId });
  return request.delete({ url, data }, { errorMessageMode });
}

/**
 * 更新策略
 * @param projectId - 项目/领域ID
 * @param data - 策略数据
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function updatePermission(projectId: string, data: PermissionExpanded[], errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.Permissions, { projectId });
  return request.put({ url, data }, { errorMessageMode });
}

export { API };
