/*
 * @Author: zy
 * @Date: 2024-12-31 14:49:11
 * @LastEditors: zy
 * @LastEditTime: 2025-01-07 13:58:18
 * @Description: 
 */
import type { UserConfig } from "vite";
import process from "node:process";
import { defineConfig, mergeConfig } from "vite";
import { createApplicationViteConfig } from "./configs/application";

interface ApplicationViteConfigOptions {
  overrides?: UserConfig;
  // options?: {};
}

export async function createViteConfig(applicationViteConfigOptions: ApplicationViteConfigOptions = {}) {
  const { overrides = {} } = applicationViteConfigOptions;
  const root = process.cwd();
  return defineConfig(async ({ command, mode }) => {
    return mergeConfigs([overrides, await createApplicationViteConfig(command, mode, root)]);
  });
}

export function mergeConfigs(configs: UserConfig[]): Record<string, any> {
  
  return configs.reduce((mergedConfig, config) => {
    return mergeConfig(mergedConfig, config);
  }, {});
}
