// PRD内容配置接口
export interface ContentConfig {
  id: string;
  title: string;
  sectionKey: string;
  icon: string;
  description: string;
  showExportButton: boolean;
}

// PRD内容模块配置数组
export const contentConfigs: ContentConfig[] = [
  {
    id: "prd-content-page-structure",
    title: "页面结构图",
    sectionKey: "pageContruction",
    icon: "tabler:layout-dashboard",
    description: "生成页面结构图",
    showExportButton: true,
  },
  {
    id: "prd-content-prd-analysis",
    title: "PDR分析",
    sectionKey: "pdrAnalisis",
    icon: "tabler:chart-line",
    description: "生成PDR分析报告",
    showExportButton: false,
  },
  {
    id: "prd-content-flowchart",
    title: "流程图",
    sectionKey: "flowChart",
    icon: "tabler:git-branch",
    description: "生成业务流程图",
    showExportButton: false,
  },
  {
    id: "prd-content-sequence-diagram",
    title: "时序图",
    sectionKey: "sequenceDiagram",
    icon: "tabler:timeline",
    description: "生成时序交互图",
    showExportButton: false,
  },
  {
    id: "prd-content-class-diagram",
    title: "类图",
    sectionKey: "classDiagram",
    icon: "tabler:hierarchy",
    description: "生成系统类图",
    showExportButton: false,
  },
  {
    id: "prd-content-data-fields",
    title: "数据字段",
    sectionKey: "dataFields",
    icon: "tabler:database",
    description: "生成数据字段定义",
    showExportButton: false,
  },
  {
    id: "prd-content-test-cases",
    title: "测试用例",
    sectionKey: "testCases",
    icon: "tabler:test-pipe",
    description: "生成测试用例",
    showExportButton: false,
  },
];
