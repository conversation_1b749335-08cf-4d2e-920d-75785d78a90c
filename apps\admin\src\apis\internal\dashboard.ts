import { request } from "@celeris/request";

enum API {
  queryCustomPro = "/v2/performance/customProperties",
  getProjectIds = "/v2/efficiency/projects",
  getTeamAccounts = "/v2/team/accounts",
  addAssessment = "/v2/efficiency/WriteAssessmentToBS", // 同步北森
  getTalentMatrix = "/v2/efficiency/scoreDistributionMatrix",
  getSubjectiveAss = "/v2/efficiency/subjectiveAssessmentHistory",
  removeCache = "/v2/efficiency/removeScoreCache",
  getComprehensivesScoreDetail = "/v2/efficiency/comprehensivesScoreDetail",
  download = "/v2/efficiency/DownloadAssessmentToExcel",
}

// 查询自定义属性
export function queryCustomPro() {
  return request.get({ url: API.queryCustomPro });
}

// 查询项目列表
export function getProjectIds() {
  return request.get({ url: API.getProjectIds });
}

// 查询团队成员
export function getTeamAccounts(params) {
  return request.post({ url: API.getTeamAccounts, params });
}

//  一键导入考核结果
export function addAssessment(params) {
  return request.post({ url: API.addAssessment, params });
}

// 查询人才矩阵
export function getTalentMatrix(params) {
  return request.post({ url: API.getTalentMatrix, params });
}

// 历史评价信息
export function getSubjectiveAss(params) {
  return request.get({ url: API.getSubjectiveAss, params });
}

// 清理后端缓存数据
export function clearCache(params) {
  return request.post({ url: API.removeCache, params });
}

// 综合评分页面个人用户明细接口
export function geScoreDetail(params) {
  return request.post({ url: API.getComprehensivesScoreDetail, params });
}

// 导出excel
export function download(data) {
  return request.post({ url: API.download, data });
}
