import type {
  CreatePrdParams,
  PrdDetail,
  PrdFormData,
  PrdItem,
  PrdListParams,
  UpdatePrdParams,
} from "-/prd";
import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";
import { replaceUrlPathParams } from "@celeris/utils";
import { createPRDSSEConnection } from "~/pages/prdAgent/utils/sse-helper";
import { useUserStore } from "~/store/modules/user";

/**
 * PRD相关API路径枚举
 */
enum API {
  // PRD列表
  PrdList = "/v1/prd/projects",
  // PRD详情
  PrdDetail = "/v1/prd/projects/{id}",
  // 删除PRD
  DeletePrd = "/v1/prd/delete/{id}",
  // 创建PRD
  CreatePrd = "/v1/prd/create",
  // 更新PRD
  UpdatePrd = "/v1/prd/update/{id}",
  // 批量删除PRD
  BatchDeletePrd = "/v1/prd/batch-delete",
  // 复制PRD
  CopyPrd = "/v1/prd/copy/{id}",
  // 更新PRD状态
  UpdatePrdStatus = "/v1/prd/status/{id}",
  // PRD生成接口（EventSource）
  GeneratePrd = "/api/v1/prd/projects",
}

/**
 * 获取PRD列表
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<PrdItem[]> PRD列表
 */
export function getPrdList(params: PrdListParams = {}, errorMessageMode: MessageMode = "message"): Promise<PrdItem[]> {
  return request.get({ url: API.PrdList, params }, { errorMessageMode });
}

/**
 * 获取PRD详情
 * @param id - PRD ID
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<PrdDetail> PRD详情
 */
export function getPrdDetail(id: string, errorMessageMode: MessageMode = "message"): Promise<PrdDetail> {
  const url = replaceUrlPathParams(API.PrdDetail, { id });
  return request.get({ url }, { errorMessageMode });
}

/**
 * 删除指定PRD
 * @param id - PRD ID
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<void>
 */
export function deletePrd(id: string, errorMessageMode: MessageMode = "message"): Promise<void> {
  const url = replaceUrlPathParams(API.DeletePrd, { id });
  return request.delete({ url }, { errorMessageMode });
}

/**
 * 创建PRD
 * @param data - 创建参数
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<PrdDetail> 创建的PRD详情
 */
export function createPrd(data: CreatePrdParams, errorMessageMode: MessageMode = "message"): Promise<PrdDetail> {
  return request.post({ url: API.CreatePrd, data }, { errorMessageMode });
}

/**
 * 更新PRD
 * @param data - 更新参数
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<PrdDetail> 更新后的PRD详情
 */
export function updatePrd(data: UpdatePrdParams, errorMessageMode: MessageMode = "message"): Promise<PrdDetail> {
  const { id, ...updateData } = data;
  const url = replaceUrlPathParams(API.UpdatePrd, { id });
  return request.put({ url, data: updateData }, { errorMessageMode });
}

/**
 * 批量删除PRD
 * @param ids - PRD ID数组
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<void>
 */
export function batchDeletePrd(ids: string[], errorMessageMode: MessageMode = "message"): Promise<void> {
  return request.post({ url: API.BatchDeletePrd, data: { ids } }, { errorMessageMode });
}

/**
 * 复制PRD
 * @param id - 源PRD ID
 * @param title - 新PRD标题
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<PrdDetail> 复制的PRD详情
 */
export function copyPrd(id: string, title: string, errorMessageMode: MessageMode = "message"): Promise<PrdDetail> {
  const url = replaceUrlPathParams(API.CopyPrd, { id });
  return request.post({ url, data: { title } }, { errorMessageMode });
}

/**
 * 生成PRD（使用SSE流式传输）
 * @param formData - 表单数据
 * @param callbacks - 回调函数
 * @param callbacks.onStart - 开始回调
 * @param callbacks.onProjectCreated - 项目创建回调
 * @param callbacks.onMessage - 消息回调
 * @param callbacks.onCompleted - 完成回调
 * @param callbacks.onError - 错误回调
 */
export function generatePrdStream(
  formData: PrdFormData,
  callbacks: {
    onStart?: () => void;
    onProjectCreated?: (projectId: string) => void;
    onMessage?: (content: string) => void;
    onCompleted?: () => void;
    onError?: (error: any) => void;
  },
): { disconnect: () => void } {
  // 获取当前租户ID
  const userStore = useUserStore();
  const currentTenant = userStore.getCurrentTenant;
  const tenantId = currentTenant?.id || currentTenant?.tenant_id;

  // 构建请求头
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  };

  if (tenantId) {
    headers["x-done-tenant"] = tenantId;
  }

  // 创建SSE连接
  const sseManager = createPRDSSEConnection(
    {
      url: API.GeneratePrd,
      method: "POST", // 使用项目的SseClient，支持POST请求
      headers,
      body: formData,
      timeout: 60000, // 60秒超时
      retryAttempts: 2,
      retryDelay: 2000,
    },
    {
      onProjectCreated: (data) => {
        if (data.data?.id) {
          callbacks.onProjectCreated?.(data.data.id);
        }
      },
      onMessage: (data) => {
        if (data.answer) {
          callbacks.onMessage?.(data.answer);
        }
      },
      onMessageEnd: () => {
        callbacks.onCompleted?.();
      },
      onUnknownEvent: (eventType, data) => {
        console.warn("未知SSE事件:", eventType, data);
      },
    },
    {
      onStateChange: (state) => {
        if (state === "connected") {
          callbacks.onStart?.();
        }
      },
      onError: (error) => {
        callbacks.onError?.(error);
      },
      onTimeout: () => {
        callbacks.onError?.(new Error("连接超时"));
      },
    },
  );

  // 建立连接
  sseManager.connect();

  // 返回控制接口
  return {
    disconnect: () => sseManager.disconnect(),
  };
}
