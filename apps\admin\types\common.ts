export type TagType = "error" | "default" | "primary" | "info" | "success" | "warning";

export interface TreeSelectMeta<T> {
  node: T | null;
  action: "select" | "unselect" | "delete" | "clear";
}

// 分页参数
export interface Paging {
  page_size?: number;
  page_index?: number;
}

// 分页数据类型
export interface Pagination extends Paging {
  total: number;
  // page_index: number;
  // page_size: number;
}

export interface TreeItem {
  id: number;
  code: string;
  name: string;
  weight: number;
  // disabled?: boolean;
  children?: TreeItem[];
}

export interface TreeWrapper {
  data: TreeItem[];
}
