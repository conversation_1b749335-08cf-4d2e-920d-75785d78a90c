<template>
  <PageWrapper>
    <template #default="slotProps: { rect?: { height: number } }">
      <NFlex
        class="overflow-hidden flex-1"
        size="large"
        vertical
        :style="{ height: slotProps?.rect?.height ? `${slotProps.rect.height - 32}px` : 'auto' }"
      >
        <!-- 工具栏 -->
        <NPageHeader>
          <NFlex class="pr" justify="space-between">
            <div class="!w-25%">
              <NButton type="primary" :loading="tableLoading" @click="handleAdd">
                新增策略
              </NButton>
            </div>
            <NFlex class="">
              <NForm ref="searchFormRef" :model="searchModel" :show-label="false" :show-feedback="false" inline @submit.prevent>
                <NFormItem path="resource">
                  <NInput v-model:value="searchModel.resource" placeholder="请输入资源名称" clearable @keyup.enter="searchConfirm" />
                </NFormItem>
                <NFormItem path="action">
                  <NInput v-model:value="searchModel.action" placeholder="请输入动作" clearable @keyup.enter="searchConfirm" />
                </NFormItem>
                <NFormItem>
                  <NSpace>
                    <NButton type="primary" @click="searchConfirm">
                      查询
                    </NButton>
                    <NButton @click="searchReset">
                      重置
                    </NButton>
                  </NSpace>
                </NFormItem>
              </NForm>
            </NFlex>
          </NFlex>
        </NPageHeader>
        <!-- 表格 -->
        <NDataTable
          class="fixed-right-table h-full [&_.ca-data-table-resize-button::after]:(!bg-[var(--primary-color)])"
          scroll-x="min-content"
          remote
          flex-height
          :bordered="false"
          :single-line="false"
          :columns="columns"
          :data="tableData"
          :loading="tableLoading"
          :pagination="tablePagination"
          @scroll="handleScroll"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        >
        </NDataTable>
      </NFlex>
      <!-- 新增/编辑弹窗 -->
      <PermissionFormModal v-model:show="permissionFormModalShow" :data="currPermission" :role-options="roleOptions" :project="domainId" @positive-click="handleUpdatePermission" />
    </template>
  </PageWrapper>
</template>

<script setup lang="tsx">
  import type { Permission, PermissionExpanded } from "-/permission";
  import type { Role } from "-/role";
  import type { DataTableColumns, FormInst, SelectOption } from "naive-ui";
  import { cloneDeep } from "lodash-es";
  import { NButton, NFlex } from "naive-ui";
  import { deletePermission, getPermissionList } from "~/apis/internal/permission";
  import { getRoleList } from "~/apis/internal/role";
  import PageWrapper from "~/component/PageWrapper/src/PageWrapper.vue";
  import PermissionFormModal from "./components/PermissionFormModal.vue";

  interface QueryCriteria {
    q: string | null;
  }
  /**
   * @description 父组件传入的 domainId、角色列表、是否子域
   */
  const props = defineProps<{
    domainId: string;
    preRoles?: SelectOption[];
    isSub?: boolean;
  }>();

  const INIT_QUERY_MODEL = {
    resource: null,
    action: null,
  };

  const queryCriteria = ref<QueryCriteria>({ q: "" });

  const {
    tableLoading,
    tableData,
    tablePagination,
    loadData,
    refreshData,
    reloadData,
    handlePageChange,
    handlePageSizeChange,
    handleScroll,
  } = useTable<QueryCriteria, Permission, PermissionExpanded>();

  const message = useMessage();
  const dialog = useDialog();

  // 查询参数
  const searchModel = ref({ ...INIT_QUERY_MODEL });

  // 角色选项
  const roleOptions = ref<SelectOption[]>([]);

  // 新建和编辑弹窗相关
  const permissionFormModalShow = ref(false);
  const currPermission = ref<PermissionExpanded | null>(null);
  const searchFormRef = useTemplateRef<FormInst>("searchFormRef");

  // ========== 表格列定义 ==========
  const columns: DataTableColumns<PermissionExpanded> = [
    {
      title: "策略序号",
      key: "id",
      width: 130,
    },
    {
      title: "资源名称",
      key: "resource",
      minWidth: 120,
    },
    {
      title: "动作",
      key: "action",
      minWidth: 100,
    },
    {
      title: "绑定角色",
      key: "role",
      minWidth: 120,
    },
    {
      title: "操作",
      key: "actions",
      width: 180,
      render(row) {
        return (
          <NFlex>
            {/* <NButton
              quaternary
              text
              type="info"
              title="编辑"
              onClick={(e) => handleEdit?.(cloneDeep(row))}
            >
              {{
                icon: <CAIcon icon="tabler:edit" size={15} />,
              }}
            </NButton> */}
            <NButton
              quaternary
              text
              type="error"
              title="删除"
              loading={row.deleteLoading}
              onClick={() => handleDelete?.(row)}
            >
              删除
              {/* {{
                icon: <CAIcon icon="tabler:trash" size={15} />,
              }} */}
            </NButton>
          </NFlex>
        );
      },
      //   return h(
      //     NFlex,
      //     {},
      //     [
      //       // h(
      //       //   NButton,
      //       //  {
      //       //    text: true,
      //       //    type: "info",
      //       //    onClick: (e: PointerEvent) => {
      //       //      e.stopPropagation();
      //       //      handleEdit?.(cloneDeep(row));
      //       //    },
      //       //  },
      //       //  { default: () => "编辑" },
      //       // ),
      //       h(
      //         NButton,
      //         {
      //           quaternary: true,
      //           type: "error",
      //           title: "删除",
      //           // @ts-expect-error 忽略煞笔报错
      //           loading: row.deleteLoading,
      //           onClick: (e: PointerEvent) => {
      //             e.stopPropagation();
      //             handleDelete?.(row);
      //           },
      //         },
      //         {
      //           // default: () => "删除",
      //           icon: () => h(CAIcon, { icon: "tabler:trash", size: 15 }),
      //         },
      //       ),
      //     ],
      //   );
      // },
    },
  ];

  // 初始化
  onMounted(() => {
    loadTableData();
    getRoles();
  });

  // 监听 searchModel 变化，同步到 queryCriteria
  watch(
    searchModel,
    (val) => {
      queryCriteria.value.q = "";
      if (val.resource) {
        queryCriteria.value.q = `v2=~${val.resource}`;
      }
      if (val.action) {
        queryCriteria.value.q = `${queryCriteria.value.q ? `${queryCriteria.value.q},` : ""}v3=${val.action}`;
      }
      // if (val.role) {
      //   queryCriteria.value.q = `${queryCriteria.value.q ? queryCriteria.value.q + ',' : ''}v0=${val.role}`
      // }
    },
    { deep: true },
  );

  /**
   * @description 获取策略列表
   */
  function loadTableData() {
    loadData({
      getQueryCriteria: () => queryCriteria.value,
      dataPath: "data.value",
      totalPath: "data.total",
      pathParam: props.domainId,
      tableRequest: getPermissionList as unknown as (queryCriteria?: QueryCriteria) => Promise<Permission[]>,
      handleTableData: (dataSource: PermissionExpanded[]) => {
        return dataSource.map((item, index) => {
          return {
            ...item,
            key: item.id ? String(item.id) : index.toString(),
            deleteLoading: false,
          } as PermissionExpanded;
        });
      },
    });
  }

  /**
   * @description 获取角色列表
   */
  async function getRoles() {
    if (props.isSub && props.preRoles) {
      roleOptions.value = props.preRoles;
    } else {
      await getRoleList(props.domainId, {
        // project: props.domainId
        page_index: 1,
        page_size: 999,
      }).then((res: { data: { value: Role[] } }) => {
        roleOptions.value = res?.data.value?.map(obj => ({ label: obj.name, value: obj.name })) as SelectOption[];
      });
    }
  }

  /**
   * @description 筛选条件查询，重置页码为1并加载数据
   */
  function searchConfirm() {
    // pagination.value.page = 1;
    // loadData();
    reloadData();
  }
  /**
   * @description 查询条件重置，清空筛选项并重置页码，重新加载数据
   */
  function searchReset() {
    searchModel.value.resource = null;
    // searchModel.value.role_id = null;
    searchModel.value.action = null;
    reloadData();
  }
  /**
   * @description 切换分页页码，重新加载数据
   * @param {number} page - 当前页码
   */
  // function handlePageChange(page: number) {
  //   pagination.value.page = page;
  //   refreshData();
  // }
  // /**
  //  * @description 切换分页数量，重置页码为1并重新加载数据
  //  * @param {number} pageSize - 每页数量
  //  */
  // function handlePageSizeChange(pageSize: number) {
  //   pagination.value.pageSize = pageSize;
  //   pagination.value.page = 1;
  //   refreshData();
  // }

  /**
   * @description 新增策略，弹出新增弹窗
   */
  function handleAdd() {
    permissionFormModalShow.value = true;
    currPermission.value = null;
  }

  /**
   * @description 编辑策略，弹出编辑弹窗并设置当前策略
   * @param {PermissionExpanded} row - 当前行策略数据
   */
  function handleEdit(row: PermissionExpanded) {
    permissionFormModalShow.value = true;
    currPermission.value = row;
  }

  /**
   * @description 新增/编辑弹窗确认后刷新列表
   */
  function handleUpdatePermission() {
    searchConfirm();
  }

  /**
   * @description 删除策略，弹出确认框，确认后调用删除接口并刷新列表
   * @param {PermissionExpanded} row - 当前行策略数据
   */
  function handleDelete(row: PermissionExpanded) {
    dialog.info({
      title: "警告",
      content: "您确认要删除该策略吗？",
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          await deletePermission(props.domainId, [row]);
          message.success("删除成功");
          refreshData();
        } catch (e) {
          message.error("删除失败");
        }
      },
    });
  }
</script>

<style scoped></style>
