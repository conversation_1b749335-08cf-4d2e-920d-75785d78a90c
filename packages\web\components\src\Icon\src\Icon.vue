<template>
  <component :is="componentToRender" v-bind="options">
    <template v-if="$slots.default">
      <slot />
    </template>
    <template v-else>
      <Icon v-if="icon" :icon="icon" :width="size" :height="size" />
      <!-- <component :is="icon" :width="size" :height="size" /> -->
    </template>
  </component>
</template>

<script setup lang="ts">
  import { isNil } from "@celeris/utils";
  // import tabler from "@iconify/json/json/tabler.json";
  import { addAPIProvider, addCollection, getIcon, Icon, type IconifyIcon, loadIcon } from "@iconify/vue";
  import { NIcon, NIconWrapper } from "naive-ui";
  import { computed, ref, watchEffect } from "vue";
  import { normalizeIconName, toComponentName } from "./utils";
  addAPIProvider("", { resources: [import.meta.env.VITE_ICONIFY_URL] })

  interface IconOptions {
    name?: string;
    icon?: string;
    size?: number;
    bgSize?: number;
    color?: string;
    iconColor?: string;
    bgColor?: string;
    borderRadius?: number;
    depth?: 1 | 2 | 3 | 4 | 5;
  }
  const props = defineProps<Omit<IconOptions, "iconColor">>();
  // 是否使用包装器
  const shouldUseWrapper = computed(() => !!(props.bgColor || props.bgSize || props.borderRadius));
  // 根据是否使用包装器来决定渲染哪个组件
  const componentToRender = computed(() => (shouldUseWrapper.value ? NIconWrapper : NIcon));
  /*
   * 根据是否使用包装器来决定传递给组件的属性
   * Determine the props to pass to the component based on whether a wrapper is used
   */
  const options = computed(() => {
    const opt: IconOptions = {};
    if (shouldUseWrapper.value) {
      if (!isNil(props.bgSize)) {
        opt.size = props.bgSize;
      }
      if (!isNil(props.bgColor)) {
        opt.color = props.bgColor;
      }
      if (!isNil(props.borderRadius)) {
        opt.borderRadius = props.borderRadius;
      }
      if (!isNil(props.color)) {
        opt.iconColor = props.color;
      }
    } else {
      if (!isNil(props.color)) {
        opt.color = props.color;
      }
      if (!isNil(props.depth)) {
        opt.depth = props.depth;
      }
      if (!isNil(props.size)) {
        opt.size = props.size;
      }
    }
    return opt;
  });

  // 加载图标集
  // addCollection(tabler);

  // 加载图标
  const loadIconByName = (name: string) => loadIcon(name).catch(() => console.error(`Failed to load icon ${name}`));

  const icon = ref<void | Required<IconifyIcon> | string>();

  /**
   * 设置图标
   * Set icon
   * @param name 图标名称
   *             Icon name
   */
  function setIcon(name: string | undefined) {
    if (!isNil(name)) {
      name = normalizeIconName(name as string); // 使用按需引入组件模式
      loadIconByName(name).then(res => (icon.value = res));  // 使用在线模式
      // icon.value = getIcon(name) as Required<IconifyIcon>; // 使用addCollection图标集模式
      // icon.value = toComponentName(name); // 使用unplugin-icons按需导入，需要配置映射表
    }
  }

  // 监听 icon 属性变化
  watchEffect(() => {
    if (!isNil(props.icon)) {
      setIcon(props.icon);
    } else {
      setIcon(props.name);
    }
  });
</script>
