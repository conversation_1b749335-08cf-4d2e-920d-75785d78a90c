import { describe, expect, it } from "vitest";
import type { QueryCriteria } from "-/tenant";

// 模拟查询字符串构建函数
function buildQueryPart(field: string, value: string | number, operator: string = "="): string {
  return `${field}${operator}${encodeURIComponent(value)}`;
}

function buildQueryString(queryCriteria?: QueryCriteria): string {
  if (!queryCriteria) {
    return "";
  }

  const queryParts: string[] = [];

  // 处理各个查询字段
  if (queryCriteria.name) {
    // 租户名称使用模糊匹配
    queryParts.push(buildQueryPart("name", queryCriteria.name, "=~"));
  }

  if (queryCriteria.status) {
    // 状态使用精确匹配
    queryParts.push(buildQueryPart("status", queryCriteria.status));
  }

  if (queryCriteria.tenant_id) {
    // 租户ID使用精确匹配
    queryParts.push(buildQueryPart("tenant_id", queryCriteria.tenant_id));
  }

  // 如果有查询条件，返回 q=xxx 格式
  return queryParts.length > 0 ? `q=${queryParts.join(",")}` : "";
}

describe("查询字符串构建功能", () => {
  it("应该正确构建单个字段的查询字符串", () => {
    const criteria: QueryCriteria = {
      name: "测试租户",
    };

    const result = buildQueryString(criteria);
    expect(result).toBe("q=name=~%E6%B5%8B%E8%AF%95%E7%A7%9F%E6%88%B7");
  });

  it("应该正确构建多个字段的查询字符串", () => {
    const criteria: QueryCriteria = {
      name: "测试租户",
      status: "active",
      tenant_id: "tenant_001",
    };

    const result = buildQueryString(criteria);
    expect(result).toBe("q=name=~%E6%B5%8B%E8%AF%95%E7%A7%9F%E6%88%B7,status=active,tenant_id=tenant_001");
  });

  it("应该正确处理状态字段的精确匹配", () => {
    const criteria: QueryCriteria = {
      status: "inactive",
    };

    const result = buildQueryString(criteria);
    expect(result).toBe("q=status=inactive");
  });

  it("应该正确处理租户ID的精确匹配", () => {
    const criteria: QueryCriteria = {
      tenant_id: "tenant_002",
    };

    const result = buildQueryString(criteria);
    expect(result).toBe("q=tenant_id=tenant_002");
  });

  it("应该正确处理空查询条件", () => {
    const result1 = buildQueryString();
    const result2 = buildQueryString({});

    expect(result1).toBe("");
    expect(result2).toBe("");
  });

  it("应该正确处理包含特殊字符的查询条件", () => {
    const criteria: QueryCriteria = {
      name: "测试&租户#123",
    };

    const result = buildQueryString(criteria);
    expect(result).toBe("q=name=~%E6%B5%8B%E8%AF%95%26%E7%A7%9F%E6%88%B7%23123");
  });

  it("buildQueryPart函数应该正确处理不同操作符", () => {
    expect(buildQueryPart("name", "test", "=~")).toBe("name=~test");
    expect(buildQueryPart("status", "active")).toBe("status=active");
    expect(buildQueryPart("count", 10, ">")).toBe("count>10");
    expect(buildQueryPart("price", 100.5, "<=")).toBe("price<=100.5");
  });

  it("buildQueryPart函数应该正确编码特殊字符", () => {
    expect(buildQueryPart("name", "测试 & 租户", "=~")).toBe("name=~%E6%B5%8B%E8%AF%95%20%26%20%E7%A7%9F%E6%88%B7");
    expect(buildQueryPart("description", "包含#号", "=~")).toBe("description=~%E5%8C%85%E5%90%AB%23%E5%8F%B7");
  });
});
