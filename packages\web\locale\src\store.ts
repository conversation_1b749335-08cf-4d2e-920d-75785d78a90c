import { LOCALES_STORE_KEY } from "@celeris/constants";
import { useLocalStorage } from "@celeris/hooks";
import { computed } from "vue";

const store = useLocalStorage(LOCALES_STORE_KEY, "zh");

/**
 * 设置国际化语言
 * Set the locale
 * @param locale 语言
 */
export function setLocale(locale: string) {
  store.value = locale;
}

/**
 * 获取国际化语言
 * Get the locale
 */
export const getLocale = computed(() => store.value);
