<template>
  <NTreeSelect
    v-bind="$attrs"
    key-field="id"
    label-field="name"
    filterable
    block-line
    virtual-scroll
    :loading="loading"
    :options="dataSource as unknown as TreeSelectOption[]"
    :expanded-keys="expandedKeys"
    :show-irrelevant-nodes="false"
    :consistent-menu-width="false"
    @update:expanded-keys="handleUpdateExpandedKeys"
  />
</template>

<script setup lang="ts">
  import type { TreeItem } from "-/common";
  import type { TreeSelectOption } from "@celeris/ca-components";
  import { isFunction } from "@celeris/utils";
  import { cloneDeep } from "lodash-es";
  // import { usePermissionStore } from "~/store/modules/permission";
  import { useUserStore } from "~/store/modules/user";

  const props = withDefaults(defineProps<{
    requirePermission: boolean;
    handleDataSource?: (dataSource: TreeItem[]) => TreeItem[];
  }>(), {
    requirePermission: true,
  });

  const emit = defineEmits<{
    (e: "loadSuccess", data: TreeItem[]): void;
  }>();

  // const permissionStore = usePermissionStore();
  // const { getTeam } = storeToRefs(permissionStore);
  const userStore = useUserStore();

  // const { getAllUserTree, getUserTree, getAllUserTreeLoading, getUserTreeLoading } = storeToRefs(userStore);
  const { getAllUserTree, getAllUserTreeLoading } = storeToRefs(userStore);

  // 树节点数据
  const dataSource = computed(() => {
    // const _dataSource = props.requirePermission ? getUserTree.value : getAllUserTree.value;
    const _dataSource = getAllUserTree.value;
    return isFunction(props.handleDataSource) ? props.handleDataSource(cloneDeep(_dataSource)) : _dataSource;
  });

  // const loading = computed(() => props.requirePermission ? getUserTreeLoading.value : getAllUserTreeLoading.value);
  const loading = computed(() => getAllUserTreeLoading.value);

  // 修改为接受 string 或 number 类型
  const expandedKeys = ref<(string | number)[]>([]);

  onMounted(loadData);

  /**
   * 加载用户树数据，根据权限决定调用不同的接口。
   * @returns Promise<void>
   */
  async function loadData() {
    try {
      // if (props.requirePermission) {
      //   const params = {
      //     team_ids: getTeam.value?.join?.(",") || "",
      //   };
      //   await userStore.fetchUserTree(params);
      // } else {
      await userStore.fetchAllUserTree();
      // }

      if (dataSource.value.length) {
        expandedKeys.value = expandedKeys.value.length ? expandedKeys.value : dataSource.value.map(item => item.id);
      } else {
        expandedKeys.value = [];
      }
      emit("loadSuccess", dataSource.value);
    } catch (e) {
      console.error("userTreeSelect Load error", e);
    }
  }

  /**
   * 处理树节点展开 keys 的更新。
   * @param keys - 展开的节点 key 数组
   */
  function handleUpdateExpandedKeys(keys: (string | number)[]) {
    expandedKeys.value = keys;
  }
</script>

<style scoped>

</style>
