import type { RouteRecordRaw } from "vue-router";
import { PageConstants } from "@celeris/constants";
import { loadRoutesFromModules } from "@celeris/utils";
import { PAGE_NOT_FOUND_ROUTE, REDIRECT_ROUTE } from "~/router/routes/basic";

const modules = import.meta.glob<{ default: any }>("./modules/**/*.ts", { eager: true });
const routeModuleList: RouteRecordRaw[] = loadRoutesFromModules(modules);

export const asyncRoutes = [PAGE_NOT_FOUND_ROUTE, ...routeModuleList];

// 根路由
export const RootRoute: RouteRecordRaw = {
  path: "/",
  name: "Root",
  redirect: PageConstants.BASE_HOME,
  meta: {
    title: "Root",
  },
};

// 登录页
export const LoginRoute: RouteRecordRaw = {
  path: PageConstants.BASE_LOGIN,
  name: "Login",
  component: () => import("~/pages/login/index.vue"),
  meta: {
    title: "Login",
  },
};

// 找回密码
export const PasswordFindRoute: RouteRecordRaw = {
  path: '/find-password',
  name: 'FindPassword',
  component: () => import('~/pages/login/FindPassword.vue'),
};

// 租户选择页
export const TenantSelectRoute: RouteRecordRaw = {
  path: '/tenant-select',
  name: 'TenantSelect',
  component: () => import('~/pages/tenant-select/index.vue'),
  meta: {
    title: '选择租户',
  },
};

// Basic routing without permission
// 无需认证的基本路由
export const basicRoutes = [
  RootRoute,
  LoginRoute,
  PasswordFindRoute,
  TenantSelectRoute,
  REDIRECT_ROUTE,
  PAGE_NOT_FOUND_ROUTE,
];
