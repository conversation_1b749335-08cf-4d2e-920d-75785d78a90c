<template>
  <PageWrapper>
    <NTabs v-model:value="activeTab" type="line" animated>
      <NTabPane name="behavior" tab="行为" display-directive="show">
        <BehaviorTab />
      </NTabPane>
      <NTabPane name="analysis" tab="大模型解读结果">
        <AnalysisTab />
      </NTabPane>
    </NTabs>
  </PageWrapper>
</template>

<script setup lang="ts">
  import PageWrapper from "~/component/PageWrapper/src/PageWrapper.vue";
  import AnalysisTab from "./components/AnalysisTab.vue";
  import BehaviorTab from "./components/BehaviorTab.vue";

  const activeTab = ref("behavior");
</script>
