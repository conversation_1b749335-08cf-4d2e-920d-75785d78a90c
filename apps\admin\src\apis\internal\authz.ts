import type { Authz, AuthzExpanded, AuthzParam } from "-/authz";
import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";
import { replaceUrlPathParams } from "@celeris/utils";

enum API {
  Authzs = "/v1/projects/{domainId}/authzs",
  DeleteAuthzs = "/v1/projects/{domainId}/authzs/{authzId}",
  AuthzUser = "/v1/projects/{domainId}/authzs/user",
}

/**
 * 获取用户授权列表
 * @param domainId - 项目/领域ID
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function getUserAuthzList(domainId: string, params: AuthzParam = {}, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.Authzs, { domainId });
  return request.get({ url, params }, { errorMessageMode, shouldTransformResponse: false });
}

/**
 * 获取可选用户的信息列表
 * @param domainId - 项目/领域ID
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function getUserListByKeyword(domainId: string, params: { keyword: string }, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.AuthzUser, { domainId });
  return request.get({ url, params }, { errorMessageMode, shouldTransformResponse: false });
}

/**
 * 新增用户授权
 * @param domainId - 项目/领域ID
 * @param data - 用户授权数据
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function createUserAuthz(domainId: string, data: Authz[], errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.Authzs, { domainId });
  return request.post({ url, data }, { errorMessageMode });
}

/**
 * 更新用户授权
 * @param domainId - 项目/领域ID
 * @param data - 用户授权数据
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function updateUserAuthz(domainId: string, data: AuthzExpanded[], errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.Authzs, { domainId });
  return request.put({ url, data }, { errorMessageMode });
}

/**
 * 删除用户授权
 * @param domainId - 项目/领域ID
 * @param data - 用户授权数据
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function deleteUserAuthz(domainId: string, authzId: number, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.DeleteAuthzs, { domainId, authzId });
  return request.delete({ url }, { errorMessageMode });
}
