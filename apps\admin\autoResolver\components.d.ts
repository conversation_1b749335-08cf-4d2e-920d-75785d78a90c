/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    CAAppLogo: typeof import('@celeris/components')['CAAppLogo']
    CAAppNaiveUIProvider: typeof import('@celeris/components')['CAAppNaiveUIProvider']
    CAIcon: typeof import('@celeris/components')['CAIcon']
    NAvatar: typeof import('@celeris/ca-components')['NAvatar']
    NBadge: typeof import('@celeris/ca-components')['NBadge']
    NBreadcrumb: typeof import('@celeris/ca-components')['NBreadcrumb']
    NBreadcrumbItem: typeof import('@celeris/ca-components')['NBreadcrumbItem']
    NButton: typeof import('@celeris/ca-components')['NButton']
    NCard: typeof import('@celeris/ca-components')['NCard']
    NConfigProvider: typeof import('@celeris/ca-components')['NConfigProvider']
    NDataTable: typeof import('@celeris/ca-components')['NDataTable']
    NDatePicker: typeof import('@celeris/ca-components')['NDatePicker']
    NDescriptions: typeof import('@celeris/ca-components')['NDescriptions']
    NDescriptionsItem: typeof import('@celeris/ca-components')['NDescriptionsItem']
    NDialogProvider: typeof import('@celeris/ca-components')['NDialogProvider']
    NDivider: typeof import('@celeris/ca-components')['NDivider']
    NDrawer: typeof import('@celeris/ca-components')['NDrawer']
    NDrawerContent: typeof import('@celeris/ca-components')['NDrawerContent']
    NDropdown: typeof import('@celeris/ca-components')['NDropdown']
    NEl: typeof import('@celeris/ca-components')['NEl']
    NEmpty: typeof import('@celeris/ca-components')['NEmpty']
    NFle: typeof import('@celeris/ca-components')['NFle']
    NFlex: typeof import('@celeris/ca-components')['NFlex']
    NFloatButton: typeof import('@celeris/ca-components')['NFloatButton']
    NForm: typeof import('@celeris/ca-components')['NForm']
    NFormItem: typeof import('@celeris/ca-components')['NFormItem']
    NGlobalStyle: typeof import('@celeris/ca-components')['NGlobalStyle']
    NInput: typeof import('@celeris/ca-components')['NInput']
    NInputGroup: typeof import('@celeris/ca-components')['NInputGroup']
    NLoadingBarProvider: typeof import('@celeris/ca-components')['NLoadingBarProvider']
    NMenu: typeof import('@celeris/ca-components')['NMenu']
    NMessageProvider: typeof import('@celeris/ca-components')['NMessageProvider']
    NModal: typeof import('@celeris/ca-components')['NModal']
    NNotificationProvider: typeof import('@celeris/ca-components')['NNotificationProvider']
    NPageHeader: typeof import('@celeris/ca-components')['NPageHeader']
    NPagination: typeof import('@celeris/ca-components')['NPagination']
    NPopover: typeof import('@celeris/ca-components')['NPopover']
    NResult: typeof import('@celeris/ca-components')['NResult']
    NS: typeof import('@celeris/ca-components')['NS']
    NScrollbar: typeof import('@celeris/ca-components')['NScrollbar']
    NSelect: typeof import('@celeris/ca-components')['NSelect']
    NSpace: typeof import('@celeris/ca-components')['NSpace']
    NSpin: typeof import('@celeris/ca-components')['NSpin']
    NStatistic: typeof import('@celeris/ca-components')['NStatistic']
    NTab: typeof import('@celeris/ca-components')['NTab']
    NTable: typeof import('@celeris/ca-components')['NTable']
    NTabPane: typeof import('@celeris/ca-components')['NTabPane']
    NTabs: typeof import('@celeris/ca-components')['NTabs']
    NTag: typeof import('@celeris/ca-components')['NTag']
    NText: typeof import('@celeris/ca-components')['NText']
    NTooltip: typeof import('@celeris/ca-components')['NTooltip']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
