{"name": "@celeris/constants", "type": "module", "version": "0.0.3", "description": "constants for <PERSON><PERSON><PERSON>", "author": "<PERSON> (https://github.com/kirklin)", "license": "MIT", "homepage": "https://github.com/kirklin/celeris-web", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./index.ts", "module": "./index.ts", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --stub", "clean": "pnpm rimraf node_modules && pnpm rimraf dist", "prepublishOnly": "npm run build", "postinstall": "npm run build"}, "dependencies": {"lodash.pick": "4.4.0"}}