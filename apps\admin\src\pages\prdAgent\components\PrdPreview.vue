<template>
  <NFlex vertical class="h-full">
    <!-- 头部区域 -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex-shrink-0">
      <!-- 左侧标题 -->
      <div class="flex items-center gap-3">
        <CAIcon icon="tabler:file-text" class="text-blue-600 text-xl" />
        <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {{ displayTitle }}的PRD
        </div>
      </div>

      <!-- 右侧导出按钮 -->
      <div class="flex items-center gap-2">
        <NButton type="primary" size="medium" :loading="exportLoading" @click="handleExport">
          <template #icon>
            <CAIcon icon="tabler:download" />
          </template>
          全部导出
        </NButton>
      </div>
    </div>

    <!-- 内容区域 -->
    <NScrollbar ref="scrollbarRef" class="flex-1">
      <NFlex vertical :size="16">
        <!-- PRD正文 - 使用独立的PrdMainContent组件 -->
        <PrdMainContent ref="mainContentRef" :prd-data="prdData" @regenerate="handleMainContentRegenerate" />

        <!-- 动态渲染其他PrdContent组件 -->
        <PrdContent
          v-for="config in contentConfigs"
          :id="config.id"
          :key="config.id"
          :title="config.title"
          :max-height="sections[config.sectionKey] ? '300px' : undefined"
          :show-empty-state="!prdData.content"
          :show-generate-state="!!prdData.content && !sections[config.sectionKey]"
          :generate-state-icon="config.icon"
          :generate-state-description="config.description"
          @generate="() => handleGenerate(config.sectionKey)"
        >
          <template v-if="config.showExportButton && prdData.content" #button>
            <NButton size="small" type="primary" ghost @click="() => handleSectionExport(config.sectionKey)">
              <template #icon>
                <CAIcon icon="tabler:download" />
              </template>
              导出
            </NButton>
          </template>

          <div v-if="sections[config.sectionKey]" class="p-4 h-full overflow-y-auto" style="max-height: 100%;">
            <div class="max-h-full overflow-hidden">
              <MdPreview :text="sections[config.sectionKey]" />
            </div>
          </div>
        </PrdContent>
      </NFlex>
    </NScrollbar>
  </NFlex>
</template>

<script setup lang="ts">
  import { CAIcon } from "@celeris/components";
  import { NButton, NScrollbar, useMessage } from "naive-ui";
  import { computed, nextTick, ref } from "vue";
  import { MdPreview } from "~/component/MdPreview";
  import { contentConfigs } from "../config";
  import PrdContent from "./PrdContent.vue";
  import PrdMainContent from "./PrdMainContent.vue";

  // PRD数据结构
  interface PrdData {
    title: string;
    content: string;
  }

  // Props定义
  interface Props {
    title?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    title: "",
  });

  const emits = defineEmits<{
    (e: "regenerateMainContent"): void;
  }>();

  // 初始化空数据
  const prdData = ref<PrdData>({
    title: "",
    content: "",
  });

  const sections = ref<Record<string, string>>({});
  const scrollbarRef = ref<InstanceType<typeof NScrollbar>>();
  const mainContentRef = ref<InstanceType<typeof PrdMainContent> | null>(null);
  const exportLoading = ref(false);
  const message = useMessage();

  // Markdown转HTML的函数
  async function convertMarkdownToHtml(markdown: string): Promise<string> {
    // 预处理：分离代码块，避免在代码块内进行转换
    const codeBlocks: string[] = [];
    const processedMarkdown = markdown.replace(/```([\s\S]*?)```/g, (_, code) => {
      const index = codeBlocks.length;
      codeBlocks.push(code);
      return `__CODE_BLOCK_${index}__`;
    });

    // 使用正则表达式转换常见的Markdown语法
    let html = processedMarkdown
      // 标题转换（支持更多级别）
      .replace(/^#### (.*$)/gm, "<h4>$1</h4>")
      .replace(/^### (.*$)/gm, "<h3>$1</h3>")
      .replace(/^## (.*$)/gm, "<h2>$1</h2>")
      .replace(/^# (.*$)/gm, "<h1>$1</h1>")
      // 图片（需要特殊处理）
      .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (_, alt, src) => {
        // 检查是否是base64图片或网络图片
        if (src.startsWith("data:") || src.startsWith("http")) {
          return `<img src="${src}" alt="${alt}" style="max-width: 100%; height: auto;" />`;
        } else {
          // 对于本地图片，显示占位符
          return `<p><strong>[图片: ${alt || src}]</strong></p>`;
        }
      })
      // 链接
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, "<a href=\"$2\">$1</a>")
      // 粗体和斜体
      .replace(/\*\*\*(.*?)\*\*\*/g, "<strong><em>$1</em></strong>")
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
      .replace(/\*(.*?)\*/g, "<em>$1</em>")
      // 删除线
      .replace(/~~(.*?)~~/g, "<del>$1</del>")
      // 行内代码
      .replace(/`(.*?)`/g, "<code>$1</code>")
      // 水平线
      .replace(/^---$/gm, "<hr>")
      .replace(/^\*\*\*$/gm, "<hr>");

    // 处理表格
    html = convertMarkdownTables(html);

    // 处理列表（需要更复杂的逻辑）
    html = convertMarkdownLists(html);

    // 恢复代码块
    codeBlocks.forEach((code, index) => {
      html = html.replace(`__CODE_BLOCK_${index}__`, `<pre><code>${code}</code></pre>`);
    });

    // 处理段落
    html = html
      .replace(/\n\n/g, "</p><p>")
      .replace(/\n/g, "<br>");

    // 包装在段落标签中，但避免在已有块级元素中包装
    const blockElements = /<(?:h[1-6]|div|p|ul|ol|li|table|tr|td|th|pre|hr)[^>]*>/i;
    if (!blockElements.test(html)) {
      html = `<p>${html}</p>`;
    }

    return html;
  }

  // 转换Markdown表格
  function convertMarkdownTables(html: string): string {
    const tableRegex = /(\|[^\n\r|\u2028\u2029]*\|.*\n)+/g;
    return html.replace(tableRegex, (match) => {
      const rows = match.trim().split("\n");
      if (rows.length < 2) {
        return match;
      }

      let tableHtml = "<table>";

      // 处理表头
      const headerRow = rows[0];
      const headers = headerRow.split("|").map(cell => cell.trim()).filter(cell => cell);
      if (headers.length > 0) {
        tableHtml += "<thead><tr>";
        headers.forEach((header) => {
          tableHtml += `<th>${header}</th>`;
        });
        tableHtml += "</tr></thead>";
      }

      // 跳过分隔行（第二行）
      if (rows.length > 2) {
        tableHtml += "<tbody>";
        for (let i = 2; i < rows.length; i++) {
          const row = rows[i];
          const cells = row.split("|").map(cell => cell.trim()).filter(cell => cell);
          if (cells.length > 0) {
            tableHtml += "<tr>";
            cells.forEach((cell) => {
              tableHtml += `<td>${cell}</td>`;
            });
            tableHtml += "</tr>";
          }
        }
        tableHtml += "</tbody>";
      }

      tableHtml += "</table>";
      return tableHtml;
    });
  }

  // 转换Markdown列表
  function convertMarkdownLists(html: string): string {
    // 处理无序列表
    html = html.replace(/^[*\-] (.+)$/gm, "<li>$1</li>");
    // 处理有序列表
    html = html.replace(/^\d+\. (.+)$/gm, "<li>$1</li>");

    // 将连续的li标签包装在ul或ol中
    html = html.replace(/(<li>.*<\/li>\s*)+/gs, (match) => {
      return `<ul>${match}</ul>`;
    });

    return html;
  }

  // HTML转DOCX的函数
  async function convertHtmlToDocx(html: string, filename: string): Promise<void> {
    // 创建一个简单的Word文档HTML结构
    const wordHtml = `
      <!DOCTYPE html>
      <html xmlns:o="urn:schemas-microsoft-com:office:office"
            xmlns:w="urn:schemas-microsoft-com:office:word"
            xmlns="http://www.w3.org/TR/REC-html40">
      <head>
        <meta charset="utf-8">
        <title>${filename}</title>
        <!--[if gte mso 9]>
        <xml>
          <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>90</w:Zoom>
            <w:DoNotPromptForConvert/>
            <w:DoNotShowInsertionsAndDeletions/>
          </w:WordDocument>
        </xml>
        <![endif]-->
        <style>
          body { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12pt; line-height: 1.6; }
          h1 { font-size: 18pt; font-weight: bold; margin: 20pt 0 10pt 0; }
          h2 { font-size: 16pt; font-weight: bold; margin: 16pt 0 8pt 0; }
          h3 { font-size: 14pt; font-weight: bold; margin: 12pt 0 6pt 0; }
          p { margin: 6pt 0; }
          ul, ol { margin: 6pt 0; padding-left: 20pt; }
          li { margin: 3pt 0; }
          code { background-color: #f5f5f5; padding: 2pt 4pt; font-family: 'Courier New', monospace; }
          pre { background-color: #f5f5f5; padding: 8pt; margin: 6pt 0; font-family: 'Courier New', monospace; }
          table { border-collapse: collapse; width: 100%; margin: 6pt 0; }
          th, td { border: 1pt solid #ddd; padding: 6pt; text-align: left; }
          th { background-color: #f2f2f2; font-weight: bold; }
        </style>
      </head>
      <body>
        ${html}
      </body>
      </html>
    `;

    // 创建Blob并下载
    const blob = new Blob([wordHtml], {
      type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    });

    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `${filename.replace(/[<>:"/\\|?*]/g, "_")}.doc`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  // 计算显示标题
  const displayTitle = computed(() => {
    return props.title || prdData.value.title || "PRD文档";
  });

  // 导出功能
  async function handleExport() {
    if (!prdData.value.content && Object.keys(sections.value).length === 0) {
      message.warning("暂无内容可导出");
      return;
    }

    exportLoading.value = true;
    try {
      // 构建完整的PRD内容
      let fullContent = "";

      // 添加标题
      const title = displayTitle.value;
      fullContent += `# ${title}\n\n`;

      // 添加PRD正文内容
      if (prdData.value.content) {
        fullContent += `## PRD正文\n\n${prdData.value.content}\n\n`;
      }

      // 添加其他模块内容
      for (const [sectionKey, sectionContent] of Object.entries(sections.value)) {
        if (sectionContent && sectionContent.trim()) {
          fullContent += `## ${sectionKey}\n\n${sectionContent}\n\n`;
        }
      }

      // 将Markdown转换为HTML
      const htmlContent = await convertMarkdownToHtml(fullContent);

      // 将HTML转换为Word文档
      await convertHtmlToDocx(htmlContent, title);

      message.success("导出成功");
    } catch (error) {
      console.error("导出失败:", error);
      message.error("导出失败，请重试");
    } finally {
      exportLoading.value = false;
    }
  }

  // 更新PRD内容的方法，供父组件调用
  function updateContent(data: PrdData) {
    prdData.value = data;
  }

  // 更新sections内容的方法，供父组件调用
  function updateSections(newSections: Record<string, string>) {
    sections.value = { ...newSections };
  }

  // 各个模块的生成处理方法
  function handleGenerate(sectionKey: string) {
    console.warn(`生成${sectionKey}模块功能待实现`);
    // TODO: 实现各个模块的生成功能
  }

  // 处理主要内容重新生成
  function handleMainContentRegenerate() {
    // 通知父组件重新生成PRD主要内容
    emits("regenerateMainContent");
  }

  function handleSectionExport(sectionKey: string) {
    console.warn(`导出${sectionKey}模块功能待实现`);
    // TODO: 实现各个模块的导出功能
  }

  // 滚动到指定章节
  function scrollToSection(sectionTitle: string) {
    // 等待DOM更新后再执行滚动
    nextTick(() => {
      // 基于配置数组动态生成映射关系
      const sectionIdMap: Record<string, string> = {
        // PRD正文的特殊映射关系
        PRD正文: "prd-content-main",
        概述: "prd-content-main",
        用户使用旅程: "prd-content-main",
        相关页面: "prd-content-main",
        功能详细描述: "prd-content-main",
      };

      // 从配置数组中动态添加映射关系
      contentConfigs.forEach((config) => {
        // 添加标题映射
        sectionIdMap[config.title] = config.id;
        // 添加带"生成"后缀的映射
        sectionIdMap[`${config.title} 生成`] = config.id;
      });

      const targetId = sectionIdMap[sectionTitle];
      if (targetId && scrollbarRef.value) {
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
          // 计算目标元素的offsetTop位置
          const offsetTop = targetElement.offsetTop;

          // 使用NScrollbar的scrollTo方法
          scrollbarRef.value.scrollTo({ top: offsetTop, behavior: "smooth" });
        }
      }
    });
  }
  // 调用子组件的setRegenerateLoading方法的函数，并暴露给父组件
  function setMainContentRegenerateLoading(value: boolean) {
    mainContentRef.value?.setRegenerateLoading(value);
  }

  // 暴露方法给父组件
  defineExpose({
    updateContent,
    updateSections,
    scrollToSection,
    setMainContentRegenerateLoading,
  });
</script>

<style scoped>
/* 自定义滚动条样式 */
:deep(.overflow-y-auto) {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

:deep(.overflow-y-auto):hover {
  scrollbar-color: #555 #f1f1f1;
}
</style>
