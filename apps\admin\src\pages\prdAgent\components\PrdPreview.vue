<template>
  <NFlex vertical class="h-full">
    <!-- 头部区域 -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex-shrink-0">
      <!-- 左侧标题 -->
      <div class="flex items-center gap-3">
        <CAIcon icon="tabler:file-text" class="text-blue-600 text-xl" />
        <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {{ displayTitle }}的PRD
        </div>
      </div>

      <!-- 右侧导出按钮 -->
      <div class="flex items-center gap-2">
        <NButton type="primary" size="medium" :loading="exportLoading" @click="handleExport">
          <template #icon>
            <CAIcon icon="tabler:download" />
          </template>
          全部导出
        </NButton>
      </div>
    </div>

    <!-- 内容区域 -->
    <NScrollbar ref="scrollbarRef" class="flex-1">
      <NFlex vertical :size="16">
        <!-- PRD正文 - 使用独立的PrdMainContent组件 -->
        <PrdMainContent :prd-data="prdData" />

        <!-- 动态渲染其他PrdContent组件 -->
        <PrdContent
          v-for="config in contentConfigs"
          :id="config.id"
          :key="config.id"
          :title="config.title"
          :max-height="sections[config.sectionKey] ? '300px' : undefined"
          :show-empty-state="!prdData.content"
          :show-generate-state="!!prdData.content && !sections[config.sectionKey]"
          :generate-state-icon="config.icon"
          :generate-state-description="config.description"
          @generate="() => handleGenerate(config.sectionKey)"
        >
          <template v-if="config.showExportButton && prdData.content" #button>
            <NButton size="small" type="primary" ghost @click="() => handleSectionExport(config.sectionKey)">
              <template #icon>
                <CAIcon icon="tabler:download" />
              </template>
              导出
            </NButton>
          </template>

          <div v-if="sections[config.sectionKey]" class="p-4 h-full overflow-y-auto" style="max-height: 100%;">
            <div class="max-h-full overflow-hidden">
              <MdPreview :text="sections[config.sectionKey]" />
            </div>
          </div>
        </PrdContent>
      </NFlex>
    </NScrollbar>
  </NFlex>
</template>

<script setup lang="ts">
  import { CAIcon } from "@celeris/components";
  import { NButton, NScrollbar, useMessage } from "naive-ui";
  import { computed, nextTick, ref } from "vue";
  import { MdPreview } from "~/component/MdPreview";
  import { contentConfigs } from "../config";
  import PrdContent from "./PrdContent.vue";
  import PrdMainContent from "./PrdMainContent.vue";

  // PRD数据结构
  interface PrdData {
    title: string;
    content: string;
  }

  // Props定义
  interface Props {
    title?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    title: "",
  });

  // 初始化空数据
  const prdData = ref<PrdData>({
    title: "",
    content: "",
  });

  const sections = ref<Record<string, string>>({});
  const scrollbarRef = ref<InstanceType<typeof NScrollbar>>();
  const exportLoading = ref(false);
  const message = useMessage();

  // 计算显示标题
  const displayTitle = computed(() => {
    return props.title || prdData.value.title || "PRD文档";
  });

  // 导出功能
  async function handleExport() {
    if (!prdData.value.content && Object.keys(sections.value).length === 0) {
      message.warning("暂无内容可导出");
      return;
    }

    exportLoading.value = true;
    try {
      // 构建完整的PRD内容
      let fullContent = `# ${displayTitle.value}\n\n`;

      if (prdData.value.content) {
        fullContent += `${prdData.value.content}\n\n`;
      }

      // 添加各个章节内容
      Object.entries(sections.value).forEach(([key, content]) => {
        if (content) {
          const config = contentConfigs.find(c => c.sectionKey === key);
          if (config) {
            fullContent += `## ${config.title}\n\n${content}\n\n`;
          }
        }
      });

      // 创建并下载文件
      const blob = new Blob([fullContent], { type: "text/markdown;charset=utf-8" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${displayTitle.value}.md`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success("导出成功");
    } catch (error) {
      console.error("导出失败:", error);
      message.error("导出失败，请重试");
    } finally {
      exportLoading.value = false;
    }
  }

  // 更新PRD内容的方法，供父组件调用
  function updateContent(data: PrdData) {
    prdData.value = data;
  }

  // 各个模块的生成处理方法
  function handleGenerate(sectionKey: string) {
    console.warn(`生成${sectionKey}模块功能待实现`);
    // TODO: 实现各个模块的生成功能
  }

  function handleSectionExport(sectionKey: string) {
    console.warn(`导出${sectionKey}模块功能待实现`);
    // TODO: 实现各个模块的导出功能
  }

  // 滚动到指定章节
  function scrollToSection(sectionTitle: string) {
    // 等待DOM更新后再执行滚动
    nextTick(() => {
      // 基于配置数组动态生成映射关系
      const sectionIdMap: Record<string, string> = {
        // PRD正文的特殊映射关系
        PRD正文: "prd-content-main",
        概述: "prd-content-main",
        用户使用旅程: "prd-content-main",
        相关页面: "prd-content-main",
        功能详细描述: "prd-content-main",
      };

      // 从配置数组中动态添加映射关系
      contentConfigs.forEach((config) => {
        // 添加标题映射
        sectionIdMap[config.title] = config.id;
        // 添加带"生成"后缀的映射
        sectionIdMap[`${config.title} 生成`] = config.id;
      });

      const targetId = sectionIdMap[sectionTitle];
      if (targetId && scrollbarRef.value) {
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
          // 计算目标元素的offsetTop位置
          const offsetTop = targetElement.offsetTop;

          // 使用NScrollbar的scrollTo方法
          scrollbarRef.value.scrollTo({ top: offsetTop, behavior: "smooth" });
        }
      }
    });
  }

  // 暴露方法给父组件
  defineExpose({
    updateContent,
    scrollToSection,
  });
</script>

<style scoped>
/* 自定义滚动条样式 */
:deep(.overflow-y-auto) {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

:deep(.overflow-y-auto):hover {
  scrollbar-color: #555 #f1f1f1;
}
</style>
