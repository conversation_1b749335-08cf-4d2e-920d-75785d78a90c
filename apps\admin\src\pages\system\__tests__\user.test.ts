import { mount } from "@vue/test-utils";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { nextTick } from "vue";
import { deleteTeamApi } from "~/apis/internal/team";
import { deleteUserApi, updateUserApi } from "~/apis/internal/user";
import Index from "../User/index.vue";

// mock 数据
const mockUser = {
  id: 1,
  username: "u1",
  nickname: "张三",
  email: "<EMAIL>",
  telephone: "***********",
  preserved: 0,
  job_title: "工程师",
  hr_com: "正式",
  position: "开发",
  group_id: "g1",
  group: "研发部",
  group_leader: "u2",
  group_leader_name: "李四",
  group_lines: ["公司", "研发部"],
  create_time: "2023-01-01",
  entry_time: "2023-01-01",
  account_expired: "2024-01-01",
  password_expired: "2024-06-01",
  key: "u1",
  deleteLoading: false,
};
const mockTeam = { id: "g1", label: "研发部", children: [] } as any;

// mock 依赖
const messageMock = {
  success: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
  warning: vi.fn(),
};

vi.mock("~/apis/internal/user", () => {
  return {
    userListApi: vi.fn().mockResolvedValue({ data: [{}] }),
    updateUserApi: vi.fn().mockResolvedValue({}),
    deleteUserApi: vi.fn().mockResolvedValue({}),
  };
});
vi.mock("~/apis/internal/team", () => {
  return {
    deleteTeamApi: vi.fn().mockResolvedValue({}),
  };
});
vi.mock("@celeris/ca-components", async (importOriginal) => {
  const actual = await importOriginal();
  const dialogInfoMock = vi.fn().mockImplementation(({ onPositiveClick }) => onPositiveClick && onPositiveClick());
  const dialogWarningMock = vi.fn().mockImplementation(({ onPositiveClick }) => onPositiveClick && onPositiveClick());
  return {
    ...actual,
    useMessage: () => messageMock,
    useDialog: () => ({
      info: dialogInfoMock,
      warning: dialogWarningMock,
    }),
  };
});
const loadDataMock = vi.fn();
const refreshDataMock = vi.fn();
const reloadDataMock = vi.fn();
const handlePageChangeMock = vi.fn();
const handlePageSizeChangeMock = vi.fn();
vi.mock("~/composables/useTable", () => {
  return {
    useTable: () => ({
      tableLoading: ref(false),
      tableData: ref([mockUser]),
      tablePagination: ref({ page: 1, pageSize: 10, itemCount: 1 }),
      loadData: loadDataMock,
      refreshData: refreshDataMock,
      reloadData: reloadDataMock,
      handlePageChange: handlePageChangeMock,
      handlePageSizeChange: handlePageSizeChangeMock,
      handleScroll: vi.fn(),
    }),
  };
});
vi.mock("~/store/modules/user", () => {
  return {
    useUserStore: () => ({
      getAllUserTree: [mockTeam],
      getAllUserTreeLoading: false,
      fetchAllUserTree: vi.fn(),
    }),
  };
});
// 子组件 stub
vi.mock("../User/components/UserFormModal.vue", () => ({
  default: { name: "UserFormModal", props: ["show"], template: "<div v-if='show' class='mock-user-form-modal'></div>" },
}));
vi.mock("../User/components/TeamFormModal.vue", () => ({
  default: { name: "TeamFormModal", props: ["show"], template: "<div v-if='show' class='mock-team-form-modal'></div>" },
}));
vi.mock("../User/components/CoverageProjectModal.vue", () => ({
  default: { name: "CoverageProjectModal", props: ["show"], template: "<div v-if='show' class='mock-coverage-project-modal'></div>" },
}));
vi.mock("../User/components/CoverageMachModal.vue", () => ({
  default: { name: "CoverageMachModal", props: ["show"], template: "<div v-if='show' class='mock-coverage-mach-modal'></div>" },
}));
vi.mock("../User/components/QueryHeader.vue", () => ({
  default: { name: "QueryHeader", props: ["value"], template: "<div class='mock-query-header'></div>" },
}));
vi.mock("~/component/UserTree/src/UserTree.vue", () => ({
  default: { name: "UserTree", template: "<div class='mock-user-tree'></div>" },
}));
vi.mock("~/component/PageWrapper/src/PageWrapper.vue", () => ({
  default: { name: "PageWrapper", template: "<div class='mock-page-wrapper'><slot /></div>" },
}));

function getCurrentMessageMock() {
  return messageMock;
}

describe("user 用户管理页", () => {
  let wrapper;
  beforeEach(async () => {
    wrapper = mount(Index, {});
    await nextTick();
  });

  it("应渲染主要结构和表格数据", async () => {
    expect(wrapper.findComponent({ name: "PageWrapper" }).exists()).toBe(true);
    expect(wrapper.findComponent({ name: "QueryHeader" }).exists()).toBe(true);
    expect(wrapper.text()).toContain("张三");
    expect(wrapper.text()).toContain("<EMAIL>");
  });

  it("handleUserTreeLoadSuccess 应加载表格", async () => {
    await wrapper.vm.handleUserTreeLoadSuccess();
    expect(loadDataMock).toHaveBeenCalled();
  });

  it("onPageChange/onPageSizeChange 应更新分页并加载", async () => {
    wrapper.vm.onPageChange(2);
    expect(handlePageChangeMock).toHaveBeenCalledWith(2);
    wrapper.vm.onPageSizeChange(50);
    expect(handlePageSizeChangeMock).toHaveBeenCalledWith(50);
  });

  it("formatter 各分支", () => {
    expect(wrapper.vm.formatter(mockUser, "preserved_user")).toBe("在职");
    expect(wrapper.vm.formatter(Object.assign({}, mockUser, { preserved: 1 }), "preserved_user")).toBe("离职");
    expect(wrapper.vm.formatter(mockUser, "group_lines")).toBe("公司/研发部");
    expect(wrapper.vm.formatter(mockUser, "entry_time")).not.toBe("--");
    expect(wrapper.vm.formatter({}, "unknown")).toBe("--");
  });

  it("renderSuffix 右键菜单", () => {
    const vnode = wrapper.vm.renderSuffix({ option: mockTeam });
    expect(vnode).toBeTruthy();
  });

  it("handleContextMenuSelect 分支", async () => {
    wrapper.vm.selectedTeam = Object.assign({}, mockTeam);
    await wrapper.vm.handleContextMenuSelect("add");
    expect(wrapper.vm.editDialogVisible).toBe(true);
    await wrapper.vm.handleContextMenuSelect("edit");
    expect(wrapper.vm.editDialogVisible).toBe(true);
    await wrapper.vm.handleContextMenuSelect("delete");
    expect(deleteTeamApi).toHaveBeenCalled();
  });

  it("addTeam/editTeam/deleteTeam/handleEditTeamPositiveClick", async () => {
    wrapper.vm.selectedTeam = Object.assign({}, mockTeam);
    wrapper.vm.addTeam();
    expect(wrapper.vm.editDialogVisible).toBe(true);
    wrapper.vm.editTeam();
    expect(wrapper.vm.editDialogVisible).toBe(true);
    await wrapper.vm.deleteTeam();
    expect(deleteTeamApi).toHaveBeenCalled();
    wrapper.vm.editDialogVisible = true;
    try {
      await wrapper.vm.handleEditTeamPositiveClick();
    } catch (e) {}
    expect(wrapper.vm.editDialogVisible).toBe(false);
  });

  it("handleSearch/handleUpdateExpandedKeys/handleUpdateSelectedKeys", async () => {
    wrapper.vm.handleSearch();
    expect(reloadDataMock).toHaveBeenCalled();
    wrapper.vm.handleUpdateExpandedKeys(["g1"]);
    expect(wrapper.vm.expandedKeys).toEqual(["g1"]);
    wrapper.vm.handleUpdateSelectedKeys(["g1"]);
    expect(wrapper.vm.selectedKeys).toEqual(["g1"]);
  });

  it("handleAdd/handleUpdate/openUserFormModal", () => {
    wrapper.vm.handleAdd();
    expect(wrapper.vm.userFormModalShow).toBe(true);
    wrapper.vm.handleUpdate(Object.assign({}, mockUser));
    expect(wrapper.vm.userFormModalShow).toBe(true);
    wrapper.vm.openUserFormModal();
    expect(wrapper.vm.userFormModalShow).toBe(true);
  });

  it("handleSelect/coverageProject/coverageMach/changeAccountState", async () => {
    const row = Object.assign({}, mockUser);
    wrapper.vm.coverageProject(row);
    expect(wrapper.vm.visible).toBe(true);
    wrapper.vm.coverageMach(row);
    expect(wrapper.vm.visibleMach).toBe(true);
    await wrapper.vm.changeAccountState(row);
    expect(updateUserApi).toHaveBeenCalled();
  });

  it("handlePositiveClick", async () => {
    await wrapper.vm.handlePositiveClick();
    expect(reloadDataMock).toHaveBeenCalled();
  });

  it("handleDelete", async () => {
    const row = Object.assign({}, mockUser);
    await wrapper.vm.handleDelete(row);
    expect(deleteUserApi).toHaveBeenCalled();
    expect(getCurrentMessageMock().success).toHaveBeenCalledWith("删除成功");
  });

  it("uI交互：点击新增、查询、删除等", async () => {
    // 新增用户按钮
    wrapper.vm.handleAdd();
    await nextTick();
    expect(wrapper.find(".mock-user-form-modal").exists()).toBe(true);
    // 查询按钮
    wrapper.vm.handleSearch();
    expect(reloadDataMock).toHaveBeenCalled();
    // 删除按钮
    const row = Object.assign({}, mockUser);
    await wrapper.vm.handleDelete(row);
    expect(deleteUserApi).toHaveBeenCalled();
  });
});
