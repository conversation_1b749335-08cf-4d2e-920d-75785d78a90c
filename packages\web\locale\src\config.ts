import type { I18nOptions } from "vue-i18n";
import { getLocale, setLocale } from "./store";

export interface LocalesOptions {
  locale: string;
  fallbackLocale: string;
  messagesHandler: AnyFn;
  otherOptions?: Partial<I18nOptions>;
}
export class LocalesConfiguration {
  private static options: LocalesOptions = {
    // locale: "zh",
    messagesHandler: () => {},
    locale: "zh",
    fallbackLocale: "zh",
  };

  /*
   * 配置国际化
   * Configure the locales
   */
  static configure(options: Partial<LocalesOptions>): void {
    this.options = { ...this.options, ...options };
  }

  /*
   * 获取国际化配置
   * Get the locales configuration
   */
  static getOptions(): LocalesOptions {
    return this.options;
  }

  /*
   * 获取国际化语言
   * Get the locale
   */
  static get locale(): string {
    return getLocale.value || this.options.locale;
  }

  /*
   * 获取国际化备用语言
   * Get the fallback locale
   */
  static get fallbackLocale(): string {
    return this.options.fallbackLocale || this.locale;
  }

  /*
   * 获取国际化消息处理函数
   * Get the messages handler
   */
  static get messagesHandler() {
    return this.options.messagesHandler;
  }

  /*
   * 获取其他国际化选项
   * Get the other locales options
   */
  static get otherOptions() {
    return this.options.otherOptions;
  }
}

export class LocalesEngine {
  /*
   * 初始化国际化
   * Initialize the locales
   */
  static initLocales(configureFunction: () => Partial<LocalesOptions>): void {
    LocalesConfiguration.configure(configureFunction());
  }

  /*
   * 设置国际化消息处理函数
   * Set the messages handler
   */
  static setMessagesHandler(messagesHandler: AnyFn): void {
    LocalesConfiguration.configure({ messagesHandler });
  }

  /*
   * 设置国际化语言
   * Set the locale
   */
  static setLocale(locale: string): void {
    setLocale(locale);
    LocalesConfiguration.configure({ locale });
  }

  /*
   * 设置国际化备用语言
   * Set the fallback locale
   */
  static setFallbackLocale(fallbackLocale: string): void {
    LocalesConfiguration.configure({ fallbackLocale });
  }

  /*
   * 设置其他国际化选项
   * Set the other locales options
   */
  static setOtherOptions(otherOptions: Partial<I18nOptions>): void {
    LocalesConfiguration.configure({ otherOptions });
  }
}
