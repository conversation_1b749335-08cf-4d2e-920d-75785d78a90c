<template>
  <CardModal
    v-bind="$attrs"
    class="update-password-modal-card w-xl"
    title="修改密码"
    :empty-func="() => false"
    @update:show="handleHide"
    @after-enter="handleAfterEnter"
    @after-leave="handleAfterLeave"
  >
    <div class="p-4">
      <NForm ref="formRef" class="mt-4" label-placement="left" label-width="80" :model="formModel" :rules="formRules">
        <NFormItem path="password" :label="t('page.login.form.password.label')">
          <NInput
            v-model:value="formModel.password"
            type="password"
            size="large"
            show-password-on="click"
            maxlength="18"
            show-count
            :placeholder="t('page.login.form.password.placeholder')"
          />
        </NFormItem>

        <NFormItem path="confirmPassword" :label="t('page.login.form.confirmPassword.label')">
          <NInput
            v-model:value="formModel.confirmPassword"
            type="password"
            size="large"
            show-password-on="click"
            maxlength="18"
            show-count
            :placeholder="t('page.login.form.confirmPassword.placeholder')"
          />
        </NFormItem>
      </NForm>
    </div>

    <template #footer>
      <NFlex justify="center">
        <NButton type="primary" :loading="loading" @click="handleConfirm">
          确定
        </NButton>
      </NFlex>
    </template>
  </CardModal>
</template>

<script lang="ts" setup>
  import type { FormInst, FormItemRule } from "naive-ui";
  import type { LocalLoginMode } from "~/pages/login/types";
  import { useI18n } from "@celeris/locale";
  import { getLoginMode, setPasswd } from "~/apis/internal/login";
  import CardModal from "~/component/CardModal/src/CardModal.vue";
  import { TOKEN_KEY } from "~/router/constant";
  import { useUserStore } from "~/store/modules/user";
  import { encryptPassword, validatePassword } from "~/utils/cipher";

  defineOptions({
    name: "UpdatePasswordModal",
    inheritAttrs: false,
  });

  const emit = defineEmits<{
    "update:show": [show: boolean];
    "positiveClick": [success: boolean];
  }>();

  const { t } = useI18n();
  const userStore = useUserStore();
  const message = useMessage();

  const loginUsername = computed(() => userStore.getUsername);
  const token = computed(() => userStore.getToken);

  const INIT_FORM_MODEL = {
    password: "",
    confirmPassword: "",
  };

  const formModel = ref({ ...INIT_FORM_MODEL });
  const formRules = ref({
    password: [
      { required: true, message: t("page.login.form.password.error"), trigger: ["input"] },
      {
        validator: (rule: FormItemRule, value: string): boolean => validatePassword(value),
        message: t("page.login.form.password.validator"),
        trigger: ["input"],
      },
    ],
    confirmPassword: [
      { required: true, message: t("page.login.form.confirmPassword.error"), trigger: ["input"] },
      {
        validator: (rule: FormItemRule, value: string): boolean => value === formModel.value.password,
        message: t("page.login.form.confirmPassword.validator"),
        trigger: ["input"],
      },
    ],
  });

  const formRef = useTemplateRef<HTMLElement & FormInst>("formRef");
  const loading = ref(false);

  //  关闭表单
  function handleHide() {
    emit("update:show", false);
  }

  //  表单提交
  function handleAfterEnter() {
    clearFormModel();
  }

  //  表单关闭
  function handleAfterLeave() {
    clearFormModel();
  }

  //  清空表单
  function clearFormModel() {
    Object.keys(INIT_FORM_MODEL).forEach((key) => {
      formModel.value[key] = INIT_FORM_MODEL[key];
    });
  }

  //  提交表单
  async function handleConfirm() {
    try {
      await formRef.value?.validate();

      const res = await getLoginMode();
      const localMode = res.find(item => item.enabled && item.auth_type === "local") as LocalLoginMode;
      const key = localMode.aes_secret;
      const iv = localMode.aes_secret.slice(0, 16);

      const data = {
        username: loginUsername.value,
        password: encryptPassword(key, iv, formModel.value.password),
        [TOKEN_KEY]: token.value,
      };

      loading.value = true;

      await setPasswd(data);
      message.success("修改成功，请重新登录");
      emit("positiveClick", true);
      await userStore.logout();
      handleHide();
    } catch (e) {
      emit("positiveClick", false);
      if (e instanceof Error) {
        message.error(e.message);
      }
    } finally {
      loading.value = false;
    }
  }
</script>

<style scoped>
:deep(.ca-input__eye) {
  @apply ml-3;
}
</style>
