/**
 * 角色管理模块API
 * Role management module APIs
 */
import { request } from "@celeris/request";
import { replaceUrlPathParams } from "@celeris/utils";
import type { MessageMode } from "@celeris/request";
import type { RoleResponseData, RoleData, MenuResponseData } from '-/role';

/**
 * 角色管理API路径枚举
 * Role management API path enumeration
 */
enum API {
  /**
   * 获取全部角色接口路径
   * Get all roles API path
   */
  ALLROLE_URL = '/admin/acl/role/',
  /**
   * 新增角色接口路径
   * Add role API path
   */
  ADDROLE_URL = '/admin/acl/role/save',
  /**
   * 更新角色接口路径
   * Update role API path
   */
  UPDATEROLE_URL = '/admin/acl/role/update',
  /**
   * 获取全部菜单与按钮权限数据接口路径
   * Get all menu and button permissions API path
   */
  ALLPERMISSION_URL = '/admin/acl/permission/toAssign/',
  /**
   * 分配权限接口路径
   * Assign permissions API path
   */
  SETPERMISSION_URL = '/admin/acl/permission/doAssign',
  /**
   * 删除角色接口路径
   * Remove role API path
   */
  REMOVEROLE_URL = '/admin/acl/role/remove/',
}
/**
 * 获取全部角色列表
 * Get all role list
 *
 * @param pageIndex - 页码
 * @param pageSize - 每页数量
 * @param roleName - 角色名称
 * @param errorMessageMode - 错误消息模式
 */
export const reqAllRoleList = (pageIndex: number, pageSize: number, roleName: string, errorMessageMode?: MessageMode) =>
  request.get<RoleResponseData>({
    url: replaceUrlPathParams(API.ALLROLE_URL, { pageIndex, pageSize }),
    params: { roleName },
  }, { errorMessageMode })
/**
 * 添加或更新角色
 * Add or update role
 *
 * @param data - 角色数据
 * @param errorMessageMode - 错误消息模式
 */
export const reqAddOrUpdateRole = (data: RoleData, errorMessageMode?: MessageMode) => {
  if (data.id) {
    return request.put<void>({
      url: API.UPDATEROLE_URL,
      data,
    }, { errorMessageMode })
  } else {
    return request.post<void>({
      url: API.ADDROLE_URL,
      data,
    }, { errorMessageMode })
  }
}

/**
 * 获取角色对应的全部菜单与按钮权限
 * Get all menus and button permissions for role
 *
 * @param roleId - 角色ID
 * @param errorMessageMode - 错误消息模式
 */
export const reqAllMenuList = (roleId: number, errorMessageMode?: MessageMode) =>
  request.get<MenuResponseData>({
    url: replaceUrlPathParams(API.ALLPERMISSION_URL, { roleId }),
  }, { errorMessageMode })
/**
 * 给角色分配权限
 * Assign permissions to role
 *
 * @param roleId - 角色ID
 * @param permissionIds - 权限ID数组
 * @param errorMessageMode - 错误消息模式
 */
export const reqSetPermission = (roleId: number, permissionIds: number[], errorMessageMode?: MessageMode) =>
  request.post<void>({
    url: API.SETPERMISSION_URL,
    params: { roleId, permissionId: permissionIds },
  }, { errorMessageMode })
/**
 * 删除角色
 * Remove role
 *
 * @param roleId - 角色ID
 * @param errorMessageMode - 错误消息模式
 */
export const reqRemoveRole = (roleId: number, errorMessageMode?: MessageMode) =>
  request.delete<void>({
    url: replaceUrlPathParams(API.REMOVEROLE_URL, { roleId }),
  }, { errorMessageMode })
