import type { Directive, DirectiveBinding } from "vue";
import { copyToClipboard, logger } from "@celeris/utils";

interface CopyDirectiveElement extends HTMLElement {
  copyData: string;
  __handleClick__: any;
}

/**
 * Define a custom directive to copy text to clipboard when clicking an element
 * 定义一个自定义指令，用于在点击一个元素时将文本复制到剪贴板
 */
const copy: Directive = {
  /*
   * 挂载元素时，添加点击事件监听器
   * Add a click event listener when the element is mounted
   */
  mounted(el: CopyDirectiveElement, binding: DirectiveBinding) {
    el.copyData = binding.value as string;
    const handleClick = () => {
      if (!el.copyData) {
        logger.warn("There is no content to copy.");
        return;
      }
      copyToClipboard(el.copyData);
    };
    el.addEventListener("click", handleClick);

    el.__handleClick__ = handleClick;
  },
  /*
   * 更新元素时，更新复制的数据
   * Update the copy data when the element is updated
   */
  updated(el: CopyDirectiveElement, binding: DirectiveBinding) {
    el.copyData = binding.value as string;
  },
  /*
   * 销毁元素时，移除点击事件监听器
   * Remove the click event listener when the element is destroyed
   */
  beforeUnmount(el: CopyDirectiveElement) {
    el.removeEventListener("click", el.__handleClick__);
  },
};

export default copy;
