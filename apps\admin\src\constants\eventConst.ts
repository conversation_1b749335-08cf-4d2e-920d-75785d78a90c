export const EventNameConst = {
  // 'API:INVALID': 'API:INVALID', // 400
  // 'API:UN_AUTH': 'API:UN_AUTH', // 401
  // 'API:ERROR': 'API:ERROR', // 500
  // 'TokenExpired': 'TokenExpired', // token 过期
  "CHAT:CHAT_CONFIG_CHANGED": "CHAT:CHAT_CONFIG_CHANGED", // 聊天参数变化
  "CHAT:CHAT_TARGET_CHANGED": "CHAT:CHAT_TARGET_CHANGED", // 聊天目标变化
  "CHAT:CLOSE": "CHAT:CLOSE", // 聊天关闭，CHAT 开头的都是 iframe 中发出来的事件
  "CHAT:FULLSCREEN": "CHAT:FULLSCREEN", // 聊天全屏
} as const;

export type EventNameConstType = typeof EventNameConst[keyof typeof EventNameConst];
