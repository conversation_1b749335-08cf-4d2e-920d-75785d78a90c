<template>
  <PageWrapper :use-scrollbar="false">
    <div class="home bg-white size-full rounded-2xl">
      <p class="text-[32px] font-medium mb-6">
        {{ getUserInfo?.name }}
      </p>
      <p class="text-[20px] font-light leading-[36px] text-center w-[380px]">
        欢迎来到息壤翼效平台，开启智能化研发效能提升之旅，今天想要了解哪些内容呢？
      </p>
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
  import PageWrapper from "~/component/PageWrapper/src/PageWrapper.vue";
  import { useUserStore } from "~/store/modules/user";

  defineOptions({
    name: "Home",
  });

  const userStore = useUserStore();
  const { getUserInfo } = storeToRefs(userStore);
</script>

<style scoped>
.home {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
