import type { VueQueryPluginOptions, PersistQueryClientOptions } from "@tanstack/vue-query";
import type { App } from "vue";
import { persistQueryClient } from "@tanstack/query-persist-client-core";
import { createSyncStoragePersister } from "@tanstack/query-sync-storage-persister";
import { VueQueryPlugin } from "@tanstack/vue-query";

const vueQueryOptions: VueQueryPluginOptions = {
  queryClientConfig: {
    defaultOptions: {
      queries: {
        staleTime: 1000 * 60 * 60 * 24,
      },
    },
  },
  /*
   * 持久化查询客户端
   * Persist query client
   * @param queryClient 查询客户端
   * @returns 持久化查询客户端
   */
  clientPersister: (queryClient: PersistQueryClientOptions) => {
    return persistQueryClient({
      queryClient,
      persister: createSyncStoragePersister({ storage: localStorage }),
    });
  },
};

/*
 * 配置 vue-query
 * Configure vue-query
 */
export function setupVueQuery(app: App<Element>) {
  app.use(VueQueryPlugin, vueQueryOptions);
}
