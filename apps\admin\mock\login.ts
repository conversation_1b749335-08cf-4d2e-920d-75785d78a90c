// test.ts

import type { MockMethod } from "vite-plugin-mock";

export default [
  {
    url: "/api/v2/user/query",
    method: "get",
    response: () => {
      return {
        code: 0,
        data: {
          username: "mock-user",
          roles: ["admin"],
        },
        message: "mock-数据获取成功",
      };
    },
  },
  // {
  //   url: "/api/v1/mode",
  //   method: "get",
  //   timeout: 2000,
  //   response: {
  //     code: 0,
  //     data: {
  //       aes_secret: "hVOx^YqOZVWe$97xhdCiuSt3vR1L*eW8",
  //       others: ["oidc"],
  //       enable_emergency: false,
  //       enable_phone: true,
  //       enable_find_password: true,
  //     },
  //     message: "mock-数据获取成功",
  //   },
  // },
  // {
  //   url: "/api/v1/user",
  //   method: "get",
  //   response: () => {
  //     return {
  //       code: 0,
  //       data: {
  //         expiresAt: 0,
  //         token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJiYXNlX2NsYWltcyI6eyJ1c2VybmFtZSI6Inplbmd5aWppYSIsIm5pY2tuYW1lIjoi5pu-6Im65L2zIiwiZW1haWwiOiJmZnplbmd5akBjaGluYXRlbGVjb20uY24iLCJ0ZWxlcGhvbmUiOiIxNTM1OTg3OTY0MiIsIm91IjoiIiwic3RhdGUiOjB9LCJidWZmZXJfdGltZSI6ODY0MDAsImV4cCI6MTc1MDM4MzI0OSwiaXNzIjoib25lIiwibmJmIjoxNzUwMjA5NDQ5fQ.jzLG664w52yCBbWIX1hcR5iRBW5Xd0sVJCr0xHak6aA",
  //         user: {
  //           email: "",
  //           nick_name: "测试admin",
  //           phone: "",
  //           state: 0,
  //           telephone: "",
  //           user_name: "testAdmin",
  //           uuid: "00000000-0000-0000-0000-000000000000",
  //         },
  //       },
  //       message: "mock-数据获取成功",
  //     };
  //   },
  // },
  {
    url: "/api/v2/permissions",
    method: "get",
    response: () => {
      return {
        code: 0,
        data: {
          permissions: [],
          page: {
            page_index: 1,
            page_size: 200,
          },
        },
        message: "mock-数据获取成功",
      };
    },
  },
  {
    url: "/api/text",
    method: "post",
    rawResponse: async (req, res) => {
      let reqbody = "";
      await new Promise((resolve) => {
        req.on("data", (chunk) => {
          reqbody += chunk;
        });
        req.on("end", () => resolve(undefined));
      });
      res.setHeader("Content-Type", "text/plain");
      res.statusCode = 200;
      res.end(`hello, ${reqbody}`);
    },
  },
  // {
  //   url: "/api/v1/login",
  //   method: "post",
  //   response: ({ query }) => {
  //     return {
  //       code: 0,
  //       data: {
  //         done_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJiYXNlX2NsYWltcyI6eyJ1c2VybmFtZSI6Inplbmd5aWppYSIsIm5pY2tuYW1lIjoi5pu-6Im65L2zIiwiZW1haWwiOiJmZnplbmd5akBjaGluYXRlbGVjb20uY24iLCJ0ZWxlcGhvbmUiOiIxNTM1OTg3OTY0MiIsIm91IjoiIiwic3RhdGUiOjB9LCJidWZmZXJfdGltZSI6MzYwMCwiZXhwIjoxNzUwMjgzNTk1LCJpc3MiOiJvbmUiLCJuYmYiOjE3NTAyMjEzOTV9.7rS9UiGyJrzGLwT5zvxgeLrlG-fEOLHLWr1sWN0veDw",
  //         user: {
  //           email: "",
  //           nickname: "测试admin",
  //           state: 0,
  //           telephone: "",
  //           username: "testAdmin",
  //           id: "00000000-0000-0000-0000-000000000000",
  //         },
  //       },
  //       message: "mock-数据获取成功",
  //     };
  //   },
  // },
  {
    url: "/api/v2/projects/done/permissions",
    method: "get",
    response: ({ query }) => {
      // 检查查询参数是否包含 resource=menu,btn
      if (query.resource === "menu,btn") {
        return {
          code: 0,
          data: {
            btn: [
              "btn.Role.create",
              "btn.Role.delete",
              "btn.Role.update",
              "btn.Project.create",
              "btn.Env.create",
              "btn.Pipeline.create",
            ],
            menu: [
              "menu-Admin",
              "menu-Applications",
              "menu-Role",
              "menu-Permission",
              "menu-User",
              "menu-ApplicationAuthz",
              "menu-ApplicationAuthzDetails",
              "menu-ApplicationAuthn",
              "menu-Audits",
              "menu-AuditsPage",
              "menu-ProjectManagerPage",
              "menu-ProjectManager",
              "menu-Efficiency",
              "menu-EfficiencyPage",
              "menu-Account",
              "menu-Token",
              "menu-Webhook",
              "menu-Acl",
              "menu-BuildManager",
              "menu-BuildManagerPage",
              "menu-CloudPage",
              "menu-CredentialPage",
              "menu-UserBehavior",
            ],
          },
          message: "mock-数据获取成功",
        };
      }

      // 其他条件去调实际接口
      return {
        code: 0,
        data: [
          {
            id: 3662,
            role_id: "241dc255-2f16-4e15-9c0d-86013d1f173e",
            domain_id: "done",
            resource: "btn.Role.create",
            action: "read",
          },
          {
            id: 7136,
            role_id: "2fea4da5-8a9b-488a-bbb9-fc06287d5868",
            domain_id: "done",
            resource: "menu-Efficiency",
            action: "read",
          },
          {
            id: 7273,
            role_id: "2fea4da5-8a9b-488a-bbb9-fc06287d5868",
            domain_id: "done",
            resource: "menu-EfficiencyPage",
            action: "read",
          },
          {
            id: 113,
            role_id: "427b8f58-d966-4608-b56b-9ffa739a741e",
            domain_id: "done",
            resource: "btn.Role.create",
            action: "read",
          },
          {
            id: 2881,
            role_id: "427b8f58-d966-4608-b56b-9ffa739a741e",
            domain_id: "done",
            resource: "menu-ApplicationAuthz",
            action: "read",
          },
          {
            id: 2882,
            role_id: "427b8f58-d966-4608-b56b-9ffa739a741e",
            domain_id: "done",
            resource: "menu-ApplicationAuthzDetails",
            action: "read",
          },
          {
            id: 2890,
            role_id: "427b8f58-d966-4608-b56b-9ffa739a741e",
            domain_id: "done",
            resource: "menu-Applications",
            action: "read",
          },
          {
            id: 7022,
            role_id: "427b8f58-d966-4608-b56b-9ffa739a741e",
            domain_id: "done",
            resource: "天翼云-CDN产品线-DevOps小组",
            action: "read",
          },
          {
            id: 6777,
            role_id: "443b96ce-2d64-432f-a50e-1e956cd97072",
            domain_id: "done",
            resource: "/api/v2/projects/done/users",
            action: ".*",
          },
          {
            id: 568,
            role_id: "4867a4dc-bbf6-4136-ad72-ed55107f4499",
            domain_id: "done",
            resource: "/api/v1/projects",
            action: "POST",
          },
          {
            id: 567,
            role_id: "4867a4dc-bbf6-4136-ad72-ed55107f4499",
            domain_id: "done",
            resource: "btn.Project.create",
            action: "read",
          },
          {
            id: 14,
            role_id: "53a1637d-5c8e-45c3-a7e2-72747939f1a9",
            domain_id: "done",
            resource: "/api/.*",
            action: ".*",
          },
          {
            id: 2706,
            role_id: "53a1637d-5c8e-45c3-a7e2-72747939f1a9",
            domain_id: "done",
            resource: "btn.Env.create",
            action: "read",
          },
          {
            id: 2878,
            role_id: "53a1637d-5c8e-45c3-a7e2-72747939f1a9",
            domain_id: "done",
            resource: "btn.Pipeline.create",
            action: "read",
          },
          {
            id: 566,
            role_id: "53a1637d-5c8e-45c3-a7e2-72747939f1a9",
            domain_id: "done",
            resource: "btn.Project.create",
            action: "read",
          },
          {
            id: 109,
            role_id: "53a1637d-5c8e-45c3-a7e2-72747939f1a9",
            domain_id: "done",
            resource: "btn.Role.create",
            action: "read",
          },
          {
            id: 110,
            role_id: "53a1637d-5c8e-45c3-a7e2-72747939f1a9",
            domain_id: "done",
            resource: "btn.Role.delete",
            action: "read",
          },
          {
            id: 111,
            role_id: "53a1637d-5c8e-45c3-a7e2-72747939f1a9",
            domain_id: "done",
            resource: "btn.Role.update",
            action: "read",
          },
          {
            id: 12655,
            role_id: "53a1637d-5c8e-45c3-a7e2-72747939f1a9",
            domain_id: "done",
            resource: "menu-Account",
            action: "read",
          },
          {
            id: 12676,
            role_id: "53a1637d-5c8e-45c3-a7e2-72747939f1a9",
            domain_id: "done",
            resource: "menu-Acl",
            action: "read",
          },
        ],
        page: {
          total: 58,
          page_index: 1,
          page_size: 20,
        },
        message: "mock-数据获取成功",
      };
    },
  },
  {
    url: "/api/v2/permissions/config",
    method: "get",
    response: () => {
      return {
        code: 0,
        data: {
          permissions: ["admin", "user"],
          config: {},
        },
        message: "mock-数据获取成功",
      };
    },
  },
  {
    url: "/api/v1/code",
    method: "post",
    response: () => {
      return {
        code: 0,
        message: "mock-数据获取成功",
      };
    },
  },
  {
    url: "/api/v2/user/passwd",
    method: "post",
    response: () => {
      return {
        code: 0,
        data: {
          user: {
            uuid: "00000000-0000-0000-0000-000000000000",
            user_name: "testAdmin",
            nick_name: "测试admin",
            phone: "",
            email: "",
            telephone: "",
            state: 4,
          },
          token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJiYXNlX2NsYWltcyI6eyJ1c2VybmFtZSI6Inplbmd5aWppYSIsIm5pY2tuYW1lIjoiIiwiZW1haWwiOiIiLCJ0ZWxlcGhvbmUiOiIiLCJvdSI6IiIsInN0YXRlIjo0fSwiYnVmZmVyX3RpbWUiOjg2NDAwLCJleHAiOjE3NTA0OTE5NDgsImlzcyI6Im9uZSIsIm5iZiI6MTc1MDMxODE0OH0.uy8GIBSw0WTLpZWqcB3TNAsvG3uBJPulo0zg5XJRlJY",
          expiresAt: 0,
        },
        message: "您已进入密码重置阶段，请输入新密码",
      };
    },
  },
] as MockMethod[];
