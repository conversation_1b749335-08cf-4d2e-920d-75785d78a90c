/*
 * @Author: zy
 * @Date: 2024-12-31 14:49:11
 * @LastEditors: zy
 * @LastEditTime: 2025-01-15 18:19:05
 * @Description: 
 */
import type { GlobConfig, GlobEnvConfig } from "@celeris/types";
import { getAppGlobalConfig } from "@celeris/utils";

/**
 * 获取全局配置
 * Get global configuration
 * @returns Readonly<GlobConfig>
 */
export function useGlobSetting(): Readonly<GlobConfig> {
  const glob = getAppGlobalConfig(<GlobEnvConfig>import.meta.env);
  return glob as Readonly<GlobConfig>;
}
