import type { PluginOption } from "vite";
import path from "node:path";
import process from "node:process";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
/*
 * Create SVG Icon plugin configuration
 * 创建SVG Icon插件配置
 * @param isProductionBuild 是否是生产环境
 * @returns Vite plugin configuration array Vite插件配置数组
 */
export function createSvgIconsPluginConfig(isProductionBuild: boolean): PluginOption {
  return createSvgIconsPlugin({
    iconDirs: [path.resolve(process.cwd(), "../../packages/web/assets/src/images/svg")],
    symbolId: "icon-[dir]-[name]",
    svgoOptions: isProductionBuild,
  });
}
