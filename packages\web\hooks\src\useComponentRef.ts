import type { ComponentPublicInstance } from "vue";
import { isComponentInstance } from "@celeris/utils";
import { onMounted, ref, watch } from "vue";

/**
 * 获取组件的 ref 引用
 * Get the ref reference of the component
 * @param name - ref 名称
 *             ref name
 * @returns - 组件的 ref 引用
 *           The ref reference of the component
 */
export function useComponentRef(name: string) {
  const componentRef = ref<HTMLElement | ComponentPublicInstance>();

  const getElement = () => {
    if (isComponentInstance(componentRef.value)) {
      return componentRef.value.$refs[name] as HTMLElement;
    }
    return componentRef.value;
  };

  const elementRef = ref();

  onMounted(() => {
    elementRef.value = getElement();
  });

  watch([componentRef], () => {
    elementRef.value = getElement();
  });

  return {
    componentRef,
    elementRef,
  };
}
