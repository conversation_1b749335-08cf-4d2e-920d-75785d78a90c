import type { EncryptionParams } from "@celeris/utils";
import { PASSWORD_REGEX } from "@celeris/constants";
import { EncryptionFactory } from "@celeris/utils";

/**
 * 使用AES加密密码
 * @param password 待加密的密码字符串
 * @param key 密钥字符串，用于AES加密的密钥
 * @param iv 初始化向量
 * @returns 返回加密后的密文
 */
export function encryptPassword(key: string, iv: string, password: string) {
  const params: EncryptionParams = {
    key,
    iv,
  };
  const aesEncryption = EncryptionFactory.createAesEncryption(params);
  return aesEncryption.encrypt(password);
}

/**
 * 使用AES解密密码
 * @param password 待解密的密文字符串
 * @param key 密钥字符串，用于AES解密的密钥
 * @param iv 初始化向量
 * @returns 返回解密后的明文
 */
export function decryptPassword(key: string, iv: string, password: string) {
  const params: EncryptionParams = {
    key,
    iv,
  };
  const aesEncryption = EncryptionFactory.createAesEncryption(params);
  return aesEncryption.decrypt(password);
}

/**
 * 验证密码是否符合要求
 * @param password 待验证的密码字符串
 * @returns 如果密码符合要求则返回true，否则返回false
 */
export function validatePassword(password: string) {
  return PASSWORD_REGEX.test(password);
}
