import type { TreeI<PERSON>, TreeWrapper } from "-/common";
import type { ResponseData, PageResponseData } from "-/http";
import type { UserListItem, UserParam } from "-/user";
import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";
import { replaceUrlPathParams } from "@celeris/utils";

enum API {
  UserTree = "/v2/team/userTeamTree",
  User = "/v1/users",
  DeleteUser = "/v1/users/{username}",
  GetUserListApi = "/v2/projects/one/authzs/users",
  AuthnUserProject = "/v2/authn/users/{uid}/projects",
  AuthnUserHost = "/v2/authn/users/{uid}/hosts",
}

/**
 * 获取当前用户所属团队树
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<TreeItem[]> 用户树列表
 */
export function userTreeApi(params?: { team_ids: string }, errorMessageMode: MessageMode = "message"): Promise<TreeItem[]> {
  return request.get<TreeWrapper>({ url: API.UserTree, params }, { errorMessageMode })
    .then(res => res.tree);
}

/**
 * 获取用户列表（分页）
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<PageResponseData<UserListItem[]>> 用户列表响应
 */
export function userListApi(params: { q: string }, errorMessageMode: MessageMode = "message") {
  return request.get<PageResponseData<UserListItem[]>>({ url: API.User, params }, { errorMessageMode, shouldTransformResponse: false });
}

/**
 * 创建用户
 * @param data - 用户数据
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<any> 创建用户响应
 */
export function createUserApi(data: UserParam, errorMessageMode: MessageMode = "message") {
  return request.post({ url: API.User, data }, { errorMessageMode });
}

/**
 * 更新用户
 * @param data - 用户数据
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<any> 更新用户响应
 */
export function updateUserApi(data: UserParam, errorMessageMode: MessageMode = "message") {
  return request.put({ url: API.User, data }, { errorMessageMode });
}

/**
 * 删除用户
 * @param id - 用户账户
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<any> 删除用户响应
 */
export function deleteUserApi(username: string, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.DeleteUser, { username });
  return request.delete({ url }, { errorMessageMode });
}

/**
 * 获取用户列表(不分页)
 * @param params
 * @returns
 */
export function getUserList(params: AnyObject = {}, errorMessageMode: MessageMode = "message") {
  return request.get({ url: API.User, params }, { errorMessageMode, shouldTransformResponse: false });
}

/**
 * 获取用户覆盖的项目列表
 * @param uid - 用户ID
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<any> 覆盖项目列表响应
 */
export function getCoverageProjectApi(uid: number, params: { q?: string }, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.AuthnUserProject, { uid });
  return request.get({ url, params }, { errorMessageMode, shouldTransformResponse: false });
}

/**
 * 获取用户覆盖的主机列表
 * @param uid - 用户ID
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式，默认 'message'
 * @returns Promise<any> 覆盖主机列表响应
 */
export function getCoverageHostApi(uid: number, params: { q?: string }, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.AuthnUserHost, { uid });
  return request.get({ url, params }, { errorMessageMode, shouldTransformResponse: false });
}

export { API };
