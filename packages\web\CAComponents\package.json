{"name": "@celeris/ca-components", "type": "module", "version": "0.0.3", "description": "components for Celeris", "author": "<PERSON> (https://github.com/kirklin)", "license": "MIT", "homepage": "https://github.com/kirklin/celeris-web", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./index.ts", "module": "./index.ts", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --stub", "clean": "pnpm rimraf node_modules && pnpm rimraf dist", "prepublishOnly": "npm run build", "postinstall": "npm run build"}, "peerDependencies": {"naive-ui": ">=2.38.2", "vue": ">=3.3.4"}, "dependencies": {}, "devDependencies": {"@celeris/constants": "workspace:*", "@celeris/utils": "workspace:*", "naive-ui": "latest", "vue": "^3.5.13"}}