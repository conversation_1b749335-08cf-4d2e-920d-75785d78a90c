import type { PluginOption } from "vite";
/**
 * Visualize and analyze your Rollup bundle to see which modules are taking up space.
 * https://github.com/btd/rollup-plugin-visualizer
 */
import visualizer from "rollup-plugin-visualizer";

/**
 * Create Visualizer plugin configuration
 * 创建打包分析插件配置
 * @returns Vite plugin configuration array Vite插件配置数组
 */
export function createVisualizerPluginConfig(): PluginOption {
  return visualizer({
    filename: "./node_modules/.cache/visualizer/stats.html",
    open: true,
    gzipSize: true,
    brotliSize: true,
  }) as PluginOption;
}
