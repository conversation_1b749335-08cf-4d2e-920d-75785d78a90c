{"name": "@celeris/admin", "type": "module", "version": "0.0.3", "packageManager": "pnpm@9.14.2", "description": "息壤翼效平台", "author": "<PERSON> (https://github.com/kirklin)", "license": "MIT", "homepage": "https://github.com/kirklin/celeris-web", "keywords": ["celeris-web-admin", "celeris-web", "celeris", "vite", "unocss", "fast", "boot"], "main": "src/main.ts", "scripts": {"dev": "vite --debug", "build:development": "pnpm cross-env NODE_ENV=development vite build --mode development", "build:test": "pnpm cross-env NODE_ENV=test vite build --mode test", "build:production": "pnpm cross-env NODE_ENV=production vite build --mode production", "clean": "pnpm rimraf node_modules && pnpm rimraf dist", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build && vite preview", "preview:dist": "vite preview", "test": "vitest", "test:run": "vitest run"}, "dependencies": {"@celeris/constants": "workspace:*", "@celeris/directives": "workspace:*", "@celeris/locale": "workspace:*", "@celeris/request": "workspace:*", "@celeris/styles": "workspace:*", "@celeris/utils": "workspace:*", "@kangc/v-md-editor": "^2.3.18", "@tanstack/query-persist-client-core": "^5.60.6", "@tanstack/query-sync-storage-persister": "^5.60.6", "@tanstack/vue-query": "^5.61.3", "@tanstack/vue-table": "^8.20.5", "crypto-js": "^4.2.0", "destr": "^2.0.3", "headless-highlighter": "^0.0.0", "md-editor-v3": "^5.7.1", "nprogress": "^0.2.0", "pinia": "^2.2.6", "pinia-plugin-persistedstate": "^4.1.3", "pkg-types": "^1.2.1", "vue": "^3.5.13", "vue-dompurify-html": "^5.3.0", "vue-i18n": "^10.0.4", "vue-router": "^4.4.5"}, "devDependencies": {"@celeris/assets": "workspace:*", "@celeris/ca-components": "workspace:*", "@celeris/components": "workspace:*", "@celeris/hooks": "workspace:*", "@celeris/types": "workspace:*", "@celeris/vite": "workspace:*", "@vitejs/plugin-vue": "^5.2.0", "@vue/compiler-dom": "^3.5.13", "@vue/test-utils": "^2.4.6", "jsdom": "^25.0.1", "pnpm": "^9.14.2", "typescript": "^5.7.2", "unocss": "^0.64.1", "vite": "^5.4.11", "vitest": "^2.1.5", "vue-tsc": "^2.1.10"}, "volta": {"node": "18.20.4"}}