<template>
  <PageWrapper>
    <template #default="slotProps: { rect?: { height: number } }">
      <NFlex
        class="overflow-hidden flex-1"
        size="large"
        vertical
        :style="{ height: slotProps?.rect?.height ? `${slotProps.rect.height - 32}px` : 'auto' }"
      >
        <QueryHeader
          v-model:value="queryCriteria" :items="queryItems"
          :show-refresh="false" @search="handleSearch"
        >
          <template #primary>
            <NButton type="primary" @click="handleAdd">
              新增用户
            </NButton>
          </template>
          <template #search>
            <!-- <NFormItem path="username">
              <NInput
                v-model:value="formModel.username"
                placeholder="请输入账号"
                clearable
                @clear="handleClear"
                @keyup.enter="handleSearch"
              />
            </NFormItem> -->
            <NButton type="info" ghost @click="handleSearch">
              查询
            </NButton>
          </template>
        </QueryHeader>

        <NDataTable
          class="fixed-right-table h-full [&_.ca-data-table-resize-button::after]:(!bg-[var(--primary-color)])"
          scroll-x="min-content"
          remote
          flex-height
          :striped="true"
          :bordered="false"
          :single-line="false"
          :loading="tableLoading"
          :data="tableData"
          :columns="tableColumns"
          :pagination="tablePagination"
          @scroll="handleScroll"
          @update:page="onPageChange"
          @update:page-size="onPageSizeChange"
        />
      </NFlex>

      <!-- 用户信息编辑对话框 -->
      <UserFormModal
        v-model:show="userFormModalShow"
        :data="currUser"
        @positive-click="handlePositiveClick"
      />
      <!-- 关联租户对话框 -->
      <RelTenantModal
        v-model:show="visible"
        :data="currUser"
        :tenant-options="tenantOptions"
        @positive-click="handlePositiveClick"
      />
    </template>
  </PageWrapper>
</template>

<script setup lang="tsx">
  import type { PageResponseData } from "-/http";
  import type { TenantInfo } from "-/tenant";
  import type { UserListExpandedItem, UserListItem } from "-/user";
  import type { DataTableColumns, SelectOption } from "naive-ui";
  import { CAIcon } from "@celeris/components";
  import { formatToDate } from "@celeris/utils";
  import { cloneDeep } from "lodash-es";
  import { NButton, NDropdown, NFlex, NInput } from "naive-ui";
  import { tenantListApi } from "~/apis/internal/tenant";
  import { deleteUserApi, updateUserApi, userListApi } from "~/apis/internal/user";
  import PageWrapper from "~/component/PageWrapper/src/PageWrapper.vue";
  import { QueryHeader } from "~/component/QueryHeader";
  import RelTenantModal from "./components/RelTenantModal.vue";
  import UserFormModal from "./components/UserFormModal.vue";

  const selectedKeys = ref<string[]>([]);

  interface QueryCriteria {
    team_ids: string[];
    username: string | null;
    q: string | null;
  }

  const INIT_QUERY_CRITERIA: QueryCriteria = {
    team_ids: [],
    username: null,
    q: null,
  };
  const queryCriteria = ref<QueryCriteria>({
    ...INIT_QUERY_CRITERIA,
    team_ids: [selectedKeys.value[0]],
  });

  const {
    tableLoading,
    tableData,
    tablePagination,
    loadData,
    reloadData,
    refreshData,
    handlePageChange,
    handlePageSizeChange,
    handleScroll,
  } = useTable<QueryCriteria, UserListItem, UserListExpandedItem>();

  // ========== 表格列定义 ==========
  const tableColumns: DataTableColumns<UserListExpandedItem> = [
    {
      title: "账号",
      key: "username",
      width: 130,
      fixed: "left",
    },
    {
      title: "姓名",
      key: "nickname",
      width: 100,
      fixed: "left",
    },
    {
      title: "邮箱",
      key: "email",
      width: 250,
      render(row) {
        return h("span", formatter(row, "email"));
      },
    },
    {
      title: "手机",
      key: "telephone",
      width: 150,
      render(row) {
        return h("span", formatter(row, "telephone"));
      },
    },
    {
      title: "账号状态",
      key: "state",
      width: 100,
      render(row) {
        const isSuccess = row.state === 0;
        const isError = row.state === 1;
        return h("span", isSuccess ? "✅ 启用" : isError ? "❌ 禁用" : "");
      },
    },
    {
      title: "账号有效期",
      key: "account_expired",
      width: 120,
      render(row) {
        return formatToDate(row.account_expired);
      },
    },
    {
      title: "密码有效期",
      key: "password_expired",
      width: 120,
      render(row) {
        return formatToDate(row.password_expired);
      },
    },
    {
      title: "关联租户",
      key: "group_lines",
      minWidth: 240,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return h("span", formatter(row, "group_lines"));
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 150,
      fixed: "right",
      render(row) {
        return (
          <NFlex>
            <NButton
              text
              type="info"
              onClick={() => handleUpdate?.(cloneDeep(row))}
            >
              编辑
            </NButton>
            <NButton
              text
              type="info"
              onClick={() => relTenant?.(cloneDeep(row))}
            >
              关联
            </NButton>
            <NDropdown
              options={dropdownOptions(row)}
              onSelect={key => handleSelect(key, row)}
              trigger="hover"
            >
              <NButton text type="info">
                更多
                <n-icon>
                  <CAIcon icon="tabler:chevron-down" size={15} />
                </n-icon>
              </NButton>
            </NDropdown>
          </NFlex>
        );
      },
    },
  ];

  // 下拉按钮
  function dropdownOptions(row): Array<{ label: string; key: string }> {
    const changeAccountItem: Array<{ label: string; key: string }> = [];
    if ([0, 1].includes(row.state)) {
      changeAccountItem.push({
        label: row.state === 1 ? "启用" : "禁用",
        key: "changeAccountState",
      });
    }
    return [
      {
        label: "删除",
        key: "deleteUser",
      },
      ...changeAccountItem,
    ];
  }

  // 角色选项
  const tenantOptions = ref<SelectOption[]>([]);

  const dialog = useDialog();
  const message = useMessage();

  onMounted(() => {
    handleUserTreeLoadSuccess();
    getTenants();
  });

  // 监听 queryCriteria.username 变化，自动拼接 q 字段
  watch(
    [() => queryCriteria.value.username],
    () => {
      getParams();
    },
    { immediate: true },
  );

  const queryItems = computed(() => [
    {
      path: "username",
      label: "账号",
      component: h(NInput, {
        placeholder: "请输入账号",
        clearable: true,
        onKeyup: (e) => {
          if (e.key === "Enter") {
            handleSearch();
          }
        },
      }),
    },
  ]);

  /**
   * 重置请求参数，拼接查询字符串 q
   */
  function getParams() {
    let q = ``;
    // 筛选条件
    if (queryCriteria.value.username) {
      q += `username=~${queryCriteria.value.username}`;
    }
    queryCriteria.value.q = q;
  }

  /**
   * 用户树加载成功时的处理逻辑，初始化参数并加载表格数据
   */
  function handleUserTreeLoadSuccess() {
    getParams();
    loadData({
      getQueryCriteria: () => queryCriteria.value,
      dataPath: "data.value",
      totalPath: "data.total",
      tableRequest: (userListApi as unknown) as (
        queryCriteria?: QueryCriteria
      ) => Promise<UserListItem[]>,
      handleTableData: (dataSource: UserListExpandedItem[]) => {
        return dataSource.map((item, index) => {
          return {
            ...item,
            key: item.id ? String(item.id) : index.toString(),
            deleteLoading: false,
          } as UserListExpandedItem;
        });
      },
    });
  }

  /**
   * 处理页码变化
   * 1. 设置当前页码
   * 2. 加载数据
   * @param page - 当前页码
   */
  function onPageChange(page: number) {
    tablePagination.value.page = page;
    getParams();
    handlePageChange(page);
  }

  /**
   * 处理每页条数变化
   * 1. 重置页码为第一页
   * 2. 设置当前每页条数
   * 3. 加载数据
   * @param pageSize - 每页条数
   */
  function onPageSizeChange(pageSize: number) {
    tablePagination.value.page = 1;
    tablePagination.value.pageSize = pageSize;
    getParams();
    handlePageSizeChange(pageSize);
  }

  /**
   * 表格字段格式化
   * @param row - 当前行数据
   * @param property - 字段名
   * @returns string | any
   */
  function formatter(row: any, property: string) {
    const cellValue = row[property];
    const emptyChar = "--";
    if (!row) {
      return emptyChar;
    }
    let result = cellValue;
    switch (property) {
      case "preserved_user":
        result = row.preserved === 1 ? "离职" : "在职";
        break;
      case "group_lines":
        result = cellValue ? cellValue.join("/") : cellValue;
        break;
      case "entry_time":
        result = formatToDate(cellValue);
        break;
    }
    return result || emptyChar;
  }

  /**
   * 查询按钮点击，重置页码并刷新表格
   */
  function handleSearch() {
    tablePagination.value.page = 1;
    getParams();
    reloadData();
  }

  const userFormModalShow = ref(false);
  const currUser = ref<UserListExpandedItem | null>(null);

  /**
   * 新增用户，弹出用户表单
   */
  function handleAdd() {
    currUser.value = null;
    openUserFormModal();
  }

  /**
   * 处理表格操作下拉菜单选择
   * @param key - 菜单项 key
   * @param row - 当前行数据
   */
  function handleSelect(key: string | number, row: UserListExpandedItem) {
    if (String(key) === "deleteUser") {
      handleDelete(row);
    }
    if (String(key) === "changeAccountState") {
      changeAccountState(row);
    }
  }

  /**
   * 编辑用户，弹出用户表单并回填数据
   * @param row - 当前行数据
   */
  function handleUpdate(row: UserListExpandedItem) {
    currUser.value = row;
    openUserFormModal();
  }

  /**
   * 打开用户表单弹窗
   */
  function openUserFormModal() {
    userFormModalShow.value = true;
  }

  const visible = ref(false);
  const currentUid = ref<number>(0);

  /**
   * 关联租户弹窗
   * @param row - 当前行数据
   */
  function relTenant(row: UserListExpandedItem) {
    currentUid.value = row.id;
    visible.value = true;
  }

  /**
   * 切换账号状态（启用/禁用）
   * @param row - 用户行数据
   * @returns Promise<void>
   */
  async function changeAccountState(row: UserListExpandedItem) {
    const isDisable = row.state === 0;
    const actionText = isDisable ? "禁用" : "启用";
    dialog.info({
      title: "警告",
      content: `确认要${actionText}该账号吗？`,
      positiveText: "确定",
      negativeText: "取消",
      async onPositiveClick() {
        try {
          const params = {
            username: row.username,
            nickname: row.nickname,
            email: row.email,
            state: isDisable ? 1 : 2, // 1: 禁用, 2: 启用
          };
          await updateUserApi(params);
          message.success(`${actionText}成功`);
          refreshData();
        } catch (error) {
          message.error(`${actionText}失败，请重试`);
        }
      },
    });
  }

  /**
   * 用户信息编辑弹窗点击确定后回调，刷新表格
   * @returns Promise<void>
   */
  async function handlePositiveClick() {
    // await userStore.fetchUserTree(null, true);
    handleSearch();
  }

  /**
   * 删除用户，弹窗确认后调用接口
   * @param row - 当前行数据
   */
  function handleDelete(row: UserListExpandedItem) {
    if (row.deleteLoading) {
      return;
    }

    dialog.warning({
      title: "警告",
      content: "您确定要删除此用户吗？",
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: () => {
        row.deleteLoading = true;
        deleteUserApi(row.username)
          .then(() => {
            message.success("删除成功");
            handleSearch();
          })
          .finally(() => {
            row.deleteLoading = false;
          });
      },
    });
  }

  /**
   * @description 获取租户列表
   */
  async function getTenants() {
    await tenantListApi({
      page_index: 1,
      page_size: 999,
    }).then((res: PageResponseData<TenantInfo[]>) => {
      tenantOptions.value = res?.data?.value.map(obj => ({ label: obj.name, value: obj.id })) as SelectOption[];
    });
  }
</script>

<style scoped></style>
