<template>
  <NModal v-model:show="showModal" preset="card" style="width: 90%; max-width: 1200px;" title="PRD思维导图">
    <div class="h-[600px] w-full">
      <CACharts v-if="chartOption" :options="chartOption" />
      <div v-else class="flex items-center justify-center h-full text-gray-500">
        正在生成思维导图...
      </div>
    </div>
  </NModal>
</template>

<script setup lang="ts">
  import type { EChartsOption } from "echarts";
  import { CACharts } from "@celeris/components";
  import { NModal } from "naive-ui";
  import { computed, watch } from "vue";

  interface PrdData {
    title?: string;
    content?: string;
  }

  interface Props {
    show: boolean;
    prdData: PrdData;
  }

  const props = defineProps<Props>();
  const emits = defineEmits<{
    (e: "update:show", value: boolean): void;
  }>();

  const showModal = computed({
    get: () => props.show,
    set: value => emits("update:show", value),
  });

  // 解析PRD内容生成思维导图数据
  function parsePrdToMindMap(content: string) {
    if (!content) {
      return null;
    }

    const lines = content.split("\n");
    const nodes: any[] = [];
    const links: any[] = [];

    // 根节点
    const rootTitle = props.prdData.title || "PRD文档";
    nodes.push({
      id: "root",
      name: rootTitle,
      x: 0,
      y: 0,
      symbolSize: 80,
      itemStyle: {
        color: "#4f46e5",
      },
      label: {
        fontSize: 16,
        fontWeight: "bold",
      },
    });

    let currentLevel1Node: any = null;
    let currentLevel2Node: any = null;
    let nodeId = 1;

    for (const line of lines) {
      const trimmedLine = line.trim();

      // 一级标题 (# 标题)
      if (trimmedLine.match(/^# (.+)$/)) {
        const title = trimmedLine.replace(/^# /, "");
        const id = `node_${nodeId++}`;

        currentLevel1Node = {
          id,
          name: title,
          symbolSize: 60,
          itemStyle: {
            color: "#059669",
          },
          label: {
            fontSize: 14,
            fontWeight: "bold",
          },
        };

        nodes.push(currentLevel1Node);
        links.push({
          source: "root",
          target: id,
          lineStyle: {
            width: 3,
          },
        });

        currentLevel2Node = null;
      }
      // 二级标题 (## 标题)
      else if (trimmedLine.match(/^## (.+)$/)) {
        const title = trimmedLine.replace(/^## /, "");
        const id = `node_${nodeId++}`;

        currentLevel2Node = {
          id,
          name: title,
          symbolSize: 45,
          itemStyle: {
            color: "#dc2626",
          },
          label: {
            fontSize: 12,
          },
        };

        nodes.push(currentLevel2Node);

        if (currentLevel1Node) {
          links.push({
            source: currentLevel1Node.id,
            target: id,
            lineStyle: {
              width: 2,
            },
          });
        }
      }
      // 三级标题 (### 标题)
      else if (trimmedLine.match(/^### (.+)$/)) {
        const title = trimmedLine.replace(/^### /, "");
        const id = `node_${nodeId++}`;

        const node = {
          id,
          name: title,
          symbolSize: 35,
          itemStyle: {
            color: "#7c3aed",
          },
          label: {
            fontSize: 11,
          },
        };

        nodes.push(node);

        const parentNode = currentLevel2Node || currentLevel1Node;
        if (parentNode) {
          links.push({
            source: parentNode.id,
            target: id,
            lineStyle: {
              width: 1,
            },
          });
        }
      }
      // 列表项 (- 或 * 或数字开头)
      else if (trimmedLine.match(/^[-*] (.+)$/) || trimmedLine.match(/^\d+\. (.+)$/)) {
        let title = "";
        if (trimmedLine.match(/^[-*] (.+)$/)) {
          title = trimmedLine.replace(/^[-*] /, "");
        } else if (trimmedLine.match(/^\d+\. (.+)$/)) {
          title = trimmedLine.replace(/^\d+\. /, "");
        }

        if (title.length > 50) {
          continue;
        } // 跳过过长的内容

        const id = `node_${nodeId++}`;

        const node = {
          id,
          name: title,
          symbolSize: 25,
          itemStyle: {
            color: "#f59e0b",
          },
          label: {
            fontSize: 10,
          },
        };

        nodes.push(node);

        const parentNode = currentLevel2Node || currentLevel1Node;
        if (parentNode) {
          links.push({
            source: parentNode.id,
            target: id,
            lineStyle: {
              width: 1,
              type: "dashed",
            },
          });
        }
      }
    }

    return { nodes, links };
  }

  // 生成ECharts配置
  const chartOption = computed<EChartsOption | null>(() => {
    if (!props.prdData.content) {
      return null;
    }

    const mindMapData = parsePrdToMindMap(props.prdData.content);
    if (!mindMapData) {
      return null;
    }

    return {
      title: {
        text: `${props.prdData.title || "PRD文档"} - 思维导图`,
        left: "center",
        textStyle: {
          fontSize: 18,
          fontWeight: "bold",
        },
      },
      tooltip: {
        trigger: "item",
        formatter: "{b}",
      },
      series: [
        {
          type: "graph",
          layout: "force",
          data: mindMapData.nodes,
          links: mindMapData.links,
          roam: true,
          focusNodeAdjacency: true,
          force: {
            repulsion: 1000,
            gravity: 0.1,
            edgeLength: 150,
            layoutAnimation: true,
          },
          label: {
            show: true,
            position: "inside",
            formatter(params: any) {
              const name = params.data.name;
              // 限制显示长度
              return name.length > 15 ? `${name.substring(0, 15)}...` : name;
            },
          },
          lineStyle: {
            color: "#999",
            curveness: 0.3,
          },
          emphasis: {
            focus: "adjacency",
            lineStyle: {
              width: 3,
            },
          },
        },
      ],
    };
  });

  // 监听显示状态变化，重新计算布局
  watch(() => props.show, (newShow) => {
    if (newShow) {
      // 模态框打开时，稍微延迟一下让图表重新渲染
      setTimeout(() => {
        // 触发图表重新渲染
      }, 100);
    }
  });
</script>

<style scoped>
/* 确保图表容器正确显示 */
:deep(.echarts) {
  width: 100% !important;
  height: 100% !important;
}
</style>
