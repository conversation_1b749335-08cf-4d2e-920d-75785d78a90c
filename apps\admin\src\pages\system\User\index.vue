<template>
  <PageWrapper :use-default-padding="false">
    <template #default="slotProps: { rect?: { height: number } }">
      <NFlex
        :size="0"
        :style="{
          height: slotProps?.rect?.height ? `${slotProps.rect.height}px` : 'auto',
        }"
      >
        <div class="flex-col-center w-20% h-full">
          <NSpin v-if="allUserTreeLoading" />

          <template v-else>
            <div class="p-4 w-full">
              <NInput v-model:value="pattern" placeholder="搜索" clearable />
            </div>

            <div class="overflow-hidden w-full flex-1">
              <UserTree
                ref="userTreeRef"
                class="px-4 pb-4 [&_.ca-empty]:(h-full flex-center)"
                :pattern="pattern"
                :draggable="true"
                :expanded-keys="expandedKeys"
                :selected-keys="selectedKeys"
                :render-suffix="renderSuffix"
                @load-success="handleUserTreeLoadSuccess"
                @drop="handleDrop"
                @update:expanded-keys="handleUpdateExpandedKeys"
                @update:selected-keys="handleUpdateSelectedKeys"
              />
            </div>
          </template>
        </div>

        <NDivider class="!h-full !mx-0 !mr-4" vertical />

        <NFlex class="overflow-hidden flex-1 py-4 pr-4" vertical size="large">
          <QueryHeader v-model:value="queryCriteria" @search="handleSearch">
            <template #actions>
              <NButton type="primary" @click="handleAdd">
                新增用户
              </NButton>
            </template>
            <template #search>
              <NButton type="info" ghost @click="handleSearch">
                查询
              </NButton>
            </template>
          </QueryHeader>

          <NDataTable
            class="fixed-right-table h-full [&_.ca-data-table-resize-button::after]:(!bg-[var(--primary-color)])"
            scroll-x="min-content"
            remote
            flex-height
            :striped="true"
            :bordered="false"
            :single-line="false"
            :loading="tableLoading"
            :data="tableData"
            :columns="tableColumns"
            :pagination="tablePagination"
            @scroll="handleScroll"
            @update:page="onPageChange"
            @update:page-size="onPageSizeChange"
          />
        </NFlex>
      </NFlex>

      <!-- 用户信息编辑对话框 -->
      <UserFormModal
        v-model:show="userFormModalShow"
        :data="currUser"
        @positive-click="handlePositiveClick"
      />

      <!-- 覆盖项目对话框 -->
      <CoverageProjectModal
        v-model:show="visible"
        :uid="currentUid"
      />

      <!-- 覆盖机器对话框 -->
      <CoverageMachModal
        v-model:show="visibleMach"
        :uid="currentUid"
      />

      <!-- 编辑团队对话框 -->
      <TeamFormModal
        v-model:show="editDialogVisible"
        :data="selectedTeamData"
        :all-user-tree="allUserTree"
        @positive-click="handleEditTeamPositiveClick"
      />

      <!-- 团队操作菜单 -->
      <NDropdown
        placement="bottom-start"
        trigger="manual"
        :x="x"
        :y="y"
        :options="contextMenuOptions"
        :show="contextMenuVisible"
        :on-clickoutside="onClickoutside"
        @select="handleContextMenuSelect"
      />
    </template>
  </PageWrapper>
</template>

<script setup lang="tsx">
  import type { UserListExpandedItem, UserListItem } from "-/user";
  import type { DataTableColumns, TreeDropInfo, TreeOption } from "naive-ui";
  import { CAIcon } from "@celeris/components";
  import { formatToDate } from "@celeris/utils";
  import { cloneDeep } from "lodash-es";
  import { NButton, NDropdown, NFlex, NTag } from "naive-ui";
  import { deleteTeamApi } from "~/apis/internal/team";
  import { deleteUserApi, updateUserApi, userListApi } from "~/apis/internal/user";
  import PageWrapper from "~/component/PageWrapper/src/PageWrapper.vue";
  import UserTree from "~/component/UserTree/src/UserTree.vue";
  import { useUserStore } from "~/store/modules/user";
  import CoverageMachModal from "./components/CoverageMachModal.vue";
  import CoverageProjectModal from "./components/CoverageProjectModal.vue";
  import QueryHeader from "./components/QueryHeader.vue";
  import TeamFormModal from "./components/TeamFormModal.vue";
  import UserFormModal from "./components/UserFormModal.vue";

  const userStore = useUserStore();

  const allUserTree = computed(() => userStore.getAllUserTree);
  const allUserTreeLoading = computed(() => userStore.getAllUserTreeLoading);
  const pattern = ref("");
  const expandedKeys = ref<number[]>([]);
  const selectedKeys = ref<number[]>([]);

  interface QueryCriteria {
    team_ids: number[];
    username: string | null;
    q: string | null;
  }

  const INIT_QUERY_CRITERIA: QueryCriteria = {
    team_ids: [],
    username: null,
    q: null,
  };
  const queryCriteria = ref<QueryCriteria>({
    ...INIT_QUERY_CRITERIA,
    team_ids: [selectedKeys.value[0]],
  });

  const selectedTeam = ref<TreeOption | null>(null); // 当前右键菜单对应的团队
  const editDialogVisible = ref(false);

  const userTreeRef = ref<{ reloadData: () => void } | null>(null);
  const contextMenuVisible = ref(false);

  const {
    tableLoading,
    tableData,
    tablePagination,
    loadData,
    reloadData,
    refreshData,
    handlePageChange,
    handlePageSizeChange,
    handleScroll,
  } = useTable<QueryCriteria, UserListItem, UserListExpandedItem>();

  // ========== 表格列定义 ==========
  const tableColumns: DataTableColumns<UserListExpandedItem> = [
    {
      title: "账号",
      key: "username",
      width: 130,
      fixed: "left",
    },
    {
      title: "姓名",
      key: "nickname",
      width: 100,
      fixed: "left",
    },
    {
      title: "邮箱",
      key: "email",
      width: 250,
      render(row) {
        return h("span", formatter(row, "email"));
      },
    },
    {
      title: "手机",
      key: "telephone",
      width: 150,
      render(row) {
        return h("span", formatter(row, "telephone"));
      },
    },
    {
      title: "账号状态",
      key: "state",
      width: 100,
      render(row) {
        const isSuccess = row.state === 0;
        const isError = row.state === 1;
        return h("span", isSuccess ? "✅ 启用" : isError ? "❌ 禁用" : "");
      },
    },
    // {
    //   title: "人员状态",
    //   key: "preserved_user",
    //   width: 100,
    //   render(row) {
    //     return h(
    //       NTag,
    //       {
    //         type: row.preserved === 1 ? "info" : "success",
    //         bordered: false,
    //       },
    //       {
    //         default: () => (row.preserved === 1 ? "离职" : "在职"),
    //       },
    //     );
    //   },
    // },
    // {
    //   title: "入职时间",
    //   key: "entry_time",
    //   width: 120,
    //   render(row) {
    //     return h("span", formatter(row, "entry_time"));
    //   },
    // },
    {
      title: "账号有效期",
      key: "account_expired",
      width: 120,
      render(row) {
        return formatToDate(row.account_expired);
      },
    },
    {
      title: "密码有效期",
      key: "password_expired",
      width: 120,
      render(row) {
        return formatToDate(row.password_expired);
      },
    },
    // {
    //   title: "类别",
    //   key: "hr_com",
    //   width: 100,
    // },
    // {
    //   title: "主管",
    //   key: "group_leader_name",
    //   width: 100,
    // },
    {
      title: "组织架构",
      key: "group_lines",
      minWidth: 240,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return h("span", formatter(row, "group_lines"));
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 150,
      fixed: "right",
      render(row) {
        return (
          <NFlex>
            <NButton
              text
              type="info"
              onClick={() => handleUpdate?.(cloneDeep(row))}
            >
              编辑
            </NButton>
            <NButton
              text
              type="error"
              loading={row.deleteLoading}
              onClick={() => handleDelete?.(row)}
            >
              删除
            </NButton>
            <NDropdown
              options={dropdownOptions(row)}
              onSelect={key => handleSelect(key, row)}
              trigger="hover"
            >
              <NButton text type="info">
                更多
                <n-icon>
                  <CAIcon icon="tabler:chevron-down" size={15} />
                </n-icon>
              </NButton>
            </NDropdown>
          </NFlex>
        );
      },
    },
  ];

  // 下拉按钮
  function dropdownOptions(row): Array<{ label: string; key: string }> {
    const changeAccountItem: Array<{ label: string; key: string }> = [];
    if ([0, 1].includes(row.state)) {
      changeAccountItem.push({
        label: row.state === 1 ? "启用" : "禁用",
        key: "changeAccountState",
      });
    }
    return [
      // {
      //   label: "覆盖项目",
      //   key: "coverageProject",
      // },
      // {
      //   label: "覆盖机器",
      //   key: "coverageMach",
      // },
      ...changeAccountItem,
    ];
  }

  const dialog = useDialog();
  const message = useMessage();

  onMounted(handleUserTreeLoadSuccess);

  // 监听 queryCriteria.username 变化，自动拼接 q 字段
  watch(
    [() => queryCriteria.value.username],
    () => {
      getParams();
    },
    { immediate: true },
  );

  /**
   * 重置请求参数，拼接查询字符串 q
   */
  function getParams() {
    let q = ``;
    // 筛选条件
    if (queryCriteria.value.username) {
      q += `username=~${queryCriteria.value.username}`;
    }
    // 筛选团队组织
    if (allUserTree.value.length) {
      const firstUserId = allUserTree.value[0]?.id;
      expandedKeys.value = expandedKeys.value.length
        ? expandedKeys.value
        : allUserTree.value.map(item => item.id);
      selectedKeys.value = selectedKeys.value.length
        ? selectedKeys.value
        : firstUserId
          ? [firstUserId]
          : [];
      // queryCriteria.value.team_ids = [selectedKeys.value[0]];
      // q += `${q.length ? ',' : ''}team_ids=${queryCriteria.value.team_ids.join(',')}`;
    } else {
      expandedKeys.value = [];
      selectedKeys.value = [];
      // queryCriteria.value.team_ids = [];
    }
    queryCriteria.value.q = q;
  }

  /**
   * 用户树加载成功时的处理逻辑，初始化参数并加载表格数据
   */
  function handleUserTreeLoadSuccess() {
    getParams();
    loadData({
      getQueryCriteria: () => queryCriteria.value,
      dataPath: "data.value",
      totalPath: "data.total",
      tableRequest: (userListApi as unknown) as (
        queryCriteria?: QueryCriteria
      ) => Promise<UserListItem[]>,
      handleTableData: (dataSource: UserListExpandedItem[]) => {
        return dataSource.map((item, index) => {
          return {
            ...item,
            key: item.id ? String(item.id) : index.toString(),
            deleteLoading: false,
          } as UserListExpandedItem;
        });
      },
    });
  }

  /**
   * 处理页码变化
   * 1. 设置当前页码
   * 2. 加载数据
   * @param page - 当前页码
   */
  function onPageChange(page: number) {
    tablePagination.value.page = page;
    getParams();
    handlePageChange(page);
  }

  /**
   * 处理每页条数变化
   * 1. 重置页码为第一页
   * 2. 设置当前每页条数
   * 3. 加载数据
   * @param pageSize - 每页条数
   */
  function onPageSizeChange(pageSize: number) {
    tablePagination.value.page = 1;
    tablePagination.value.pageSize = pageSize;
    getParams();
    handlePageSizeChange(pageSize);
  }

  /**
   * 表格字段格式化
   * @param row - 当前行数据
   * @param property - 字段名
   * @returns string | any
   */
  function formatter(row: any, property: string) {
    const cellValue = row[property];
    const emptyChar = "--";
    if (!row) {
      return emptyChar;
    }
    let result = cellValue;
    switch (property) {
      case "preserved_user":
        result = row.preserved === 1 ? "离职" : "在职";
        break;
      case "group_lines":
        result = cellValue ? cellValue.join("/") : cellValue;
        break;
      case "entry_time":
        result = formatToDate(cellValue);
        break;
    }
    return result || emptyChar;
  }

  const x = ref(0);
  const y = ref(0);

  /**
   * 渲染用户树节点后缀操作按钮
   * @param param0 - 传入的 option 对象
   * @returns VNode
   */
  function renderSuffix({ option }: { option: TreeOption }) {
    return h(
      NButton,
      {
        text: true,
        size: "tiny",
        onClick: (e) => {
          e.stopPropagation();
          selectedTeam.value = option;
          contextMenuVisible.value = true;
          nextTick().then(() => {
            x.value = e.clientX;
            y.value = e.clientY;
          });
        },
      },
      {
        default: () =>
          h(CAIcon, {
            icon: "tabler:dots-vertical",
          }),
      },
    );
  }

  /**
   * 点击下拉菜单外部时关闭菜单
   */
  function onClickoutside() {
    contextMenuVisible.value = false;
  }

  /**
   * 处理团队右键菜单选择
   * @param key - 菜单项 key
   */
  function handleContextMenuSelect(key) {
    contextMenuVisible.value = false;

    if (!selectedTeam.value) {
      return;
    }

    switch (key) {
      case "add":
        addTeam();
        break;
      case "edit":
        editTeam();
        break;
      case "delete":
        deleteTeam();
        break;
    }
  }

  // 当前选中的团队
  const selectedTeamData = ref<{ id?: number; name?: string; parent_id?: number; weight?: number } | null>(null);

  /**
   * 添加团队，弹出编辑对话框
   */
  function addTeam() {
    if (!selectedTeam.value) {
      return;
    }
    selectedTeamData.value = { name: "", parent_id: selectedTeam.value.id as number };
    editDialogVisible.value = true;
  }

  /**
   * 编辑团队，弹出编辑对话框并回填数据
   */
  function editTeam() {
    if (!selectedTeam.value) {
      return;
    }
    const selectedParentTeam = findParentNode(allUserTree.value, selectedTeam.value?.id);
    selectedTeamData.value = {
      id: selectedTeam.value.id as number,
      name: selectedTeam.value.name as string,
      parent_id: typeof selectedParentTeam?.id === "number" ? selectedParentTeam?.id : null,
    };
    editDialogVisible.value = true;
  }

  /**
   * 新增/编辑团队成功回调，关闭弹窗并刷新用户树
   */
  function handleEditTeamPositiveClick() {
    editDialogVisible.value = false;
    userTreeRef.value?.reloadData();
  }

  /**
   * 查找父节点
   * @param nodes - 节点数组
   * @param targetId - 目标节点 id
   * @returns 父节点对象或 null
   */
  function findParentNode(nodes, targetId) {
    for (const node of nodes) {
      if (node.children?.some(child => child.id === targetId)) {
        return node; // 找到父节点
      }

      // 递归查找子节点
      const parent = findParentNode(node.children || [], targetId);
      if (parent) {
        return parent;
      }
    }
    return null; // 未找到父节点（可能是根节点）
  }

  const isRootNode = computed(() => {
    if (!selectedTeam.value) {
      return true;
    }
    const parent = findParentNode(allUserTree.value, (selectedTeam.value?.id));
    return !!parent; // 没有父节点即为根节点
  });

  const contextMenuOptions = ref([
    {
      label: "新建团队",
      key: "add",
    },
    {
      label: "编辑团队",
      key: "edit",
    },
    {
      label: "删除团队",
      key: "delete",
      show: isRootNode,
    },
  ]);

  /**
   * 删除团队，弹窗确认后调用接口
   */
  function deleteTeam() {
    if (!selectedTeam.value) {
      return;
    }
    dialog.warning({
      title: "删除确认",
      content: "确认要删除这个团队吗？",
      positiveText: "确定",
      negativeText: "取消",
      draggable: true,
      onPositiveClick: async () => {
        try {
          await deleteTeamApi(selectedTeam.value?.id as number);
          message.success("删除成功");
          userTreeRef.value?.reloadData();
        } catch (error) {
          message.error("删除失败");
        }
      },
    });
  }

  /**
   * 查询按钮点击，重置页码并刷新表格
   */
  function handleSearch() {
    tablePagination.value.page = 1;
    getParams();
    reloadData();
  }

  /**
   * 更新展开的团队节点
   * @param keys - 展开的节点 key 数组
   */
  function handleUpdateExpandedKeys(keys) {
    expandedKeys.value = keys;
  }

  /**
   * 更新选中的团队节点，并联动查询
   * @param keys - 选中的节点 key 数组
   */
  function handleUpdateSelectedKeys(keys) {
    selectedKeys.value = keys;
    queryCriteria.value.team_ids = [keys[0]];
    handleSearch();
  }

  function handleDrop({ node, dragNode, dropPosition }: TreeDropInfo) {
    /*
    const [dragNodeSiblings, dragNodeIndex] = findSiblingsAndIndex(
      dragNode,
      dataRef.value,
    );
    if (dragNodeSiblings === null || dragNodeIndex === null) {
      return;
    }
    dragNodeSiblings.splice(dragNodeIndex, 1);
    if (dropPosition === "inside") {
      if (node.children) {
        node.children.unshift(dragNode);
      } else {
        node.children = [dragNode];
      }
    } else if (dropPosition === "before") {
      const [nodeSiblings, nodeIndex] = findSiblingsAndIndex(
        node,
        dataRef.value,
      );
      if (nodeSiblings === null || nodeIndex === null) {
        return;
      }
      nodeSiblings.splice(nodeIndex, 0, dragNode);
    } else if (dropPosition === "after") {
      const [nodeSiblings, nodeIndex] = findSiblingsAndIndex(
        node,
        dataRef.value,
      );
      if (nodeSiblings === null || nodeIndex === null) {
        return;
      }
      nodeSiblings.splice(nodeIndex + 1, 0, dragNode);
    }
    dataRef.value = Array.from(dataRef.value);
    */
  }

  const userFormModalShow = ref(false);
  const currUser = ref<UserListExpandedItem | null>(null);

  /**
   * 新增用户，弹出用户表单
   */
  function handleAdd() {
    currUser.value = null;
    openUserFormModal();
  }

  /**
   * 处理表格操作下拉菜单选择
   * @param key - 菜单项 key
   * @param row - 当前行数据
   */
  function handleSelect(key: string | number, row: UserListExpandedItem) {
    if (String(key) === "coverageProject") {
      coverageProject(row);
    }
    if (String(key) === "coverageMach") {
      coverageMach(row);
    }
    if (String(key) === "changeAccountState") {
      changeAccountState(row);
    }
  }

  /**
   * 编辑用户，弹出用户表单并回填数据
   * @param row - 当前行数据
   */
  function handleUpdate(row: UserListExpandedItem) {
    currUser.value = row;
    openUserFormModal();
  }

  /**
   * 打开用户表单弹窗
   */
  function openUserFormModal() {
    userFormModalShow.value = true;
  }

  const visible = ref(false);
  const visibleMach = ref(false);
  const currentUid = ref<number>(0);

  /**
   * 覆盖项目弹窗
   * @param row - 当前行数据
   */
  function coverageProject(row: UserListExpandedItem) {
    currentUid.value = row.id;
    visible.value = true;
  }

  /**
   * 覆盖机器弹窗
   * @param row - 当前行数据
   */
  function coverageMach(row: UserListExpandedItem) {
    currentUid.value = row.id;
    visibleMach.value = true;
  }

  /**
   * 切换账号状态（启用/禁用）
   * @param row - 用户行数据
   * @returns Promise<void>
   */
  async function changeAccountState(row: UserListExpandedItem) {
    const isDisable = row.state === 0;
    const actionText = isDisable ? "禁用" : "启用";
    dialog.info({
      title: "警告",
      content: `确认要${actionText}该账号吗？`,
      positiveText: "确定",
      negativeText: "取消",
      async onPositiveClick() {
        try {
          const params = {
            username: row.username,
            nickname: row.nickname,
            email: row.email,
            state: isDisable ? 1 : 2, // 1: 禁用, 2: 启用
          };
          await updateUserApi(params);
          message.success(`${actionText}成功`);
          refreshData();
        } catch (error) {
          message.error(`${actionText}失败，请重试`);
        }
      },
    });
  }

  /**
   * 用户信息编辑弹窗点击确定后回调，刷新表格
   * @returns Promise<void>
   */
  async function handlePositiveClick() {
    // await userStore.fetchUserTree(null, true);
    handleSearch();
  }

  /**
   * 删除用户，弹窗确认后调用接口
   * @param row - 当前行数据
   */
  function handleDelete(row: UserListExpandedItem) {
    if (row.deleteLoading) {
      return;
    }

    dialog.warning({
      title: "警告",
      content: "您确定要删除此用户吗？",
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: () => {
        row.deleteLoading = true;
        deleteUserApi(row.username)
          .then(() => {
            message.success("删除成功");
            handleSearch();
          })
          .finally(() => {
            row.deleteLoading = false;
          });
      },
    });
  }
</script>

<style scoped></style>
