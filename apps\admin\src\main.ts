import { setupDirectives } from "@celeris/directives";
import { setupI18n } from "@celeris/locale";
import { createApp } from "vue";
import VueDOMPurifyHTML from "vue-dompurify-html";
import { setupVueQuery } from "~/apis";
import App from "~/App.vue";
import { initializeConfiguration } from "~/AppConfiguration";
import { setupComponents } from "~/component";
import { setupPermissionDirective } from "~/directives/permission";
// import { setupLoadingDirective } from "~/directives/loading";
import { setupRouterGuard } from "~/router/guard";

import { router, setupRouter } from "./router";

import { setupStore } from "./store";
import { setupEventBus } from "./utils/eventBus";

import "virtual:svg-icons-register";
import "@celeris/styles";
import "uno.css";
import "marked";

// // v-md-editor
// import VueMarkdownEditor from "@kangc/v-md-editor";
// import githubTheme from "@kangc/v-md-editor/lib/theme/github.js";
// import vuepressTheme from "@kangc/v-md-editor/lib/theme/vuepress.js";
// import hljs from "highlight.js";
// import Prism from "prismjs";
// import "@kangc/v-md-editor/lib/style/base-editor.css";
// import "@kangc/v-md-editor/lib/theme/style/github.css";
// import VMdPreviewHtml from "@kangc/v-md-editor/lib/preview-html"
// import "@kangc/v-md-editor/lib/style/preview-html.css";
// import "@kangc/v-md-editor/lib/theme/style/vuepress.css";
// VueMarkdownEditor.use(vuepressTheme, {
//   Prism,
// });
// VueMarkdownEditor.use(githubTheme, {
//   Hljs: hljs,
// });

const app = createApp(App);

app.use(VueDOMPurifyHTML);

// app.use(VueMarkdownEditor);
// app.use(VMdPreviewHtml);

// Configure vue-query
// 配置 vue-query
setupVueQuery(app);

// Configure store
// 配置 store
setupStore(app);

// Configure routing
// 配置路由
setupRouter(app);

// Configure router guard
// 配置路由守卫
setupRouterGuard(router);

// Register global directive
// 注册全局指令
setupDirectives(app);
setupPermissionDirective(app);
// setupLoadingDirective(app)

setupEventBus(app);
setupComponents(app);

void Promise.all([
  // Initialize internal system configuration
  // 初始化内部系统配置
  initializeConfiguration(),
  // Configure i18n
  // 配置国际化
  setupI18n(app),
]).finally(() => {
  app.mount("#app");
});
