<template>
  <PageWrapper>
    <template #default="slotProps: { rect?: { height: number } }">
      <NFlex
        class="overflow-hidden flex-1"
        size="large"
        vertical
        :style="{ height: slotProps?.rect?.height ? `${slotProps.rect.height - 32}px` : 'auto' }"
      >
        <!-- 工具栏 -->
        <NPageHeader>
          <NFlex class="pr" justify="space-between">
            <NFlex>
              <NForm ref="searchFormRef" :model="searchModel" :show-label="false" :show-feedback="false" inline @submit.prevent>
                <NFormItem path="tag">
                  <NSelect
                    v-model:value="searchModel.tag"
                    :options="platformList"
                    placeholder="请选择平台"
                    class="w-50"
                  >
                  </NSelect>
                </NFormItem>
                <NFormItem path="user">
                  <NSelect
                    v-model:value="searchModel.user"
                    :options="userList"
                    :loading="userLoading"
                    filterable
                    remote
                    placeholder="请输入用户账号"
                    @search="debouncedRemoteUserList"
                  />
                </NFormItem>
                <NFormItem path="path">
                  <NInput v-model:value="searchModel.path" placeholder="请输入路径" clearable @keyup.enter="searchConfirm" />
                </NFormItem>
                <NFormItem path="method">
                  <NInput v-model:value="searchModel.method" placeholder="请输入请求方法" clearable @keyup.enter="searchConfirm" />
                </NFormItem>
                <NFormItem path="date">
                  <NDatePicker
                    v-model:value="queryTime"
                    type="datetimerange"
                    class="w-80"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :shortcuts="pickerOptions.shortcuts"
                    :is-date-disabled="pickerOptions.disabledDate"
                  />
                </NFormItem>
                <NFormItem>
                  <NSpace>
                    <NButton type="primary" @click="searchConfirm">
                      查询
                    </NButton>
                    <NButton @click="searchReset">
                      重置
                    </NButton>
                  </NSpace>
                </NFormItem>
              </NForm>
            </NFlex>
          </NFlex>
        </NPageHeader>
        <!-- 表格 -->
        <NDataTable
          class="fixed-right-table h-full [&_.ca-data-table-resize-button::after]:(!bg-[var(--primary-color)])"
          scroll-x="min-content"
          remote
          flex-height
          :bordered="false"
          :single-line="false"
          :columns="columns"
          :data="tableData"
          :loading="tableLoading"
          :pagination="tablePagination"
          @scroll="handleScroll"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        >
        </NDataTable>
      </NFlex>
    </template>
  </PageWrapper>
</template>

<script setup lang="tsx">
  import type { Audit, AuditExpanded } from "-/audit";
  import type { DataTableColumns, FormInst, SelectOption } from "naive-ui";
  import { dateUtil, formatToDateTime } from "@celeris/utils";
  import { debounce } from "lodash-es";
  import { NButton, NFlex } from "naive-ui";
  import { getUserListByKeyword } from "~/apis/internal/authz";
  import { getUserAudits } from "~/apis/internal/userAudit";
  import PageWrapper from "~/component/PageWrapper/src/PageWrapper.vue";

  interface QueryCriteria {
    tag: string | null;
    user: string | null;
    path: string | null;
    method: string | null;
    from: string | null;
    to: string | null;
  }

  const INIT_QUERY_MODEL = {
    tag: "one",
    user: null,
    path: null,
    method: null,
  };

  const queryCriteria = ref<QueryCriteria>({
    tag: "one",
    user: null,
    path: null,
    method: null,
    from: null,
    to: null,
  });

  const {
    tableLoading,
    tableData,
    tablePagination,
    loadData,
    refreshData,
    reloadData,
    handlePageChange,
    handlePageSizeChange,
    handleScroll,
  } = useTable<QueryCriteria, Audit, AuditExpanded>();

  const domainId = import.meta.env.VITE_APP_BASE_DOMAIN_ID;

  // 查询参数
  const searchModel = ref({ ...INIT_QUERY_MODEL });
  const queryTime = ref<[number, number]>([
    dateUtil().subtract(3, "day").valueOf(),
    dateUtil().valueOf(),
  ]);

  // 用户账号选项
  const userLoading = ref(false);
  const userList = ref<any[]>([]);

  // 平台列表
  const platformList = ref<SelectOption[]>([
    {
      label: "one平台操作审计",
      value: "one",
    },
    // {
    //   label: '第三方登录平台',
    //   value: 'app',
    // },
  ]);

  // 一天几毫秒
  const d = 3600 * 1000 * 24;
  const pickerOptions = {
    shortcuts: {
      最近一周: () => {
        const end = Date.now();
        const start = end - d * 7;
        return [start, end] as [number, number];
      },
      最近一个月: () => {
        const end = Date.now();
        const start = end - d * 30;
        return [start, end] as [number, number];
      },
      最近三个月: () => {
        const end = Date.now();
        const start = end - d * 90;
        return [start, end] as [number, number];
      },
    },
    disabledDate: (current: number, type: "start" | "end", range: [number, number] | null) => {
      // 今天之后的时间不可选
      if (current > Date.now() || current < new Date("2023-03-01").getTime()) {
        return true;
      }
      // 选择日期范围为三个月内
      if (type === "start" && range !== null) {
        return (
          dateUtil(range[1]).startOf("day").valueOf() - dateUtil(current).startOf("day").valueOf() > d * 90
        );
      }
      if (type === "end" && range !== null) {
        return (
          dateUtil(current).startOf("day").valueOf() - dateUtil(range[0]).startOf("day").valueOf() > d * 90
        );
      }
      return false;
    },
  };

  const searchFormRef = useTemplateRef<FormInst>("searchFormRef");

  // ========== 表格列定义 ==========
  const columns: DataTableColumns<AuditExpanded> = [
    {
      title: "用户",
      key: "nickname",
      width: 100,
      render(row) {
        return (<a class="text-blue cursor-pointer">{ row.nickname }</a>);
      },
    },
    {
      title: "账号",
      key: "username",
      width: 120,
    },
    {
      title: "邮箱",
      key: "email",
      width: 150,
    },
    {
      title: "路径",
      key: "path",
      render(row) {
        const tag = searchModel.value.tag;
        const path = row.path || "";
        return tag === "one" ? path : decodeURIComponent(path);
      },
    },
    {
      title: "方法",
      key: "method",
      width: 80,
      render(row) {
        return row.method || "-";
      },
    },
    {
      title: "源IP",
      width: 130,
      key: "ip",
    },
    {
      title: "时间",
      key: "time",
      width: 170,
      render(row) {
        return formatToDateTime(row.time);
      },
    },
    {
      title: "描述",
      key: "description",
      render(row) {
        return row.description || "-";
      },
    },
  ];

  // 初始化
  onMounted(() => {
    loadTableData();
  });

  // 监听 searchModel、queryTime 变化，同步到 queryCriteria
  watch([searchModel, queryTime], ([searchVal, timeVal]) => {
    Object.keys(searchVal).forEach((key) => {
      queryCriteria.value[key] = searchVal?.[key];
    });
    if (timeVal) {
      queryCriteria.value.from = formatToDateTime(timeVal[0]);
      queryCriteria.value.to = formatToDateTime(timeVal[1]);
    }
  }, { immediate: true, deep: true });

  /**
   * @description 获取用户审计列表
   */
  function loadTableData() {
    loadData({
      getQueryCriteria: () => queryCriteria.value,
      dataPath: "data",
      totalPath: "page.total",
      tableRequest: getUserAudits as unknown as (queryCriteria?: QueryCriteria) => Promise<Audit[]>,
      handleTableData: (dataSource: AuditExpanded[]) => {
        return dataSource.map((item, index) => {
          return {
            ...item,
            key: item.id ? String(item.id) : index.toString(),
            deleteLoading: false,
          } as AuditExpanded;
        });
      },
    });
  }

  /**
   * @description 筛选条件查询，重置页码为1并加载数据
   */
  function searchConfirm() {
    reloadData();
  }

  /**
   * @description 查询条件重置，清空筛选项并重置页码，重新加载数据
   */
  function searchReset() {
    searchModel.value.user = null;
    searchModel.value.path = null;
    searchModel.value.method = null;
    reloadData();
  }

  /**
   * @description 远程搜索可选用户，根据输入关键字从后端获取用户列表。
   * @param query - 用户输入的搜索关键字
   * @returns {void}
   */
  function remoteUserList(query: string) {
    if (!query || !query.length) {
      userList.value = [];
      return;
    }
    userLoading.value = true;
    getUserListByKeyword(domainId, { keyword: query })
      .then((res) => {
        userList.value = res.data;
      })
      .finally(() => {
        userLoading.value = false;
      });
  }

  /**
   * @description 对远程搜索用户方法 remoteUserList 进行防抖处理，减少接口请求频率。
   * @param query - 用户输入的搜索关键字
   * @returns {void}
   */
  const debouncedRemoteUserList = debounce(remoteUserList, 300);
</script>

<style scoped></style>
