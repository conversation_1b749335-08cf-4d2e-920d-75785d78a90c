<template>
  <NTree
    key-field="id"
    label-field="name"
    block-line
    virtual-scroll
    :data="allUserTree as unknown as TreeOption[]"
    :show-irrelevant-nodes="false"
    :draggable="props.draggable"
  />
</template>

<script setup lang="ts">
  import type { TreeOption } from "@celeris/ca-components";
  import { useUserStore } from "~/store/modules/user";

  const props = defineProps({
    /** 是否可拖拽 */
    draggable: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits<{
    (e: "loadSuccess"): void;
  }>();

  const userStore = useUserStore();
  const { allUserTree } = storeToRefs(userStore);

  onMounted(loadData);

  /**
   * @description 加载用户树数据，并在加载完成后触发 loadSuccess 事件
   * @returns Promise<void>
   */
  async function loadData() {
    await userStore.fetchAllUserTree();
    emit("loadSuccess");
  }
  /**
   * @description 重新加载用户树数据（强制刷新），并在加载完成后触发 loadSuccess 事件
   * @returns Promise<void>
   */
  async function reloadData() {
    await userStore.fetchAllUserTree(true);
    emit("loadSuccess");
  }
  // 暴露方法给父组件
  defineExpose({
    reloadData,
  });
</script>

<style scoped>

</style>
