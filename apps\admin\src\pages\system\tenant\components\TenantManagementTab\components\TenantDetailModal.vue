<template>
  <NModal
    v-model:show="showModal"
    :mask-closable="false"
    preset="dialog"
    :title="modalTitle"
    class="!w-600px !max-w-[90vw]"
    @after-leave="handleAfterLeave"
  >
    <template #header>
      <div class="flex items-center w-full py-2">
        <div class="flex items-center space-x-3">
          <div class="w-1 h-6 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full"></div>
          <div class="flex items-center h-6">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100 leading-none my-0">
              {{ modalTitle }}
            </h3>
          </div>
        </div>
        <div v-if="data" class="flex items-center space-x-2 ml-auto">
          <NButton
            text
            type="primary"
            size="small"
            class="hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-200 mt--10px"
            @click="toggleMode"
          >
            <template #icon>
              <CAIcon :icon="isViewMode ? 'tabler:edit' : 'tabler:eye'" class="text-blue-600" />
            </template>
            {{ isViewMode ? '编辑模式' : '查看模式' }}
          </NButton>
        </div>
      </div>
    </template>

    <div class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-6 mt-4">
      <NForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="120px"
        class="space-y-6"
      >
        <!-- 基本信息区域 -->
        <div class="space-y-4">
          <div class="flex items-center space-x-2 mb-4">
            <CAIcon icon="tabler:info-circle" class="text-blue-600 text-lg" />
            <h4 class="text-base font-medium text-gray-800 dark:text-gray-200">
              基本信息
            </h4>
          </div>

          <NFormItem label="租户名称" path="name">
            <NInput
              v-model:value="formData.name"
              :disabled="isViewMode"
              placeholder="请输入租户名称"
              class="transition-all duration-200"
              :class="{ 'hover:shadow-sm': !isViewMode }"
            >
              <template #prefix>
                <CAIcon icon="tabler:building" class="text-gray-400" />
              </template>
            </NInput>
          </NFormItem>

          <NFormItem label="管理员" path="admin_name">
            <UserSelect
              v-model:value="formData.admin_name"
              :disabled="isViewMode"
              placeholder="请选择管理员"
              multiple
              max-tag-count="responsive"
            />
          </NFormItem>
          <NFormItem label="状态" path="state">
            <NSelect
              v-model:value="formData.state"
              :disabled="isViewMode"
              :options="statusOptions"
              placeholder="请选择状态"
              class="transition-all duration-200"
              :class="{ 'hover:shadow-sm': !isViewMode }"
            />
          </NFormItem>
        </div>
      </NForm>
    </div>

    <template #action>
      <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <NButton
          size="medium"
          class="px-6 py-2 transition-all duration-200 hover:shadow-md"
          @click="handleCancel"
        >
          <template #icon>
            <CAIcon icon="tabler:x" />
          </template>
          取消
        </NButton>
        <NButton
          v-if="!isViewMode"
          type="primary"
          size="medium"
          :loading="submitLoading"
          class="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 transition-all duration-200 hover:shadow-md"
          @click="handleSubmit"
        >
          <template #icon>
            <CAIcon icon="tabler:check" />
          </template>
          {{ submitLoading ? '保存中...' : '保存' }}
        </NButton>
      </div>
    </template>
  </NModal>
</template>

<script setup lang="ts">
  import type { TenantInfo, TenantInfoExpanded } from "-/tenant";
  import type { FormInst, FormRules } from "naive-ui";
  import { CAIcon } from "@celeris/components";
  import { cloneDeep } from "lodash-es";
  import { NButton, NForm, NFormItem, NInput, NModal, NSelect, useMessage } from "naive-ui";
  import { UserSelect } from "~/component/UserSelect";

  interface Props {
    show: boolean;
    data?: TenantInfoExpanded | null;
    mode?: "view" | "edit";
  }

  interface Emits {
    (e: "update:show", value: boolean): void;
    (e: "submit", data: TenantInfo): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    show: false,
    data: null,
    mode: "view",
  });

  const emit = defineEmits<Emits>();

  // ========== 响应式数据 ==========
  const message = useMessage();
  const formRef = ref<FormInst>();
  const showModal = computed({
    get: () => props.show,
    set: value => emit("update:show", value),
  });

  const isViewMode = ref(props.mode === "view");
  const submitLoading = ref(false);

  // ========== 表单数据 ==========
  function initFormData(): TenantInfo {
    return {
      name: "",
      admin_name: [],
      state: 0,
    };
  }

  const formData = ref<TenantInfo>(initFormData());

  // ========== 计算属性 ==========
  const modalTitle = computed(() => {
    if (!props.data) {
      return "新增租户";
    }
    return isViewMode.value ? "查看租户" : "编辑租户";
  });

  const statusOptions = [
    {
      label: "未激活",
      value: 0,
      style: "color: #10b981; font-weight: 500;",
    },
    {
      label: "已激活",
      value: 1,
      style: "color: #ef4444; font-weight: 500;",
    },
  ];

  // ========== 表单验证规则 ==========
  const formRules: FormRules = {
    name: [
      { required: true, message: "请输入租户名称", trigger: "blur" },
      { min: 2, max: 50, message: "租户名称长度应在2-50个字符之间", trigger: "blur" },
    ],
    admin_name: [
      {
        required: true,
        message: "请至少选择一个管理员",
        trigger: "blur",
        validator: (_rule: any, value: any) => {
          if (!value || !Array.isArray(value) || value.length === 0) {
            return new Error("请至少选择一个管理员");
          }
          return true;
        },
      },
    ],
    state: [
      {
        required: true,
        message: "请选择状态",
        trigger: "change",
        validator: (_rule: any, value: any) => {
          if (value === null || value === undefined || value === "") {
            return new Error("请选择状态");
          }
          return true;
        },
      },
    ],
  };

  // ========== 方法 ==========
  /**
   * 切换查看/编辑模式
   */
  function toggleMode() {
    isViewMode.value = !isViewMode.value;
  }

  /**
   * 处理取消操作
   */
  function handleCancel() {
    showModal.value = false;
  }

  /**
   * 处理提交操作
   */
  async function handleSubmit() {
    if (!formRef.value) {
      return;
    }

    try {
      await formRef.value.validate();
      submitLoading.value = true;
      // 根据是否有原始数据来决定提交的数据结构
      if (props.data?.id) {
        // 编辑模式：保留原有的完整字段，只更新表单中的字段
        const submitData: TenantInfo = {
          ...props.data,
          ...cloneDeep(formData.value),
        } as TenantInfo;
        emit("submit", submitData);
      } else {
        // 新增模式：只提交表单数据
        emit("submit", cloneDeep(formData.value));
      }
      showModal.value = false;
    } catch (error) {
      console.error("表单提交失败:", error);
      message.error("表单验证失败，请检查输入内容");
    } finally {
      submitLoading.value = false;
    }
  }

  /**
   * 弹框关闭后的回调
   */
  function handleAfterLeave() {
    // 重置表单数据
    formData.value = initFormData();
    isViewMode.value = props.mode === "view";
    submitLoading.value = false;
  }

  // ========== 监听器 ==========
  watch(
    () => props.data,
    (newData) => {
      if (newData) {
        const clonedData = cloneDeep(newData);
        // 确保 admin_name 是数组格式
        if (clonedData.admin_name && !Array.isArray(clonedData.admin_name)) {
          clonedData.admin_name = [clonedData.admin_name];
        } else if (!clonedData.admin_name) {
          clonedData.admin_name = [];
        }
        formData.value = clonedData;
      } else {
        formData.value = initFormData();
      }
    },
    { immediate: true },
  );

  watch(
    () => props.mode,
    (newMode) => {
      isViewMode.value = newMode === "view";
    },
    { immediate: true },
  );
</script>

<style scoped>
/* 表单标签样式 */
:deep(.n-form-item-label) {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

:deep(.dark .n-form-item-label) {
  color: #d1d5db;
}

/* 禁用状态的输入框样式 */
:deep(.n-input[disabled]) {
  color: var(--n-text-color) !important;
  background-color: #f9fafb;
  border-color: #e5e7eb;
}

:deep(.dark .n-input[disabled]) {
  background-color: #1f2937;
  border-color: #374151;
}

:deep(.n-select[disabled]) {
  color: var(--n-text-color) !important;
}

/* 输入框聚焦效果 */
:deep(.n-input:not([disabled]):hover) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

:deep(.n-input:not([disabled]):focus-within) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 选择框样式 */
:deep(.n-select:not([disabled]):hover .n-base-selection) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 模态框样式 */
:deep(.n-modal) {
  backdrop-filter: blur(8px);
}

:deep(.n-dialog) {
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  background-color: rgba(255, 255, 255, 0.95);
}

:deep(.dark .n-dialog) {
  background-color: rgba(31, 41, 55, 0.95);
}

/* 按钮样式 */
:deep(.n-button) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

:deep(.n-button:hover) {
  transform: translateY(-1px);
}

/* 表单项间距 */
:deep(.n-form-item) {
  margin-bottom: 20px;
}

:deep(.n-form-item:last-child) {
  margin-bottom: 0;
}
</style>
