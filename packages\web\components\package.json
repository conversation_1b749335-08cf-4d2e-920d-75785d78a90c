{"name": "@celeris/components", "type": "module", "version": "0.0.3", "description": "components for Celeris", "author": "<PERSON> (https://github.com/kirklin)", "license": "MIT", "homepage": "https://github.com/kirklin/celeris-web", "main": "./index.ts", "module": "./index.ts", "scripts": {"clean": "pnpm rimraf node_modules && pnpm rimraf dist"}, "peerDependencies": {"vue": ">=3.3.4"}, "dependencies": {"@celeris/ca-components": "workspace:*", "@celeris/constants": "workspace:*", "@celeris/styles": "workspace:*", "@celeris/utils": "workspace:*", "echarts": "^5.5.1", "vue-echarts": "^7.0.3"}, "devDependencies": {"@celeris/assets": "workspace:*", "@celeris/types": "workspace:*", "vue": "^3.5.13", "vue-router": "^4.4.5"}}