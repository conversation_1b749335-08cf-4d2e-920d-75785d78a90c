import type { RouteLocationNormalizedLoaded } from "vue-router";
import { useTransitionSetting } from "~/composables";

interface Context {
  route: RouteLocationNormalizedLoaded;
}

interface TransitionOptions {
  enableTransition: boolean;
}

/**
 * 获取路由切换动画名称
 * Get the route transition name
 * @param context - 路由上下文
 * @param options - 动画选项
 * @returns 动画名称
 */
export function getTransitionName({ route }: Context, { enableTransition }: TransitionOptions): string | undefined {
  const { getRouterBasicTransition, getShouldEnableTransition } = useTransitionSetting();
  if (!enableTransition || !toValue(getShouldEnableTransition)) {
    return undefined;
  }

  return (route.meta.transitionName as string) || toValue(getRouterBasicTransition);
}
