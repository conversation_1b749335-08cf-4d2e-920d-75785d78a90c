{"name": "@celeris/types", "type": "module", "version": "0.0.3", "description": "types for Celeris", "author": "<PERSON> (https://github.com/kirklin)", "license": "MIT", "homepage": "https://github.com/kirklin/celeris-web", "main": "./index.ts", "module": "./index.ts", "types": "./index.ts", "scripts": {"clean": "pnpm rimraf node_modules && pnpm rimraf dist"}, "dependencies": {"@celeris/constants": "workspace:*"}, "devDependencies": {"pinia": "2.2.4", "vue": "^3.5.13", "vue-router": "^4.4.5"}}