<template>
  <NModal
    v-model:show="visible"
    preset="dialog"
    class="w-xl"
    title="覆盖项目"
    style="width: 650px; position: fixed; top: 100px; left: 50%; transform: translateX(-50%); background: #fff;"
    positive-text="确定"
    @positive-click="handleClose"
    @after-leave="handleClose"
  >
    <NDivider />
    <NDataTable :loading="loading" :data="tableData" :columns="columns" :max-height="300" />
    <NDivider />
  </NModal>
</template>

<script setup lang="ts">
  import type { CoverageProjectItem } from "-/user";
  import { getCoverageProjectApi } from "~/apis/internal/user";

  defineOptions({
    name: "CoverageProjectModal",
    inheritAttrs: false,
  });

  const props = defineProps({
    show: Boolean,
    uid: {
      type: Number,
      required: true,
    },
  });

  const emit = defineEmits<{
    "update:show": [show: boolean];
  }>();

  const visible = ref(props.show);

  const loading = ref(false);
  const tableData = ref<CoverageProjectItem[]>([]);
  const columns = [
    { title: "项目名称", key: "name" },
    { title: "角色", key: "userType" },
  ];

  watch(
    () => props.show,
    async (val) => {
      visible.value = val;
      if (val && props.uid) {
        loading.value = true;
        try {
          const res = await getCoverageProjectApi(props.uid, {});
          tableData.value = res.data || [];
        } catch (e) {
          tableData.value = [];
        } finally {
          loading.value = false;
        }
      } else if (!val) {
        tableData.value = [];
      }
    },
  );

  /**
   * 关闭弹窗并通知父组件更新显示状态
   * @function
   * @returns {void} 无返回值
   * @emits update:show 通知父组件关闭弹窗
   */
  function handleClose() {
    emit("update:show", false);
  }
</script>
