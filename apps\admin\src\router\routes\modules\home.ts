import type { RouteRecordRaw } from "vue-router";
import { LAYOUT } from "~/router/constant";

const home: RouteRecordRaw = {
  path: "/",
  name: "HOME",
  component: LAYOUT,
  redirect: "/home",
  meta: {
    title: "首页",
    icon: "i-tabler-home",
    orderNumber: 1,
    shouldHideSubMenuInMenu: true,
  },
  children: [
    {
      path: "home",
      name: "home",
      component: () => import("~/pages/home/<USER>"),
      meta: {
        title: "首页",
        icon: "i-tabler-home",
        shouldHideInMenu: true,
        shouldAffixToNavBar: true,
      },
    },
  ],
};

export default home;
