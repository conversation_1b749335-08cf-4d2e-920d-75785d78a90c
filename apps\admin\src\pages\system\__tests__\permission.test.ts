import { mount } from "@vue/test-utils";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { nextTick } from "vue";
import { deletePermission } from "~/apis/internal/permission";
import { getRoleList } from "~/apis/internal/role";
import { useTable } from "~/composables/useTable";
import Index from "../Permission/index.vue";

// mock 依赖
const messageMock = {
  success: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
  warning: vi.fn(),
};
vi.mock("~/apis/internal/permission", () => ({
  getPermissionList: vi.fn().mockResolvedValue([]),
  deletePermission: vi.fn().mockResolvedValue({}),
}));
vi.mock("~/apis/internal/role", () => ({
  getRoleList: vi.fn().mockResolvedValue([]),
}));
vi.mock("../Permission/components/PermissionFormModal.vue", () => ({
  default: {
    name: "PermissionFormModal",
    props: ["show"],
    template: "<div v-if=\"show\" class=\"mock-permission-form-modal\"></div>",
  },
}));
vi.mock("@celeris/ca-components", async (importOriginal) => {
  const actual: AnyObject = await importOriginal();
  return {
    ...actual,
    useMessage: () => messageMock,
    useDialog: () => ({
      info: vi.fn().mockImplementation(({ onPositiveClick }) => {
        if (onPositiveClick) {
          onPositiveClick();
        }
      }),
    }),
  };
});
vi.mock("~/composables/useTable", () => {
  const loadDataMock = vi.fn();
  const refreshDataMock = vi.fn();
  const reloadDataMock = vi.fn();
  return {
    useTable: () => ({
      tableLoading: false,
      tableData: [
        { id: 1, resource: "资源A", action: "读", role_id: "角色1", key: "1" },
      ],
      tablePagination: { page: 1, pageSize: 10, itemCount: 1 },
      loadData: loadDataMock,
      refreshData: refreshDataMock,
      reloadData: reloadDataMock,
      handlePageChange: vi.fn(),
      handlePageSizeChange: vi.fn(),
      handleScroll: vi.fn(),
    }),
  };
});

function getCurrentMessageMock() {
  return messageMock;
}

describe("permission 策略管理页", () => {
  let wrapper;
  let tableApi;
  beforeEach(async () => {
    wrapper = mount(Index, {
      props: { domainId: "test-domain" },
      global: {
        // stubs: ["PageWrapper", "NFlex", "NButton", "NForm", "NFormItem", "NInput", "NSpace", "NPageHeader", "NDataTable"],
      },
    });
    tableApi = useTable();
    await nextTick();
  });

  it("应渲染主要结构和表格数据", async () => {
    expect(wrapper.findComponent({ name: "PageWrapper" }).exists()).toBe(true);
    expect(wrapper.findComponent({ name: "DataTable" }).exists()).toBe(true);
    expect(wrapper.text()).toContain("资源A");
    expect(wrapper.text()).toContain("读");
  });

  // loadTableData
  it("loadTableData 应调用 loadData 并格式化数据", () => {
    wrapper.vm.loadTableData();
    expect(tableApi.loadData).toHaveBeenCalled();
  });

  // getRoles
  it("getRoles: 子域传入时直接赋值", async () => {
    await wrapper.setProps({ isSub: true, preRoles: [{ label: "角色A", value: "1" }] });
    await wrapper.vm.getRoles();
    expect(wrapper.vm.roleOptions).toEqual([{ label: "角色A", value: "1" }]);
  });

  it("getRoles: 非子域时调用 getRoleList", async () => {
    (getRoleList as any).mockResolvedValue({ data: [{ id: "2", name: "角色B" }] });
    await wrapper.setProps({ isSub: false, preRoles: undefined });
    await wrapper.vm.getRoles();
    expect(getRoleList).toHaveBeenCalled();
    expect(wrapper.vm.roleOptions).toEqual([{ label: "角色B", value: "2" }]);
  });

  // searchConfirm
  it("searchConfirm 应调用 reloadData", () => {
    wrapper.vm.searchConfirm();
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  // searchReset
  it("searchReset 应清空筛选项并 reloadData", () => {
    wrapper.vm.searchModel.resource = "xxx";
    wrapper.vm.searchModel.action = "yyy";
    wrapper.vm.searchReset();
    expect(wrapper.vm.searchModel.resource).toBeNull();
    expect(wrapper.vm.searchModel.action).toBeNull();
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  // handleAdd
  it("handleAdd 应弹出新增弹窗", () => {
    wrapper.vm.permissionFormModalShow = false;
    wrapper.vm.currPermission = { id: 1 };
    wrapper.vm.handleAdd();
    expect(wrapper.vm.permissionFormModalShow).toBe(true);
    expect(wrapper.vm.currPermission).toBeNull();
  });

  // handleEdit
  it("handleEdit 应弹出编辑弹窗并设置当前策略", () => {
    const row = { id: 2, resource: "资源B" };
    wrapper.vm.handleEdit(row);
    expect(wrapper.vm.permissionFormModalShow).toBe(true);
    expect(wrapper.vm.currPermission).toEqual(row);
  });

  // handleUpdatePermission
  it("handleUpdatePermission 应调用 searchConfirm", () => {
    wrapper.vm.handleUpdatePermission();
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  // handleDelete
  it("handleDelete: 删除成功应提示并刷新", async () => {
    const row = { id: 1 };
    (deletePermission as any).mockResolvedValue({});
    await wrapper.vm.handleDelete(row);
    expect(deletePermission).toHaveBeenCalled();
    expect(getCurrentMessageMock().success).toHaveBeenCalledWith("删除成功");
    expect(tableApi.refreshData).toHaveBeenCalled();
  });

  it("handleDelete: 删除失败应提示", async () => {
    const row = { id: 1 };
    (deletePermission as any).mockRejectedValue(new Error("fail"));
    await wrapper.vm.handleDelete(row);
    expect(getCurrentMessageMock().error).toHaveBeenCalledWith("删除失败");
  });

  // UI交互（已覆盖）
  it("点击新增策略按钮应弹出表单", async () => {
    expect(wrapper.find(".mock-permission-form-modal").exists()).toBe(false);
    console.warn(await wrapper.findAllComponents({ name: "Button" }));
    await wrapper.findAllComponents({ name: "Button" })[0].trigger("click");
    await nextTick();
    expect(wrapper.find(".mock-permission-form-modal").exists()).toBe(true);
  });

  it("点击查询按钮应调用 reloadData", async () => {
    await wrapper.findAllComponents({ name: "Button" })[1].trigger("click");
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  it("点击重置按钮应清空筛选并 reloadData", async () => {
    await wrapper.findAllComponents({ name: "Button" })[2].trigger("click");
    expect(tableApi.reloadData).toHaveBeenCalled();
  });

  it("点击删除按钮应弹出确认框并调用删除接口和刷新", async () => {
    await wrapper.findAllComponents({ name: "Button" })[3].trigger("click");
    await nextTick();
    expect(deletePermission).toHaveBeenCalled();
    expect(tableApi.refreshData).toHaveBeenCalled();
  });

  // it("编辑按钮应弹出表单并带入数据", async () => {
  //   await wrapper.findAllComponents({ name: "Button" })[3].trigger("click");
  //   await nextTick();
  //   expect(wrapper.findComponent({ name: "PermissionFormModal" }).exists()).toBe(true);
  // });
});
