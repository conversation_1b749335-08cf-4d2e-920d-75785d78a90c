import type { ApprovalListParams, TenantInfo, TenantListParams } from "-/tenant";
import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";
import { replaceUrlPathParams } from "@celeris/utils";

enum API {
  Tenant = "/v1/tenants",
  DeleteTenant = "/v1/tenants/{id}",
  TenantUsers = "/v1/tenants/{id}/users",
  ApprovelList = "/v1/tenants/approvals",
  RemoveUserFromTenant = "/v1/tenants/{tenantId}/users/{id}",
  SetDefaultTenant = "/v1/users/default/{tenantId}",
}

// 获取租户列表
export function tenantListApi(params: TenantListParams = {}, errorMessageMode: MessageMode = "message") {
  return request.get({ url: API.Tenant, params }, { errorMessageMode });
}
// 创建租户
export function createTenantApi(data: AnyObject = {}, errorMessageMode: MessageMode = "message") {
  return request.post({ url: API.Tenant, data }, { errorMessageMode });
}

// 更新租户
export function updateTenantApi(data: AnyObject = {}, errorMessageMode: MessageMode = "message") {
  return request.put({ url: API.Tenant, data }, { errorMessageMode });
}

// 删除租户
export function deleteTenantApi(id: string = "", errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.DeleteTenant, { id });
  return request.delete({ url }, { errorMessageMode });
}

// // 获取审批列表
export function approvelListApi(params: ApprovalListParams = {}, errorMessageMode: MessageMode = "message") {
  return request.get({ url: API.ApprovelList, params }, { errorMessageMode });
}

// 获取租户用户列表
export function getTenantUsersApi(params: AnyObject = {}, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.TenantUsers, { id: params.id });
  return request.get({ url, params }, { errorMessageMode });
}

// 添加用户到租户
export function addUserToTenantApi(data: AnyObject = {}, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.TenantUsers, { id: data.id} );
  return request.post({ url, data }, { errorMessageMode });
}

// 从租户移除用户
export function removeUserFromTenantApi(id: string, tenantId: string, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.RemoveUserFromTenant, { tenantId, id });
  return request.delete({ url }, { errorMessageMode });
}

// 设置默认租户
export function setDefaultTenantApi(tenantId: string, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.SetDefaultTenant, { tenantId });
  return request.put({ url }, { errorMessageMode });
}
