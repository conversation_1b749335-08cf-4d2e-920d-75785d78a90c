import type { Paging } from "./common";


// 租户基本信息
export interface TenantInfo {
  id?: string;
  tenant_id?: string; // 租户业务ID，用于API调用
  name: string;
  admin_name: string[] | null; // 支持多个管理员
  state: 0 | 1;
  registration_date?: string;
  update_time?: string;
  is_default?: boolean; // 是否为默认租户
}

// 扩展的租户信息（用于表格显示）
export type TenantInfoExpanded = TenantInfo & {
  key?: string;
  deleteLoading?: boolean;
  editLoading?: boolean;
};

// 租户查询条件
export interface QueryCriteria {
  q?: string; // 查询字符串参数
  name?: string; // 租户名称
}

// 租户列表查询参数
export interface TenantListParams extends Paging {
  q?: string; // 查询字符串参数
}

// 审批信息
export interface ApprovalInfo {
  id: string;
  applicant: string;
  tenant_name: string;
  tenant_id: string;
  state: "pending" | "approved" | "rejected";
  apply_time: string;
  process_time?: string;
  reason?: string;
  application_type: "create" | "update" | "delete" | "reset";
  approve_time?: string;
  approver?: string;
  description?: string;
}

// 扩展的审批信息（用于表格显示）
export type ApprovalInfoExpanded = ApprovalInfo & {
  key?: string;
  approveLoading?: boolean;
  rejectLoading?: boolean;
};

// 审批列表查询参数
export interface ApprovalListParams extends Paging {
  applicant?: string;
  tenant_name?: string;
  status?: string;
}
