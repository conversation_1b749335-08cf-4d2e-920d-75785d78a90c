import type { ComputedRef, Ref } from "vue";
import { isUndefined } from "@celeris/utils";
import { computed, toRef, watch } from "vue";
import { useState } from "./useState";

/**
 * Merges a local state with a prop value.
 * 将本地状态与 prop 值合并。
 *
 * @param defaultValue The default value for the local state.
 *                     本地状态的默认值。
 * @param props The props object containing the value to merge with.
 *             包含要合并的值的 props 对象。
 * @returns A tuple containing the merged value, a function to update the local state, and the local state itself.
 *          包含合并值、更新本地状态的函数以及本地状态本身的元组。
 */
export function useMergeState<T, E = T | undefined>(
  defaultValue: T,
  props: { value: E },
): [ComputedRef<T>, (val: E) => void, Ref<T>] {
  const value = toRef(props.value);
  const [localValue, setLocalValue] = useState(!isUndefined(value.value) ? value.value : defaultValue);
  watch(value, (newVal) => {
    isUndefined(newVal) && setLocalValue(undefined);
  });

  const mergeValue = computed(() => (!isUndefined(value.value) ? value.value : localValue.value));

  return [mergeValue, setLocalValue, localValue];
}
