<template>
  <PageWrapper>
    <template #default="slotProps: { rect?: { height: number } }">
      <NFlex
        class="overflow-hidden flex-1"
        size="large"
        vertical
        :style="{ height: slotProps?.rect?.height ? `${slotProps.rect.height - 32}px` : 'auto' }"
      >
        <!-- 工具栏 -->
        <NPageHeader>
          <NFlex class="pr" justify="space-between">
            <div class="!w-25%">
              <NButton type="primary" :loading="tableLoading" @click="handleAdd">
                新增授权
              </NButton>
            </div>
            <NFlex class="">
              <NForm ref="searchFormRef" :model="searchModel" :show-label="false" :show-feedback="false" inline @submit.prevent>
                <NFormItem path="role">
                  <NSelect
                    v-model:value="searchModel.role"
                    :options="roleOptions"
                    clearable
                    placeholder="请选择角色"
                    class="w-50"
                    @update:value="nextTick(searchConfirm)"
                  />
                  <!-- <NInput v-model:value="searchModel.username" placeholder="请选择角色" clearable @keyup.enter="searchConfirm" /> -->
                </NFormItem>
                <NFormItem path="resource">
                  <NInput v-model:value="searchModel.username" placeholder="请输入用户账号" clearable @keyup.enter="searchConfirm" />
                </NFormItem>
                <NFormItem>
                  <NSpace>
                    <NButton type="primary" @click="searchConfirm">
                      查询
                    </NButton>
                    <NButton @click="searchReset">
                      重置
                    </NButton>
                  </NSpace>
                </NFormItem>
              </NForm>
            </NFlex>
          </NFlex>
        </NPageHeader>
        <!-- 表格 -->
        <NDataTable
          class="fixed-right-table h-full [&_.ca-data-table-resize-button::after]:(!bg-[var(--primary-color)])"
          scroll-x="min-content"
          remote
          flex-height
          :bordered="false"
          :single-line="false"
          :columns="columns"
          :data="tableData"
          :loading="tableLoading"
          :pagination="tablePagination"
          @scroll="handleScroll"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        >
        </NDataTable>
      </NFlex>
      <!-- 新增/编辑弹窗 -->
      <AuthzFormModal v-model:show="authzFormModalShow" :data="currAuthz" :role-options="roleOptions" :domain-id="domainId" @positive-click="handleUpdateAuthz" />
    </template>
  </PageWrapper>
</template>

<script setup lang="tsx">
  import type { Authz, AuthzExpanded } from "-/authz";
  import type { Role } from "-/role";
  import type { DataTableColumns, FormInst, SelectOption } from "naive-ui";
  import type { PageResponseData } from "-/http";
  import { cloneDeep } from "lodash-es";
  import { NButton, NFlex } from "naive-ui";
  import { deleteUserAuthz, getUserAuthzList } from "~/apis/internal/authz";
  import { getRoleList } from "~/apis/internal/role";
  import PageWrapper from "~/component/PageWrapper/src/PageWrapper.vue";
  import AuthzFormModal from "./components/AuthzFormModal.vue";

  interface QueryCriteria {
    q: string | null;
  }
  /**
   * @description 父组件传入的 domainId、角色列表、是否子域
   */
  const props = defineProps<{
    domainId: string;
    preRoles?: SelectOption[];
    isSub?: boolean;
  }>();

  const INIT_QUERY_MODEL = {
    username: null,
    role: null
  };

  const queryCriteria = ref<QueryCriteria>({
    q: "",
  });

  const {
    tableLoading,
    tableData,
    tablePagination,
    loadData,
    refreshData,
    reloadData,
    handlePageChange,
    handlePageSizeChange,
    handleScroll,
  } = useTable<QueryCriteria, Authz, AuthzExpanded>();

  const message = useMessage();
  const dialog = useDialog();

  // 查询参数
  const searchModel = ref({ ...INIT_QUERY_MODEL });

  // 角色选项
  const roleOptions = ref<SelectOption[]>([]);

  // 新建和编辑弹窗相关
  const authzFormModalShow = ref(false);
  const currAuthz = ref<AuthzExpanded | null>(null);
  const searchFormRef = useTemplateRef<FormInst>("searchFormRef");

  // ========== 表格列定义 ==========
  const columns: DataTableColumns<AuthzExpanded> = [
    // {
    //   title: "授权序列",
    //   key: "id",
    // },
    {
      title: "用户账号",
      key: "username",
      render(row) {
        return (<a class="text-blue cursor-pointer">{ row.username }</a>);
      },
    },
    {
      title: "用户姓名",
      key: "nickname",
      render(row) {
        return (<a class="text-blue cursor-pointer">{ row.nickname }</a>);
      },
    },
    {
      title: "角色",
      key: "role",
    },
    {
      title: "操作",
      key: "actions",
      render(row) {
        return (
          <NFlex>
            <NButton
              text
              type="info"
              onClick={() => handleEdit?.(cloneDeep(row))}
            >
              分配角色
            </NButton>
            <NButton
              text
              type="error"
              loading={row.deleteLoading}
              onClick={() => handleDelete?.(row)}
            >
            删除
            </NButton>
          </NFlex>
        );
      },
    },
  ];

  // 初始化
  onMounted(() => {
    loadTableData();
    getRoles();
  });

  // 监听 searchModel 变化，同步到 queryCriteria
  watch(
    searchModel,
    (val) => {
      queryCriteria.value.q = "";
      const conds:string[] = [];
      if (val.username) {
        conds.push(`v0=~${val.username}`);
      }
      if (val.role) {
        conds.push(`v1=~${val.role}`);
      }
      queryCriteria.value.q = conds.join(",");
    },
    { deep: true },
  );

  /**
   * @description 获取用户授权列表
   */
  function loadTableData() {
    loadData({
      getQueryCriteria: () => queryCriteria.value,
      dataPath: "data.value",
      totalPath: "data.total",
      pathParam: props.domainId,
      tableRequest: getUserAuthzList as unknown as (queryCriteria?: QueryCriteria) => Promise<Authz[]>,
      handleTableData: (dataSource: AuthzExpanded[]) => {
        return dataSource.map((item, index) => {
          return {
            ...item,
            key: item.id ? String(item.id) : index.toString(),
            deleteLoading: false,
          } as AuthzExpanded;
        });
      },
    });
  }

  /**
   * @description 获取角色列表
   */
  async function getRoles() {
    if (props.isSub && props.preRoles) {
      roleOptions.value = props.preRoles;
    } else {
      await getRoleList(props.domainId, {
        // domain_id: props.domainId
        page_index: 1,
        page_size: 999,
      }).then((res: PageResponseData<Role[]>) => {
        roleOptions.value = res?.data?.value.map(obj => ({ label: obj.name, value: obj.name })) as SelectOption[];
      });
    }
  }

  /**
   * @description 筛选条件查询，重置页码为1并加载数据
   */
  function searchConfirm() {
    reloadData();
  }

  /**
   * @description 查询条件重置，清空筛选项并重置页码，重新加载数据
   */
  function searchReset() {
    searchModel.value.username = null;
    reloadData();
  }

  /**
   * @description 新增用户授权，弹出新增弹窗
   */
  function handleAdd() {
    authzFormModalShow.value = true;
    currAuthz.value = null;
  }

  /**
   * @description 编辑用户授权，弹出编辑弹窗并设置当前授权
   * @param {AuthzExpanded} row - 当前行用户授权数据
   */
  function handleEdit(row: AuthzExpanded) {
    authzFormModalShow.value = true;
    currAuthz.value = row;
  }

  /**
   * @description 新增/编辑弹窗确认后刷新列表
   */
  function handleUpdateAuthz() {
    searchConfirm();
  }

  /**
   * @description 删除用户授权，弹出确认框，确认后调用删除接口并刷新列表
   * @param {AuthzExpanded} row - 当前行用户授权数据
   */
  function handleDelete(row: AuthzExpanded) {
    dialog.info({
      title: "警告",
      content: `您确认要删除【${row.username}】用户授权吗？`,
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          await deleteUserAuthz(props.domainId, row.id as number);
          message.success("删除成功");
          refreshData();
        } catch (e) {
          message.error("删除失败");
        }
      },
    });
  }
</script>

<style scoped></style>
