{"name": "@celeris/request", "type": "module", "version": "0.0.3", "description": "request for <PERSON><PERSON><PERSON>", "author": "<PERSON> (https://github.com/kirklin)", "license": "MIT", "homepage": "https://github.com/kirklin/celeris-web", "main": "./index.ts", "module": "./index.ts", "scripts": {"clean": "pnpm rimraf node_modules && pnpm rimraf dist"}, "dependencies": {"@celeris/constants": "workspace:*", "@celeris/locale": "workspace:*", "@celeris/types": "workspace:*", "@celeris/utils": "workspace:*", "axios": "^1.7.7", "qs": "^6.13.1"}, "devDependencies": {"@types/qs": "^6.9.17"}}