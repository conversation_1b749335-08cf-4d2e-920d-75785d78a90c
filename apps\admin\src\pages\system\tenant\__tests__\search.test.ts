import { describe, expect, it, vi } from "vitest";
import { mount } from "@vue/test-utils";
import { createPinia, setActivePinia } from "pinia";
import TenantIndex from "../index.vue";

// Mock components
vi.mock("~/component/PageWrapper/src/PageWrapper.vue", () => ({
  default: { name: "PageWrapper", template: "<div><slot /></div>" },
}));

vi.mock("../components/QueryHeader/index.vue", () => ({
  default: { 
    name: "QueryHeader", 
    template: "<div><button @click='$emit(\"search\")'>搜索</button></div>",
    emits: ["search", "add"],
  },
}));

vi.mock("../components/TenantManagementTab/index.vue", () => ({
  default: { 
    name: "TenantManagementTab", 
    template: "<div>TenantManagementTab</div>",
    props: ["queryCriteria"],
    emits: ["tabChange", "addTenant"],
  },
}));

describe("租户搜索功能", () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it("应该正确处理搜索事件", async () => {
    const wrapper = mount(TenantIndex);
    const vm = wrapper.vm as any;

    // 初始搜索触发器值应该为0
    expect(vm.searchTrigger).toBe(0);

    // 模拟搜索事件
    await wrapper.findComponent({ name: "QueryHeader" }).vm.$emit("search");

    // 搜索触发器值应该增加
    expect(vm.searchTrigger).toBe(1);
  });

  it("应该正确提供搜索触发器给子组件", () => {
    const wrapper = mount(TenantIndex);
    const vm = wrapper.vm as any;

    // 验证provide的值
    expect(vm.searchTrigger).toBeDefined();
    expect(typeof vm.searchTrigger).toBe("number");
  });

  it("应该正确处理查询条件变化", async () => {
    const wrapper = mount(TenantIndex);
    const vm = wrapper.vm as any;

    // 初始查询条件应该为空对象
    expect(vm.queryCriteria).toEqual({});

    // 模拟查询条件变化
    vm.queryCriteria = { tenant_id: "test_tenant" };
    await wrapper.vm.$nextTick();

    // 查询条件应该正确更新
    expect(vm.queryCriteria.tenant_id).toBe("test_tenant");
  });
});
