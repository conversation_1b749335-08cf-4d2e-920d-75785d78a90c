<template>
  <NEl tag="div" class="flex flex-row flex-1 h-full w-full">
    <div v-if="sidebarVisible">
      <SidebarLayout />
    </div>
    <div class="overflow-hidden flex flex-col flex-1 h-full w-full">
      <header v-if="headerVisible" class="">
        <Header />
      </header>
      <div class="block flex-1 h-full overflow-x-hidden rounded-2xl pl-0 pr-5 pt-0 pb-2">
        <div class="min-h-full h-full w-full rounded-2xl bg-gray-100 p-4">
          <Content />
        </div>
      </div>
      <footer>
        <Footer />
      </footer>
    </div>
    <SearchDialog />
  </NEl>
</template>

<script setup lang="ts">
  import SearchDialog from "~/component/SearchDialog/src/SearchDialog.vue";
  import SidebarLayout from "~/layouts/sidebar/index.vue";
  import { useAppStore } from "~/store/modules/app";
  import Content from "./content/index.vue";
  import Footer from "./footer/index.vue";
  import Header from "./header/index.vue";

  defineOptions({
    name: "Layout",
  });

  const route = useRoute();
  const appStore = useAppStore();
  const headerSetting = toRef(appStore.getHeaderSetting);
  const menuSetting = toRef(appStore.getMenuSetting);

  // 获取是否显示头部
  // Get whether to show the header
  const headerVisible = computed(() => {
    const hasShouldHideHeader = Reflect.has(route.meta, "shouldHideHeader");
    return (!hasShouldHideHeader && headerSetting.value.shouldShow) || (hasShouldHideHeader && route.meta.shouldHideHeader && headerSetting.value.shouldShow);
  });

  // 获取是否显示侧边栏
  // Get whether to show the sidebar
  const sidebarVisible = computed(() => {
    const hasShouldHideSidebar = Reflect.has(route.meta, "shouldHideSidebar");
    return (!hasShouldHideSidebar && menuSetting.value.shouldShow) || (hasShouldHideSidebar && route.meta.shouldHideSidebar && menuSetting.value.shouldShow);
  });
</script>

