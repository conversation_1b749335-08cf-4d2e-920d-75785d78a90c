import { describe, expect, it, vi } from "vitest";
import { mount } from "@vue/test-utils";
import CodeReview from "../index.vue";

// Mock dependencies
vi.mock("~/apis/internal/codeReview", () => ({
  getCodeReviewList: vi.fn(() => Promise.resolve({
    items: [],
    total: 0,
    total_pages: 0
  })),
  getGitMessage: vi.fn(() => Promise.resolve({
    data: {}
  }))
}));

vi.mock("@celeris/utils", () => ({
  formatToDateTime: vi.fn(date => date ? "2024-01-01 12:00:00" : "--"),
}));

vi.mock("@celeris/components", () => ({
  CAIcon: {
    name: "CAIcon",
    template: "<span>Icon</span>"
  }
}));

describe("CodeReview", () => {
  it("should render correctly", () => {
    const wrapper = mount(CodeReview, {
      global: {
        stubs: {
          PageWrapper: true,
          NInputGroup: true,
          NInput: true,
          NButton: true,
          NSelect: true,
          NSpin: true,
          NEmpty: true,
          NPagination: true,
          NTag: true,
          TaskDetail: true,
          CAIcon: true
        }
      }
    });

    expect(wrapper.exists()).toBe(true);
  });

  it("should have correct initial data", () => {
    const wrapper = mount(CodeReview, {
      global: {
        stubs: {
          PageWrapper: true,
          NInputGroup: true,
          NInput: true,
          NButton: true,
          NSelect: true,
          NSpin: true,
          NEmpty: true,
          NPagination: true,
          NTag: true,
          TaskDetail: true,
          CAIcon: true
        }
      }
    });

    // Check if component has the expected structure
    expect(wrapper.find('.code-review-container').exists()).toBe(true);
    expect(wrapper.find('.header-section').exists()).toBe(true);
    expect(wrapper.find('.content-wrapper').exists()).toBe(true);
  });
});
