<template>
  <div ref="pageWrapperRef" class="page-wrapper" :class="[{ 'scrollbar-enabled': useScrollbar }]">
    <NScrollbar v-if="useScrollbar" class="rounded-2xl">
      <div
        v-if="useDefaultContent"
        class="rounded-2xl"
        :class="{
          'p-4': useDefaultPadding,
          'bg-white': useDefaultBgColor,
        }"
        :style="{
          minHeight: defaultContentFull && pageWrapperRect?.height ? `${pageWrapperRect?.height}px` : 'auto',
        }"
      >
        <slot :rect="pageWrapperRect" />
      </div>

      <slot v-else />
    </NScrollbar>

    <slot v-else />
  </div>
</template>

<script setup lang="ts">
  interface PageWrapperProps {
    useScrollbar?: boolean;
    useDefaultContent?: boolean;
    useDefaultPadding?: boolean;
    useDefaultBgColor?: boolean;
    defaultContentFull?: boolean;
  }

  withDefaults(defineProps<PageWrapperProps>(), {
    useScrollbar: true,
    useDefaultContent: true,
    useDefaultPadding: true,
    useDefaultBgColor: true,
    defaultContentFull: true,
  });

  const pageWrapperRef = useTemplateRef("pageWrapperRef");
  const pageWrapperRect = ref<DOMRect>();

  onMounted(() => {
    const el = pageWrapperRef.value as HTMLDivElement;
    pageWrapperRect.value = el.getBoundingClientRect();
  });
</script>

<style scoped>
.page-wrapper {
  /* Default styles for page-wrapper */
  @apply rounded-2xl size-full min-h-full;
}

.page-wrapper.scrollbar-enabled {
  /* Styles specific to when scrollbar is enabled */
  width: calc(100% + 8px);
}

.page-wrapper.scrollbar-enabled :deep(.ca-scrollbar-container) {
  width: calc(100% - 8px);
  @apply rounded-2xl;
}

/* 重置在 .ca-data-table 内的元素样式 */
.page-wrapper.scrollbar-enabled :deep(.ca-data-table .ca-scrollbar-container) {
  width: 100%;
  border-radius: 0;
}
</style>
