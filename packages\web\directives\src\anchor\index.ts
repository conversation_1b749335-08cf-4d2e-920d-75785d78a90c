import type { Directive, DirectiveBinding } from "vue";

const anchor: Directive = {
  /*
   * 挂载元素时，添加点击事件监听器
   * Add a click event listener when the element is mounted
   */
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    el.addEventListener("click", () => {
      const target = document.querySelector(binding.value);
      if (target) {
        target.scrollIntoView({ behavior: "smooth" });
      }
    });
  },
};

export default anchor;
