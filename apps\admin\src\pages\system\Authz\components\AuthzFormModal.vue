<template>
  <CardModal
    v-bind="$attrs"
    class="w-xl"
    :title="modalTitle"
    :empty-func="() => false"
    @update:show="handleHide"
    @after-enter="handleAfterEnter"
    @after-leave="handleAfterLeave"
  >
    <div class="p-4">
      <NForm ref="formRef" :model="formModel" :rules="formRules" label-width="60" label-placement="left" require-mark-placement="left">
        <NFormItem label="用户" path="username">
          <UserSelect
            v-model:value="formModel.username"
            placeholder="请输入用户账号或姓名"
            remote
            clearable
            :disabled="isEdit"
            :options="props.data?.username ? [{ username: props.data?.username, nickname: props.data?.nickname}] as SimpleUserItem[] : []"
          />
        </NFormItem>
        <NFormItem label="角色" path="role">
          <NSelect
            v-model:value="formModel.role"
            :options="roleOptions"
            placeholder="请选择角色"
          />
        </NFormItem>
      </NForm>
    </div>
    <template #footer>
      <NFlex justify="center">
        <NButton @click="handleHide">
          取消
        </NButton>
        <NButton type="primary" :loading="submitting" @click="handleCreate">
          确定
        </NButton>
      </NFlex>
    </template>
  </CardModal>
</template>

<script lang="ts" setup>
  import type { AuthzExpanded } from "-/authz";
  import type { FormInst, FormRules, SelectOption } from "naive-ui";
  import { debounce } from "lodash-es";
  import { createUserAuthz, getUserListByKeyword, updateUserAuthz } from "~/apis/internal/authz";
  import CardModal from "~/component/CardModal/src/CardModal.vue";
  import { UserSelect } from "~/component/UserSelect";
  import type { SimpleUserItem } from "-/user";

  defineOptions({
    name: "AuthzFormModal",
    inheritAttrs: false,
  });

  const props = withDefaults(defineProps<{
    data: AuthzExpanded | null;
    roleOptions: SelectOption[];
    domainId: string;
  }>(), {
    data: () => ({}) as AuthzExpanded,
  });

  const emit = defineEmits<{
    "update:show": [show: boolean];
    "positiveClick": [success: boolean];
  }>();

  /**
   * @description 用户授权表单初始值
   */
  const INIT_AUTHZ_FORM_MODEL: AuthzExpanded = {
    id: 0,
    username: null,
    nickname: null,
    role: undefined,
  };

  const isEdit = computed(() => !!(props.data && props.data.id));

  const modalTitle = computed(() => {
    return isEdit.value ? "编辑授权" : "新增授权";
  });

  const message = useMessage();

  const submitting = ref<boolean>(false);

  const loading = ref<boolean>(false);

  const userList = ref<any[]>([]);

  const formRef = useTemplateRef<FormInst>("formRef");
  const formModel = ref<AuthzExpanded>({ ...INIT_AUTHZ_FORM_MODEL });
  /**
   * @description 用户授权表单校验规则
   */
  const formRules: FormRules = {
    username: [
      { required: true, message: "请输入用户账号", trigger: "blur" },
    ],
    role: [
      { required: true, message: "请选择角色", trigger: "blur" },
    ],
  };

  /**
   * @description 弹窗进入动画后初始化表单数据。
   * @returns {void}
   */
  function handleAfterEnter() {
    props.data ? initFormModel() : clearFormModel();
  }

  /**
   * @description 弹窗关闭动画后清空表单数据。
   * @returns {void}
   */
  function handleAfterLeave() {
    clearFormModel();
  }

  /**
   * @description 关闭弹窗。
   * @returns {void}
   */
  function handleHide() {
    emit("update:show", false);
  }

  /**
   * @description 根据 props.data 初始化表单模型。
   * @returns {void}
   */
  function initFormModel() {
    const data = props.data;
    Object.keys(INIT_AUTHZ_FORM_MODEL).forEach((key) => {
      formModel.value[key] = data?.[key] || INIT_AUTHZ_FORM_MODEL[key];
    });
  }

  /**
   * @description 重置表单模型为初始值，并恢复校验状态。
   * @returns {void}
   */
  function clearFormModel() {
    formModel.value = { ...INIT_AUTHZ_FORM_MODEL };
    formRef.value?.restoreValidation?.();
  }

  /**
   * @description 提交表单，创建或更新用户授权。
   * @returns {Promise<void>}
   */
  async function handleCreate() {
    await formRef.value?.validate();
    // 判断是否有修改角色
    if (formModel.value.id && props.data?.role === formModel.value.role) {
      message.warning("角色未修改");
      return;
    }
    submitting.value = true;
    try {
      if (formModel.value.id) {
        const newAuth = {
          role: formModel.value.role,
          username: formModel.value.username,
        } as AuthzExpanded;
        await updateUserAuthz(props.domainId, [props.data as AuthzExpanded, newAuth]);
      } else {
        await createUserAuthz(props.domainId, [formModel.value]);
      }
      handleHide();
      emit("positiveClick", true);
    } catch (e) {
      message.error("操作失败");
    } finally {
      submitting.value = false;
    }
  }

  /**
   * @description 远程搜索可选用户，根据输入关键字从后端获取用户列表。
   * @param query - 用户输入的搜索关键字
   * @returns {void}
   */
  function remoteUserList(query: string) {
    if (!query || !query.length) {
      userList.value = [];
      return;
    }
    loading.value = true;
    getUserListByKeyword(props.domainId, { keyword: query })
      .then((res) => {
        userList.value = res.data;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /**
   * @description 对远程搜索用户方法 remoteUserList 进行防抖处理，减少接口请求频率。
   * @param query - 用户输入的搜索关键字
   * @returns {void}
   */
  const debouncedRemoteUserList = debounce(remoteUserList, 300);
</script>
