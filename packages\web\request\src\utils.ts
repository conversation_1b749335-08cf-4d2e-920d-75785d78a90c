import { isObject, isString } from "@celeris/utils";

const DATE_TIME_FORMAT = "YYYY-MM-DD HH:mm:ss";

export function joinTimestamp<T extends boolean>(
  join: boolean,
  restful: T,
): T extends true ? string : object;

/*
 * 拼接时间戳
 * @param join 是否拼接时间戳
 * @param restful 是否使用restful风格
 * @returns 如果restful为true，则返回字符串，否则返回对象
 */
export function joinTimestamp(join: boolean, restful = false): string | object {
  return join
    ? restful
      ? `?_t=${new Date().getTime()}`
      : { _t: new Date().getTime() }
    : restful
      ? ""
      : {};
}

/**
 * @description: Format request parameter time
 */
export function formatRequestDate(params: Record<string, any>) {
  if (!isObject(params)) {
    return;
  }

  Object.keys(params).forEach((key) => {
    const format = params[key]?.format ?? null;
    if (format && typeof format === "function") {
      params[key] = params[key].format(DATE_TIME_FORMAT);
    }
    if (isString(key)) {
      const value = params[key];
      if (value) {
        try {
          params[key] = isString(value) ? value.trim() : value;
        } catch (error: any) {
          throw new Error(error);
        }
      }
    }
    if (isObject(params[key])) {
      formatRequestDate(params[key]);
    }
  });
}
/**
 * Add the object as a parameter to the URL
 * @param baseUrl url
 * @param obj
 * @returns {string}
 * eg:
 *  let obj = {a: '3', b: '4'}
 *  setObjToUrlParams('www.baidu.com', obj)
 *  ==>www.baidu.com?a=3&b=4
 */
export function setObjToUrlParams(baseUrl: string, obj: Record<string, any>): string {
  let parameters = "";
  for (const key in obj) {
    parameters += `${key}=${encodeURIComponent(obj[key])}&`;
  }
  parameters = parameters.replace(/&$/, "");
  return /\?$/.test(baseUrl) ? `${baseUrl}${parameters}` : `${baseUrl.replace(/\/?$/, "?")}${parameters}`;
}
