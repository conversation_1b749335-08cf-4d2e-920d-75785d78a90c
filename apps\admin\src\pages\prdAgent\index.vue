<template>
  <NFlex class="h-full max-h-full overflow-hidden">
    <!-- 主要内容区 -->
    <main class="flex-1 flex gap-4 overflow-hidden min-h-0 rounded-2xl" style="max-height: calc(100vh - 60px);">
      <!-- 左侧表单组件 -->
      <div class="w-1/3 h-full max-h-full overflow-hidden rounded-2xl">
        <PrdForm
          ref="prdFormRef"
          :loading="formLoading"
          :sections="prdSections"
          :prd-content="prdContent"
          @generate="generatePRD"
          @show-his-detail="showHisDetail"
          @scroll-to-section="handleScrollToSection"
          @create-new="handleCreateNew"
        />
      </div>

      <!-- 右侧预览组件 -->
      <div class="w-2/3 h-full max-h-full overflow-hidden rounded-2xl">
        <PrdPreview ref="prdPreviewRef" :title="currentTitle" />
      </div>
    </main>
  </NFlex>
</template>

<script setup lang="ts">
  import { generatePrdStream, getPrdDetail } from "~/apis/internal/prd";
  import PrdForm from "./components/PrdForm.vue";
  import PrdPreview from "./components/PrdPreview.vue";

  const prdFormRef = ref<InstanceType<typeof PrdForm> | null>(null);
  const prdPreviewRef = ref<InstanceType<typeof PrdPreview> | null>(null);

  const formLoading = ref(false);
  const prdSections = ref<Record<string, string>>({});
  const prdContent = ref<string>(""); // PRD正文内容
  const currentTitle = ref("");
  const currentProjectId = ref(""); // 当前PRD项目ID
  const sseConnection = ref<{ disconnect: () => void } | null>(null); // SSE连接实例
  const message = useMessage();

  // 生成PRD的逻辑
  async function generatePRD(formData: any) {
    formLoading.value = true;

    // 清空之前的内容
    prdContent.value = "";
    prdSections.value = {};

    // 更新当前标题
    currentTitle.value = formData.title || "";

    try {
      // 使用EventSource流式生成PRD
      sseConnection.value = generatePrdStream(formData, {
        onStart: () => {
          console.warn("开始生成PRD...");
        },
        onProjectCreated: (projectId: string) => {
          console.warn("PRD项目已创建，ID:", projectId);
          // 保存项目ID到状态中，用于后续操作
          currentProjectId.value = projectId;
        },
        onMessage: (content: string) => {
          // 累积PRD内容
          prdContent.value += content;

          // 实时更新预览组件
          if (prdPreviewRef.value) {
            prdPreviewRef.value.updateContent({
              title: formData.title,
              content: prdContent.value,
            });
          }
        },
        onCompleted: () => {
          console.warn("PRD生成完成");
          formLoading.value = false;

          // 解析最终的sections
          const sections: Record<string, string> = {};
          const sectionRegex = /## ([^\n]+)\n([\s\S]*?)(?=## |$)/g;
          let match: RegExpExecArray | null;

          // eslint-disable-next-line no-cond-assign
          while ((match = sectionRegex.exec(prdContent.value)) !== null) {
            const sectionTitle = match[1].trim();
            const sectionContent = match[2].trim();
            sections[sectionTitle] = sectionContent;
          }

          // 更新sections状态
          prdSections.value = sections;

          // 设置表单组件为生成完成状态
          if (prdFormRef.value) {
            prdFormRef.value.setGeneratedView(true);
          }
        },
        onError: (error: any) => {
          console.error("生成PRD失败:", error);
          formLoading.value = false;
          // 这里可以添加错误提示
        },
      });
    } catch (error) {
      console.error("启动PRD生成失败:", error);
      message.error("生成PRD失败，请重试");
      formLoading.value = false;
    }
  }

  // 处理滚动到指定章节
  function handleScrollToSection(sectionTitle: string) {
    if (prdPreviewRef.value) {
      prdPreviewRef.value.scrollToSection(sectionTitle);
    }
  }

  // 查看历史prd详情
  async function showHisDetail(data: any) {
    try {
      const res = await getPrdDetail(data.id);
      console.warn("res", res);

      if (prdPreviewRef.value) {
        prdPreviewRef.value.updateContent({
          title: res.title,
          content: res.generated_prd || "",
        });
      }
      if (prdFormRef.value) {
        prdFormRef.value.setGeneratedView(true);
      }
      currentTitle.value = res.title;
      prdContent.value = res.generated_prd || "";
    } catch (_error) {
      console.error("获取PRD详情失败:", _error);
      message.error("获取PRD详情失败，请重试");
    }
  }

  // 处理新建PRD
  function handleCreateNew() {
    // 断开当前的SSE连接
    if (sseConnection.value) {
      sseConnection.value.disconnect();
      sseConnection.value = null;
    }

    // 重置所有状态
    formLoading.value = false;
    prdSections.value = {};
    prdContent.value = "";
    currentTitle.value = "";
    currentProjectId.value = "";

    // 清空预览内容
    if (prdPreviewRef.value) {
      prdPreviewRef.value.updateContent({
        title: "",
        content: "",
      });
    }

    // 确保表单处于编辑模式
    if (prdFormRef.value) {
      prdFormRef.value.setGeneratedView(false);
    }
  }
</script>

<style scoped>
/* 确保根元素无滚动 */
:deep(html),
:deep(body) {
  height: 100%;
  overflow: hidden;
}
</style>
