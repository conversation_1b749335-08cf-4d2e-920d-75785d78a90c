import type { Menu } from "@celeris/types";
import type { Router, RouteRecordRaw } from "vue-router";
import { HttpStatusConstants, PageConstants } from "@celeris/constants";
import { findTreeNode } from "@celeris/utils";
import { TOKEN_KEY_SIDE } from "~/router/constant";
import { PAGE_NOT_FOUND_ROUTE } from "~/router/routes/basic";
import { usePermissionStoreWithOut } from "~/store/modules/permission";
import { useUserStoreWithOut } from "~/store/modules/user";

const whitePathList: (PageConstants | string)[] = [PageConstants.BASE_LOGIN, '/tenant-select'];
const cookies = useCookies([], { autoUpdateDependencies: true });

let isGetUserInfo = false;
/*
 * 获取用户信息
 */
async function getUserInfo() {
  const userStore = useUserStoreWithOut();
  if (!userStore.getUserInfo) {
    try {
      await userStore.fetchUserInfo();
      userStore.getUserInfo && (isGetUserInfo = true);
      return !!userStore.getUserInfo;
    } catch (err) {
      return !!userStore.getUserInfo;
    }
  } else {
    if (!isGetUserInfo) {
      try {
        await userStore.fetchUserInfo();
        userStore.getUserInfo && (isGetUserInfo = true);
        return !!userStore.getUserInfo;
      } catch (err) {
        return !!userStore.getUserInfo;
      }
    }
    return true;
  }
}

/**
 * 重定向到登录页
 * Redirect to login page
 * @param redirectPath
 * @returns redirectData
 */
function redirectToLogin(redirectPath: string) {
  const redirectData: { path: string; replace: boolean; query?: Recordable<string> } = {
    path: PageConstants.BASE_LOGIN,
    replace: true,
  };
  if (redirectPath) {
    redirectData.query = {
      ...redirectData.query,
      redirect: redirectPath,
    };
  }
  return redirectData;
}

/**
 * 路由是否被禁用
 * Route is disabled
 * @param toPath
 * @returns boolean
 */
function routeDisabled(toPath: string) {
  const permissionStore = usePermissionStoreWithOut();
  const menu = findTreeNode<Menu>(permissionStore.getFrontendMenuList, menu => menu.path === toPath);
  return !!menu?.shouldDisabled;
}

/**
 * 创建一个权限守卫，当路由切换时检查用户是否有权限访问该路由
 * Creates a permission guard that checks whether the user has permission to access the route when the route is switched
 * @param router - 路由对象。
 */
export function createPermissionGuard(router: Router) {
  const userStore = useUserStoreWithOut();
  const permissionStore = usePermissionStoreWithOut();

  router.beforeEach(async (to, from, next) => {
    console.warn("走到1", to.path);
    // Whitelist can be directly entered
    if (whitePathList.includes(to.path)) {
      // 已登录用户访问登录页时，自动跳转到首页（但租户选择页面除外）
      if (userStore.getToken && to.path === PageConstants.BASE_LOGIN) {
        next(PageConstants.BASE_HOME);
        return;
      }
      next();
      return;
    }

    console.warn("走到2", userStore.getToken);
    if (!userStore.getToken) {
      // You can access without permission. You need to set the routing meta.ignoreAuth to true
      if (to.meta.shouldIgnoreAuth) {
        next();
        return;
      }

      // redirect login page
      // 如果非外部登录(没有done_token_side)，直接跳转登录页
      if (!cookies.get(TOKEN_KEY_SIDE)) {
        next(redirectToLogin(to.path));
        return;
      }
    }

    const allowNext = await getUserInfo();
    console.warn("走到3", allowNext);
    if (!allowNext) {
      // redirect login page
      next(redirectToLogin(to.path));
      return;
    }

    // 检查是否需要租户信息（排除租户选择页面）
    if (to.path !== '/tenant-select' && !userStore.getCurrentTenant) {
      // 尝试获取租户列表并设置当前租户
      try {
        const tenants = await userStore.fetchAvailableTenants(true);
         if (tenants.length === 1) {
          // 只有一个租户，自动设置
          await userStore.switchTenant(tenants[0].id!);
        } else {
          // 多个租户或没有租户，跳转到租户选择页面
          next('/tenant-select');
          return;
        }
      } catch (error) {
        console.error("获取租户信息失败:", error);
        // 继续访问，可能会在页面中显示错误
      }
    }

    console.warn("走到4", permissionStore.getShouldAddRouteDynamically);
    if (permissionStore.getShouldAddRouteDynamically) {
      if (routeDisabled(to.path)) {
        next(`${HttpStatusConstants.NotFound}`);
        return;
      }
      next();
      return;
    }

    await permissionStore.getUserPermissions();
    const routes = await permissionStore.buildRoutesAction();

    routes.forEach((route) => {
      router.addRoute(route);
    });

    router.addRoute(PAGE_NOT_FOUND_ROUTE as unknown as RouteRecordRaw);

    permissionStore.setShouldAddRouteDynamically(true);

    if (routeDisabled(to.path)) {
      next(`${HttpStatusConstants.NotFound}`);
      return;
    }

    if (to.name === PAGE_NOT_FOUND_ROUTE.name) {
      // 动态添加路由后，此处应当重定向到fullPath，否则会加载404页面内容
      next({ path: to.fullPath, replace: true, query: to.query });
    } else {
      const redirectPath = (from.query.redirect || to.path) as string;
      const redirect = decodeURIComponent(redirectPath);
      const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect };
      next(nextData);
    }
  });
}
