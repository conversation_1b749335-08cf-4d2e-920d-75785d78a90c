import type { CreateAxiosOptions } from "./axiosTransform";
import { deepMerge } from "@celeris/utils";
import { defaultAxiosOptions } from "./axiosOptions/defaultOptions";
import { HttpClient } from "./HttpClient";
import { SseClient } from "./SseClient";

/**
 * 创建一个HTTP客户端
 * Create an HTTP client
 * @param opt - 创建HTTP客户端的选项
 * Options for creating an HTTP client
 * @returns HTTP客户端
 * HTTP client
 */
export function createHttpClient(opt?: Partial<CreateAxiosOptions>) {
  return new HttpClient(
    deepMerge(
      defaultAxiosOptions,
      opt || {},
    ),
  );
}

/**
 * 创建一个SSE客户端
 * Create an SSE client
 * @returns SSE客户端
 * SSE client
 */
export function createSSEClient() {
  return new SseClient();
}

export const defaultHttpClient = createHttpClient();
export const defaultSseClient = createSSEClient();
