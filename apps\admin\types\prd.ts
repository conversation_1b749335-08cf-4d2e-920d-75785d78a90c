import type { Paging } from "./common";

/**
 * PRD列表查询参数
 */
export interface PrdListParams extends Paging {
  keyword?: string;
  status?: string;
  createTime?: string[];
  createdBy?: string;
}

/**
 * PRD基本信息
 */
export interface PrdItem {
  id: string;
  title: string;
  description: string;
  status: 'draft' | 'published' | 'archived';
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  version: string;
  tags?: string[];
  generated_prd?: string;
}

/**
 * PRD扩展信息（用于表格显示）
 */
export type PrdItemExpanded = PrdItem & {
  key?: string;
  deleteLoading?: boolean;
  editLoading?: boolean;
};

/**
 * PRD详细信息
 */
export interface PrdDetail extends PrdItem {
  content: string;
  sections: {
    pageContruction?: string;
    pdrAnalisis?: string;
    flowChart?: string;
    sequenceDiagram?: string;
    classDiagram?: string;
    dataFields?: string;
    testCases?: string;
  };
  formData: {
    featureName: string;
    description: string;
    requirements: string;
  };
}

/**
 * 创建PRD参数
 */
export interface CreatePrdParams {
  title: string;
  description: string;
  content?: string;
  formData: {
    featureName: string;
    description: string;
    requirements: string;
  };
  sections?: PrdDetail['sections'];
  tags?: string[];
}

/**
 * 更新PRD参数
 */
export interface UpdatePrdParams extends Partial<CreatePrdParams> {
  id: string;
  version?: string;
}

/**
 * PRD表单数据
 */
export interface PrdFormData {
  title: string;
  description: string;
  requirements: string;
}

/**
 * PRD生成参数
 */
export interface GeneratePrdParams {
  formData: PrdFormData;
  type?: 'full' | 'section';
  sectionKey?: string;
}

/**
 * PRD生成响应
 */
export interface GeneratePrdResponse {
  content?: string;
  sections?: PrdDetail['sections'];
}

/**
 * PRD历史记录
 */
export interface PrdHistory {
  id: string;
  prdId: string;
  version: string;
  content: string;
  sections: PrdDetail['sections'];
  createdAt: string;
  createdBy: string;
  changeLog?: string;
}

/**
 * PRD模板
 */
export interface PrdTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  sections: PrdDetail['sections'];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * PRD统计信息
 */
export interface PrdStatistics {
  total: number;
  draft: number;
  published: number;
  archived: number;
  recentlyCreated: number;
  recentlyUpdated: number;
}
