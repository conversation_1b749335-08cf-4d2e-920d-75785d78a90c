<template>
  <CardModal
    v-bind="$attrs"
    class="w-xl"
    :title="modalTitle"
    :empty-func="() => false"
    :mask-closable="false"
    @update:show="handleHide"
    @after-enter="handleAfterEnter"
    @after-leave="handleAfterLeave"
  >
    <div class="p-4">
      <NForm
        ref="formRef"
        label-width="80"
        label-placement="left"
        require-mark-placement="left"
        filterable
        :model="formModel"
        :rules="formRules"
      >
        <NFormItem path="username" label="账号">
          <NInput v-model:value="formModel.username" :disabled="isUpdateUser" placeholder="请输入账号" />
        </NFormItem>
        <NFormItem path="nickname" label="姓名">
          <NInput v-model:value="formModel.nickname" placeholder="请输入姓名" />
        </NFormItem>

        <NFormItem path="email" label="邮箱">
          <NInput v-model:value="formModel.email" placeholder="请输入邮箱" />
        </NFormItem>

        <NFormItem path="telephone" label="手机号">
          <NInput v-model:value="formModel.telephone" clearable placeholder="请输入手机号" />
        </NFormItem>

        <!-- <NFormItem path="password" label="密码">
          <NInput
            v-model:value="formModel.password"
            type="password"
            show-password-on="click"
            maxlength="18"
            show-count
            :disabled="isUpdateUser"
          />
        </NFormItem> -->
      </NForm>
    </div>

    <template #footer>
      <NFlex justify="center">
        <NButton @click="handleHide">
          取消
        </NButton>
        <NButton type="primary" :loading="loading" @click="handleConfirm">
          确定
        </NButton>
      </NFlex>
    </template>
  </CardModal>
</template>

<script lang="ts" setup>
  import type { UserListExpandedItem } from "-/user";
  import type { FormInst, FormItemRule } from "naive-ui";
  import { CHINESE_PHONE_REGEX, EMAIL_REGEX } from "@celeris/constants";
  import { createUserApi, updateUserApi } from "~/apis/internal/user";
  import CardModal from "~/component/CardModal/src/CardModal.vue";
  import UserTreeSelect from "~/component/UserTreeSelect/src/UserTreeSelect.vue";
  // import { i18n } from "@celeris/locale";
  // import { validatePassword } from "~/utils/cipher";

  defineOptions({
    name: "UserFormModal",
    inheritAttrs: false,
  });

  const props = withDefaults(defineProps<{
    data: UserListExpandedItem | null;
  }>(), {
    data: () => ({}) as UserListExpandedItem,
  });

  const emit = defineEmits<{
    "update:show": [show: boolean];
    "positiveClick": [success: boolean];
  }>();

  const isUpdateUser = computed(() => !!(props.data && props.data.username));

  const modalTitle = computed(() => {
    return isUpdateUser.value ? "更新用户信息" : "新增用户";
  });

  /**
   * @description 弹窗进入动画后初始化表单数据。
   * 若有传入 data，则初始化为 data，否则清空表单。
   */
  function handleAfterEnter() {
    props.data ? initFormModel() : clearFormModel();
  }

  /**
   * @description 弹窗关闭动画后清空表单数据。
   */
  function handleAfterLeave() {
    clearFormModel();
  }

  /**
   * @description 关闭弹窗，通知父组件更新显示状态。
   */
  function handleHide() {
    emit("update:show", false);
  }

  // 初始表单数据
  const INIT_USER_FORM_MODEL = {
    username: "",
    nickname: "",
    email: "",
    telephone: "",
    group_id: "",
  };

  /**
   * @description 获取用户表单的校验规则
   * @param isUpdateUser - 是否为更新用户（true: 更新，false: 新增）
   * @returns 用户表单的校验规则对象
   */
  function getUserFormRules(isUpdateUser: boolean) {
    return {
      username: [{ required: true, message: "请输入账号", trigger: "input" }],
      nickname: [{ required: true, message: "请输入姓名", trigger: "input" }],
      email: [
        { required: true, message: "请输入邮箱", trigger: "input" },
        {
          validator: (rule: FormItemRule, value: string) => EMAIL_REGEX.test(value),
          message: "邮箱格式不合法",
          trigger: "input",
        },
      ],
      telephone: [
        { required: true, message: "请输入手机号", trigger: "input" },
        {
          validator: (rule: FormItemRule, value: string) => value ? CHINESE_PHONE_REGEX.test(value) : true,
          message: "手机号格式不合法",
          trigger: "input",
        },
      ],
      // password: [
      //   { required: !isUpdateUser, message: "请输入密码", trigger: "input" },
      //   {
      //     validator: (rule: FormItemRule, value: string): boolean => !isUpdateUser ? validatePassword(value) : true,
      //     // @ts-expect-error 忽略找不到 t 函数的错误
      //     message: i18n.global.t("page.login.form.password.validator"),
      //     trigger: ["input"],
      //   },
      // ],
      group_id: [{ required: false, message: "请选择上级领导", trigger: "input" }],
    };
  }

  const formRef = useTemplateRef<FormInst>("formRef");
  const formModel = ref({ ...INIT_USER_FORM_MODEL });
  const formRules = computed(() => getUserFormRules(isUpdateUser.value));
  const loading = ref(false);

  /**
   * @description 根据 props.data 初始化表单模型。
   */
  function initFormModel() {
    const data = props.data;
    Object.keys(INIT_USER_FORM_MODEL).forEach((key) => {
      formModel.value[key] = data?.[key] || INIT_USER_FORM_MODEL[key];
    });
    formModel.value.group_id = data?.group_id || "";
  }

  /**
   * @description 重置表单模型为初始值，并恢复表单校验状态。
   */
  function clearFormModel() {
    formModel.value = { ...INIT_USER_FORM_MODEL };
    formRef.value?.restoreValidation?.();
  }

  const message = useMessage();

  /**
   * @description 提交表单，进行校验并调用新增或更新用户接口。
   * 校验通过后自动关闭弹窗并通知父组件。
   * @returns {Promise<void>}
   */
  async function handleConfirm() {
    try {
      await formRef.value?.validate();
      const data = {
        nickname: formModel.value.nickname,
        username: isUpdateUser.value ? props.data?.username : formModel.value.username,
        telephone: formModel.value.telephone,
        email: formModel.value.email,
        groupid: formModel.value.group_id,
        password: isUpdateUser.value ? undefined : "IqU7m)5KTat+",
      };
      loading.value = true;
      isUpdateUser.value ? await updateUserApi(data) : await createUserApi(data);
      handleHide();
      emit("positiveClick", true);
      message.success(`${modalTitle.value}成功`);
    } catch (e) {
      console.error(e);
    } finally {
      loading.value = false;
    }
  }
</script>
