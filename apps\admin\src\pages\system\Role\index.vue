<template>
  <PageWrapper>
    <template #default="slotProps: { rect?: { height: number } }">
      <NFlex
        class="overflow-hidden flex-1"
        size="large"
        vertical
        :style="{ height: slotProps?.rect?.height ? `${slotProps.rect.height - 32}px` : 'auto' }"
      >
        <!-- 工具栏 -->
        <NPageHeader>
          <NFlex class="pr" justify="space-between">
            <div class="!w-25%">
              <NButton type="primary" :loading="tableLoading" @click="handleAdd">
                新增角色
              </NButton>
            </div>
            <NFlex class="">
              <NForm ref="searchFormRef" :model="searchModel" :show-label="false" :show-feedback="false" inline @submit.prevent>
                <NFormItem path="resource">
                  <NInput v-model:value="searchModel.name" placeholder="请输入角色名称" clearable @keyup.enter="searchConfirm" />
                </NFormItem>
                <NFormItem>
                  <NSpace>
                    <NButton type="primary" @click="searchConfirm">
                      查询
                    </NButton>
                    <NButton @click="searchReset">
                      重置
                    </NButton>
                  </NSpace>
                </NFormItem>
              </NForm>
            </NFlex>
          </NFlex>
        </NPageHeader>
        <!-- 表格 -->
        <NDataTable
          class="fixed-right-table h-full [&_.ca-data-table-resize-button::after]:(!bg-[var(--primary-color)])"
          scroll-x="min-content"
          remote
          flex-height
          :bordered="false"
          :single-line="false"
          :columns="columns"
          :data="tableData"
          :loading="tableLoading"
          :pagination="tablePagination"
          @scroll="handleScroll"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        >
        </NDataTable>
      </NFlex>
      <!-- 新增/编辑弹窗 -->
      <RoleFormModal v-model:show="roleFormModalShow" :data="currRole" :domain-id="domainId" @positive-click="handleUpdateRole" />
      <!-- 设置权限 -->
      <AuthorityDrawer v-model:show="authVisible" :data="currRole" :domain-id="domainId"></AuthorityDrawer>
    </template>
  </PageWrapper>
</template>

<script setup lang="tsx">
  import type { Role, RoleExpanded } from "-/role";
  import type { DataTableColumns, FormInst } from "naive-ui";
  import { cloneDeep } from "lodash-es";
  import { NButton, NFlex } from "naive-ui";
  import { deleteRole, getRoleList } from "~/apis/internal/role";
  import PageWrapper from "~/component/PageWrapper/src/PageWrapper.vue";
  import AuthorityDrawer from "./components/AuthorityDrawer.vue";
  import RoleFormModal from "./components/RoleFormModal.vue";

  interface QueryCriteria {
    q: string | null;
  }
  /**
   * @description 父组件传入的 domainId、角色列表、是否子域
   */
  const props = defineProps<{
    domainId: string;
  }>();

  const INIT_QUERY_MODEL = {
    name: null,
  };

  const queryCriteria = ref<QueryCriteria>({ q: "" });

  const {
    tableLoading,
    tableData,
    tablePagination,
    loadData,
    refreshData,
    reloadData,
    handlePageChange,
    handlePageSizeChange,
    handleScroll,
  } = useTable<QueryCriteria, Role, RoleExpanded>();

  const message = useMessage();
  const dialog = useDialog();

  // 查询参数
  const searchModel = ref({ ...INIT_QUERY_MODEL });

  // 新建和编辑弹窗相关
  const roleFormModalShow = ref(false);
  const currRole = ref<RoleExpanded | null>(null);
  const searchFormRef = useTemplateRef<FormInst>("searchFormRef");

  // 设置权限弹窗相关
  const authVisible = ref(false);

  // ========== 表格列定义 ==========
  const columns: DataTableColumns<RoleExpanded> = [
    {
      title: "ID",
      key: "id",
    },
    {
      title: "角色名称",
      key: "name",
      render(row) {
        return (<a class="text-blue cursor-pointer">{ row.name }</a>);
      },
    },
    {
      title: "描述",
      key: "description",
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "创建时间",
      key: "created_at",
    },
    {
      title: "更新时间",
      key: "updated_at",
    },
    {
      title: "领域标识",
      key: "project_name",
    },
    {
      title: "操作",
      key: "actions",
      width: 180,
      render(row) {
        return (
          <NFlex>
            <NButton
              text
              type="info"
              onClick={() => handleAuthority?.(cloneDeep(row))}
            >
              设置权限
            </NButton>
            <NButton
              text
              type="info"
              onClick={() => handleEdit?.(cloneDeep(row))}
            >
              编辑
            </NButton>
            <NButton
              text
              type="error"
              loading={row.deleteLoading}
              onClick={() => handleDelete?.(row)}
            >
            删除
            </NButton>
          </NFlex>
        );
      },
    },
  ];

  // 初始化
  onMounted(() => {
    loadTableData();
  });

  // 监听 searchModel 变化，同步到 queryCriteria
  watch(
    searchModel,
    (val) => {
      queryCriteria.value.q = "";
      if (val.name) {
        queryCriteria.value.q = `name=~${val.name}`;
      }
    },
    { deep: true },
  );

  /**
   * @description 获取角色列表
   */
  function loadTableData() {
    loadData({
      getQueryCriteria: () => queryCriteria.value,
      dataPath: "data.value",
      totalPath: "data.total",
      pathParam: props.domainId,
      tableRequest: getRoleList as unknown as (queryCriteria?: QueryCriteria) => Promise<Role[]>,
      handleTableData: (dataSource: RoleExpanded[]) => {
        return dataSource.map((item, index) => {
          return {
            ...item,
            key: item.id ? String(item.id) : index.toString(),
            deleteLoading: false,
          } as RoleExpanded;
        });
      },
    });
  }

  /**
   * @description 筛选条件查询，重置页码为1并加载数据
   */
  function searchConfirm() {
    reloadData();
  }

  /**
   * @description 查询条件重置，清空筛选项并重置页码，重新加载数据
   */
  function searchReset() {
    searchModel.value.name = null;
    reloadData();
  }

  /**
   * @description 新增角色，弹出新增弹窗
   */
  function handleAdd() {
    roleFormModalShow.value = true;
    currRole.value = null;
  }

  /**
   * @description 编辑角色，弹出编辑弹窗并设置当前角色
   * @param {RoleExpanded} row - 当前行角色数据
   */
  function handleEdit(row: RoleExpanded) {
    roleFormModalShow.value = true;
    currRole.value = row;
  }

  /**
   * @description 新增/编辑弹窗确认后刷新列表
   */
  function handleUpdateRole() {
    searchConfirm();
  }

  /**
   * 设置权限
   */
  function handleAuthority(row: RoleExpanded) {
    currRole.value = row;
    authVisible.value = true;
  }

  /**
   * @description 删除角色，弹出确认框，确认后调用删除接口并刷新列表
   * @param {RoleExpanded} row - 当前行角色数据
   */
  function handleDelete(row: RoleExpanded) {
    dialog.info({
      title: "警告",
      content: `您确认要删除角色【${row.name}】及授权吗？`,
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          await deleteRole(props.domainId, row.id as string);
          message.success("删除成功");
          refreshData();
        } catch (e) {
          message.error("删除失败");
        }
      },
    });
  }
</script>

<style scoped></style>
