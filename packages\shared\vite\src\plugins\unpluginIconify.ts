import type { PluginOption } from "vite";
import Icons from "unplugin-icons/vite";

/**
 * Create unplugin-iconify plugin configuration
 * 创建 unplugin-iconify 插件配置
 * @returns Vite plugin configuration array Vite插件配置数组
 */
export function createIconifyPlugin(): PluginOption {
  return Icons({
    compiler: "vue3", // 适用于 Vue 3 项目
    autoInstall: false, // 禁用自动联网下载图标（适合内网）
    scale: 1, // SVG 缩放比例（默认1）
  } as any);
}
