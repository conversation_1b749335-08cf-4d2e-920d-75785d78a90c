import { mount } from "@vue/test-utils";
import { createPinia } from "pinia";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { getCode, login } from "~/apis/internal/login";
import Index from "../index.vue";

vi.mock("~/apis/internal/login", () => ({
  getCode: vi.fn().mockResolvedValue({ code: 0 }),
  getLoginMode: vi.fn().mockResolvedValue({ code: 0 }),
  login: vi.fn(),
  getUserInfo: vi.fn().mockResolvedValue({ user: { nick_name: "1", user_name: "1" } }),
}));

vi.mock("naive-ui", async (importOriginal) => {
  const actual: AnyObject = await importOriginal();
  return {
    ...actual,
    useMessage: () => ({
      info: vi.fn(),
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
    }),
  };
});

const mockRouter = { push: vi.fn() };
vi.mock("vue-router", async (importOriginal) => {
  const actual: AnyObject = await importOriginal();
  return {
    ...actual, // 展开真实模块，保证其他导出可用
    useRouter: () => mockRouter, // 只重写 useRouter
  };
});

describe("login Page", () => {
  let wrapper: any;

  beforeEach(() => {
    wrapper = mount(Index, {
      global: {
        plugins: [createPinia()],
      },
    });
  });

  // it("should render background image", () => {
  //   expect(wrapper.find(".wrapper").attributes("style")).toContain("background-image: url(");
  // });

  it("should render logo", () => {
    expect(wrapper.find(".header__logo").exists()).toBe(true);
  });

  it("should render login form", () => {
    expect(wrapper.find(".loginForm").exists()).toBe(false);
  });

  // it("should render login tabs", () => {
  //   expect(wrapper.findAll(".tab-item").length).toBeGreaterThan(1);
  // });

  // it("should switch login tabs", async () => {
  //   const tabItems = wrapper.findAll(".tab-item");
  //   expect(tabItems[0].classes()).toContain("active");
  //   await tabItems[1].trigger("click");
  //   expect(tabItems[1].classes()).toContain("active");
  // });
});

describe("login Page Logic", () => {
  let wrapper;
  let vm;
  beforeEach(async () => {
    wrapper = mount(Index, {
      global: {
        mocks: {
          // useRouter: () => mockRouter,
          // useMessage: () => ({ success: vi.fn(), error: vi.fn(), warning: vi.fn() }),
          // useUserStore: () => ({ login: vi.fn().mockResolvedValue({ data: { user: { state: 0 } } }) }),
        },
      },
    });
    vm = wrapper.vm;
    await nextTick();
  });

  it("should submit form and login success", async () => {
    vm.formModel.username = "user";
    vm.formModel.password = "pwd";
    vm.formModel.code = "1234";
    vm.aes_secret = "key";
    vm.ruleForm.value = {
      validate: vi.fn().mockResolvedValue(undefined),
    };
    (login as any).mockResolvedValueOnce({ data: { user: { state: 0 } } });
    vm.message.success = vi.fn();
    await vm.submitForm();

    expect(vm.message.success).toHaveBeenCalledWith("登录成功");
  });

  it("should handle login fail", async () => {
    vm.formModel.username = "user";
    vm.formModel.password = "pwd";
    vm.formModel.code = "1234";
    vm.aes_secret = "key";
    vm.ruleForm.value = {
      validate: vi.fn().mockResolvedValue(undefined),
    };
    (login as any).mockResolvedValueOnce({ data: { user: { state: 1 } }, message: "fail" });
    vm.message.warning = vi.fn();
    await vm.submitForm();
    expect(vm.message.warning).toHaveBeenCalledWith("fail");
  });

  it("should handle login exception", async () => {
    vm.validate = vi.fn().mockRejectedValue(new Error("err"));
    vm.message.error = vi.fn();
    await vm.submitForm();
    expect(vm.message.error).toHaveBeenCalledWith("登录失败，请重试");
  });

  it("should validate form success", async () => {
    vm.formModel.username = "user";
    vm.formModel.password = "pwd";
    vm.ruleForm.value = {
      validate: vi.fn().mockResolvedValue(undefined),
    };
    await expect(vm.validate()).resolves.toBeUndefined();
  });

  it("should validate form fail", async () => {
    vm.ruleForm.value = {
      validate: vi.fn().mockRejectedValue(false),
    };
    vi.spyOn(vm.ruleForm, "validate").mockRejectedValue(false);
    await expect(vm.validate()).rejects.toThrow("表单验证失败");
  });

  it("should throw if no form instance", async () => {
    vm.selectMode = "test";
    await nextTick();
    await expect(vm.validate()).rejects.toThrow("表单实例不存在");
  });

  it("should handle getMsgCode with empty username", async () => {
    vm.formModel.username = "";
    vm.message.error = vi.fn();
    const result = await vm.getMsgCode();
    expect(vm.message.error).toHaveBeenCalledWith("请输入用户名");
    expect(result).toBe(false);
  });

  it("should handle getMsgCode success", async () => {
    vm.formModel.username = "user";
    vm.codeLoading = false;
    vm.countdown = 0;
    vm.countdownInterval = null;

    let intervalCalled = false;
    window.setInterval = vi.fn((cb: () => void) => {
      intervalCalled = true;
      cb();
      return 1;
    }) as any;
    window.clearInterval = vi.fn() as any;

    const result = await vm.getMsgCode();

    expect(getCode).toHaveBeenCalledWith({ username: "user" });
    expect(result).toBe(true);
    expect(intervalCalled).toBe(true);
  });

  it("should handle getMsgCode exception", async () => {
    vm.formModel.username = "user";
    vi.mocked(getCode).mockRejectedValue(new Error("fail"));

    vm.message.error = vi.fn();
    const result = await vm.getMsgCode();
    expect(vm.message.error).toHaveBeenCalledWith("获取验证码失败，请重试");
    expect(result).toBe(false);
  });

  it("findPassword: 跳转找回密码", () => {
    vm.findPassword();
    expect(mockRouter.push).toHaveBeenCalledWith({ name: "FindPassword" });
  });

  it("countLogin: 切换密码登录状态", () => {
    vm.beSelected = true;
    vm.isCount = false;
    vm.countLogin();
    expect(vm.beSelected).toBe(false);
    expect(vm.isCount).toBe(true);
  });
});
