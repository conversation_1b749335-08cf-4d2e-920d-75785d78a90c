<template>
  <Transition name="fade">
    <div
      v-if="config?.mask"
      v-show="visible"
      class="fixed inset-0 z-2000 w-100vw h-100vh bg-#00000066 backdrop-blur-5px z-1999"
    />
  </Transition>

  <Transition name="fade">
    <div
      v-show="visible"
      ref="wrapperRef"
      class="fixed z-2001 flex bg-white border-rd-4 shadow-[0_2px_12px_rgba(0,0,0,0.15)]"
      :style="{
        left: `${position.x}px`,
        top: `${position.y}px`,
      }"
    >
      <div class="relative" :class="size">
        <iframe
          ref="iframeRef"
          class="border-rd-4 size-full"
          frameborder="0"
          allow="microphone"
          allowfullscreen
          :src="chatUrl"
        />
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
  import type { EventNameConstType } from "~/constants/eventConst";

  /*
  * 弹窗初始化
   */
  onMounted(() => {
    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("mouseup", handleMouseUp);
    window.addEventListener("resize", handleResize);
    window.addEventListener("message", handleMessage);
  });

  /*
  * 弹窗销毁
   */
  onUnmounted(() => {
    window.removeEventListener("mousemove", handleMouseMove);
    window.removeEventListener("mouseup", handleMouseUp);
    window.removeEventListener("resize", handleResize);
    window.removeEventListener("message", handleMessage);
  });

  const wrapperRef = useTemplateRef<HTMLElement>("wrapperRef");
  const iframeRef = useTemplateRef<HTMLIFrameElement>("iframeRef");
  const position = ref({ x: 0, y: 0 });
  const visible = ref(false);
  const config = ref<AnyObject | null | undefined>();
  const fullscreen = ref(false);

  const positionShortCuts = {
    /*
     * 弹窗位置
     */
    fullscreen: () => {
      return {
        x: 0,
        y: 0,
      };
    },
    /*
     * 弹窗居中
     */
    center: (rect: DOMRect) => {
      return {
        x: (window.innerWidth - rect.width) / 2,
        y: (window.innerHeight - rect.height) / 2,
      };
    },
    /*
     * 弹窗右下角
     */
    right: (rect: DOMRect) => {
      return {
        x: window.innerWidth - rect.width - 16,
        y: window.innerHeight - rect.height - 16,
      };
    },
  };

  /*
   * 弹窗大小
   */
  const size = computed(() => {
    const sizeShortCuts = {
      fullscreen: "w-100vw h-100vh",
      large: "w-1000px h-750px",
      small: "w-550px h-650px",
    };
    return isFullscreen() ? sizeShortCuts.fullscreen : sizeShortCuts[config.value?.size as keyof typeof sizeShortCuts] || sizeShortCuts.small;
  });

  /*
   * 弹窗可见性
   */
  onClickOutside(wrapperRef, hide);

  // 显示弹窗
  function show(options: AnyObject | null | undefined) {
    if (visible.value) {
      return;
    }

    config.value = options;
    visible.value = true;

    nextTick(handleResize);
  }

  // 关闭弹窗
  function hide() {
    if (!visible.value) {
      return;
    }
    visible.value = false;
  }

  // 弹窗是否可见
  function isVisible() {
    return visible.value;
  }

  /*
   * 弹窗拖拽
   */
  const isDragging = ref(false);
  const startPos = ref({ x: 0, y: 0 });
  const currentPos = ref({ x: 0, y: 0 });

  // 拖拽开始
  function startDrag(e: MouseEvent) {
    isDragging.value = true;
    startPos.value = {
      x: e.clientX - position.value.x,
      y: e.clientY - position.value.y,
    };
  }
  // 拖拽移动
  function handleMouseMove(e: MouseEvent) {
    if (!isDragging.value || !wrapperRef.value) {
      return;
    }

    const rect = wrapperRef.value.getBoundingClientRect();
    const newX = e.clientX - startPos.value.x;
    const newY = e.clientY - startPos.value.y;

    // 计算边界
    const maxX = window.innerWidth - rect.width;
    const maxY = window.innerHeight - rect.height;

    // 限制在视口范围内
    currentPos.value = {
      x: Math.min(Math.max(0, newX), maxX),
      y: Math.min(Math.max(0, newY), maxY),
    };

    position.value = currentPos.value;
  }

  // 拖拽结束
  function handleMouseUp() {
    isDragging.value = false;
  }

  /*
   * 弹窗全屏：视口全屏
   */
  function isFullscreen() {
    return !!fullscreen.value;
  }

  // 切换全屏
  function handleFullScreen() {
    fullscreen.value = !isFullscreen();
    nextTick(() => {
      handleResize();
      sendMessage(EventNameConst["CHAT:FULLSCREEN"], fullscreen.value);
    });
  }

  /*
   * 弹窗大小变化
   */
  function handleResize() {
    if (wrapperRef.value) {
      if (isFullscreen()) {
        position.value = positionShortCuts.fullscreen();
      } else {
        const rect = wrapperRef.value.getBoundingClientRect();
        const xy = positionShortCuts[config.value?.position as string]?.(rect);
        position.value = xy || positionShortCuts.center(rect);
      }
    }
  }

  /*
   * 弹窗消息处理
   */
  function handleMessage(e: MessageEvent) {
    const { event } = e.data;
    switch (event) {
      case EventNameConst["CHAT:FULLSCREEN"]: {
        handleFullScreen();
        break;
      }
      case EventNameConst["CHAT:CLOSE"]: {
        hide();
        break;
      }
    }
  }

  /*
   * 弹窗消息发送
   */
  function sendMessage(event: EventNameConstType, params: any) {
    const message = { event, params };
    iframeRef.value?.contentWindow?.postMessage?.(message, "*");
  }

  // -------------------------------------------------------------------------------------------------------------------

  const chatUrl = computed(() => {
    // ⚠️ee_token 是由后端设置的，需要由后端将本 cookie 的 http-only 属性改为 false，否则前端将无法读取到 cookie
    // http-only 属性使得前端无法获取 cookie，有助于防范跨站脚本攻击，目前先由后端改为 false 跑通整个流程
    const cookies = useCookies(["ee_token"]);
    const ee_token = ref<string>(cookies.get("ee_token"));

    const fallbackParams = {
      ee_token: ee_token.value,
    };
    console.warn("小翼同学参数", config.value?.params);
    const urlParams = Object.assign({}, config.value?.params || {}, fallbackParams);
    const searchParams = new URLSearchParams(urlParams);
    const queryString = searchParams.toString();
    return `${import.meta.env.VITE_CHAT_URL}?${queryString}`;
  });

  // 切换聊天目标
  function changeTarget(params) {
    sendMessage(EventNameConst["CHAT:CHAT_TARGET_CHANGED"], params);
  }

  defineExpose({
    show,
    hide,
    isVisible,
    changeTarget,
  });
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
