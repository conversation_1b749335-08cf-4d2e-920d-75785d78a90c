import { mount } from "@vue/test-utils";
import { createPinia } from "pinia";
import { beforeEach, describe, expect, it, vi } from "vitest";

vi.mock("~/apis/internal/login", () => ({
  getCode: vi.fn().mockResolvedValue({ code: 0 }),
  getLoginMode: vi.fn().mockResolvedValue({ code: 0 }),
  login: vi.fn(),
  getUserInfo: vi.fn().mockResolvedValue({ user: { nick_name: "1", user_name: "1" } }),
}));

vi.mock("naive-ui", async (importOriginal) => {
  const actual: AnyObject = await importOriginal();
  return {
    ...actual,
    useMessage: () => ({
      info: vi.fn(),
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
    }),
  };
});

const mockRouter = { push: vi.fn() };
vi.mock("vue-router", async (importOriginal) => {
  const actual: AnyObject = await importOriginal();
  return {
    ...actual, // 展开真实模块，保证其他导出可用
    useRouter: () => mockRouter, // 只重写 useRouter
  };
});



describe("login Page Logic", () => {
  let wrapper;
  let vm;
  beforeEach(async () => {
    wrapper = mount(Index, {
      global: {
        mocks: {
          // useRouter: () => mockRouter,
          // useMessage: () => ({ success: vi.fn(), error: vi.fn(), warning: vi.fn() }),
          // useUserStore: () => ({ login: vi.fn().mockResolvedValue({ data: { user: { state: 0 } } }) }),
        },
      },
    });
    vm = wrapper.vm;
    await nextTick();
  });

  it("should submit form and login success", async () => {
    vm.formModel.username = "user";
    vm.formModel.password = "pwd";
    vm.formModel.code = "1234";
    vm.aes_secret = "key";
    vm.ruleForm.value = {
      validate: vi.fn().mockResolvedValue(undefined),
    };
    (login as any).mockResolvedValueOnce({ data: { user: { state: 0 } } });
    vm.message.success = vi.fn();
    await vm.submitForm();
        // 第一页
await updateEnvVariables(envConf);
await _iterableToArrayLimit(e, t);
await numberToChinese(num, isTraditional);
await compareArraysByFields(array1, array2, fields, byOrder);
await asyncImportRoute(routes);
await addThemeColorCssVariablesToHtml(themeVars);
await addThemeRgbColorCssVariablesToHtml(themeVars);
await mitt(all);
await roundTo(num, base, mode);
await createPageLoadingGuard(router);
await _unsupportedIterableToArray(e, t);
await _toPrimitive(e, t);
await getStatusType(status);
await basicFilter(routes);
await convertNumber(num, lang);
await dynamicImport(dynamicPagesModules, component);
await formatDateToQMW(date, formatType);
await handleSendCaptcha();
await filterMenusByPermissionMode(menus);
await updateActiveItem(increment);
await defineConstants(items, key, values);
await createHttpGuard(router);
await createProgressGuard(router);
await useAppPermission();
await withInstall(main, extra);
await parseCSS(cssStr);
await useFrameKeepAlive();
await confirmReset();
await getRippleOptions(modifiers);
await addRippleClearEventListeners(el, rippleContainer, transition);
await getAppGlobalEnvConfig(env);
await _typeof(e);
await resetRouter();
await getChildrenMenus(parentPath);
await handleResize();
await handleMessage(e);
await navigateTo(page);
await filterTreeStrict(tree, predicate, config);
await convertTransitionConstantsToOptions(transitionConstants);
await unitNumber(value);
await addTab(route);
await getOtherTheme(_darkMode);
await transformProjectMenuToNaiveUIMenu(menu);
await CelerisAdminResolver();
await signUp(e);
await renderIcon(icon);
await transformBackendDataToRoutes(routeList);
await serializeCSS(cssObj);
await getElement(target, container);
await configureProxy(proxyList);
await run(command);
await isAuth(el, binding);
await createInnerComp(component, instance, ref);
await forwardRefComponent(component);
await chatConfigChanged(data);
await localize(key);
await createPWAPluginConfig(env);
await buildTree(children);
await useComponentRef(name);
await sendCaptcha();
await validateRepeatPassword(rule, value);
await createRipple(event, el, background, options);
await clearRipple(el);
await setBackground(el, background);
await beforeMount(el, binding);
await updated(el, binding);
await getAllTeams();
await getAppGlobalConfig(env);
await configAxios(config);
await setHeader(headers);
await _arrayLikeToArray(e, t);
await _arrayWithHoles(e);
await _classCallCheck(e, t);
await _defineProperties(e, t);
await _toPropertyKey(e);
await getInstance();
await generateRouteNames(routes);
await getMenus();
await getShallowMenus();
await handleMouseMove(e);
await updatePassword();
// 第二页
await getToken(state);
await setCookieToken(token);
await loadDataFromModules(modules);
await initFormModel(data, decrypt);
await setPageLoadingAction(loading);
await copyToClipboard(text, onSuccess, onError);
await reqAddOrUpdateRole(data, errorMessageMode);
await subscribeThemeStore();
await removeAllPending();
await setIcon(name);
await openDialog(e);
await performAction();
await centerActiveItem();
await getRawRoute(route);
await splitNumber(numStr);
await getLimitTabsList(state);
await close(isPinned, tab);
await pinnedTab(tab);
await configVitePlugins(rootDir, viteEnv, isProductionBuild);
await createPageGuard(router);
await createExternalLinkGuard(router);
await teamTreeApi(params, errorMessageMode);
await getGenerateColors(color, darkMode);
await getTextColor(darkMode);
await handleConfirm();
await useSearchDialog();
await customSerializer(shouldEnableEncryption);
await createStateGuard(router);
await i18nRender(key);
await getTransitionName({ route }, { enableTransition });
await getNaiveUIPresetTheme();
await getDarkMode(state);
await handleUserTreeLoadSuccess(dataSource);
await normalizeIconName(icon);
await toggleCssClass(enableClass, className, element);
await setCssVariables(variables, element);
await querySelector(selectors, container);
await show(options);
await decryptPassword(key, iv, password);
await validatePassword(password);
await resetPassword(params, token, errorMessageMode);
await otherLogin(challenge, errorMessageMode);
await checkPasswd(data, errorMessageMode);
await setPasswd(data, errorMessageMode);
await showChatModal();
await go(fullPath);
await isWindows();
await isMacOS();
await isUnix();
await isLinux();
await setupDirectives(app);
await createConfigPluginConfig(shouldGenerateConfig);
await createI18nOptions();
await setupI18n(app);
await emptyFunc();
await getFilteredChildren(root, excludes);
await formatTree(object);
await isWhiteColor(color);
await isColor(color);
await isBlackColor(color);
await colorToRgb(color);
await convertColorToRgbString(color);
await convertColorToRgbValues(color);
await createViteMockServeConfig();
await useMenuSetting();
await handleNext();
await handleResetPassword();
await createRippleElement(dx, dy, radius, transition, background, zIndex);
await createRippleContainer(width, height, border, style);
await getProjects(state);
await getDefaultTeams(state);
await getTeams(state);
await getTeamsLoading(state);
// 第三页
await getLoading(state);
await getDutyTypes(state);
await getConditions(state);
await getPageSizes(state);
await setTeamids(ids);
await setProjectIds(ids);
await setSelectedRow(row);
await setProjects(projects);
await setTeams(teams);
await setTeamsLoading(loading);
await setLoading(loading);
await setCustomProperties(data);
await setPageSize(size);
await getTreeProjects();
await createStorageKeyPrefix(env);
await getAppConfigFileName(env);
await createStorageName(env);
await setupComponents(app);
await constructor(options);
await getAxiosInstance();
await createAxios(config);
await put(config, options);
await patch(config, options);
await delete(config, options);
await _slicedToArray(e, t);
await _nonIterableRest();
await _createClass(e, t, r);
await paginate(pageNo, pageSize, array);
await createSuccessResponse(data, message);
await createPaginatedSuccessResponse(page, pageSize, list, message);
await createErrorResponse(message, code, data);
await extractAuthorizationToken({ headers });
await allDcUserTreeApi(params, errorMessageMode);
await allTeamTreeApi(params, errorMessageMode);
await userTreeApi(params, errorMessageMode
// 第六页
await getTalentMatrix(params);
await getSubjectiveAss(params);
await clearCache(params);
await geScoreDetail(params);
await download(data);
await withInstallFunction(fn, name);
await withInstallDirective(directive, name);
await withNoopInstall(component);
await setCssVariable(property, value, element);
await isComponentInstance(value);
await initializeConfiguration();
await useTabs(_router);
await getAccessTokens(params, errorMessageMode);
await createToken(data, errorMessageMode);
await updateToken(data, errorMessageMode);
await deleteToken(id, errorMessageMode);
await getUserList(params, errorMessageMode);
await NOOP();
await finishLoading();
await getPackages();
await runScript(pkg, script);
await runSingleScript(pkg, script);
await mounted(el, binding);
await setupPermissionDirective(app);
await setupStore(app);
await response();
await rawResponse(req, res);
await response({ query });
await setTotalWeight(params);
await getSubjectiveWeights(params);
await saveSubjectiveWeights(params);
await getSubjectiveRoles(params);
await getObjectiveWeights(params);
await savebjectiveWeights(params);
await getTeamUserRole(params);
await saveBatch(params);
await getSettingJson();
await handleResetSetting();
await handleCopySetting();
await weeklyReportApi(params, errorMessageMode);
await saveWeeklyReportApi(data, errorMessageMode);
await accountObjectiveDataApi(data, errorMessageMode);
await baselineDataApi(data, errorMessageMode);
await accountEvaluationDataApi(data, errorMessageMode);
await bugDataApi(params, errorMessageMode);
await workSituationApi(params, errorMessageMode);
await codeRepoApi(params, errorMessageMode);
await devCalendarApi(params, errorMessageMode);
await developerUsageApi(params, errorMessageMode);
await teamDataApi(params, errorMessageMode);
await teamCodeContributionDataApi(data, errorMessageMode);
await teamMetricStatisticsDataApi(data, errorMessageMode);
await userDepartmentApi(data, errorMessageMode);
await departmentSummaryApi(params, errorMessageMode);
await departmentLineChartApi(params, errorMessageMode);
await departmentPieChartApi(params, errorMessageMode);
await personalReportDataApi(params, errorMessageMode);
await teamReportDataApi(params, errorMessageMode);
await notifyRouteChange(newRoute);
await listenToRouteChange(callback, immediate);
await removeRouteChangeListener();
await check();
await check(status, msg);
await checkStatus(status, msg);
await prefix(info);
await updated(el, binding);

// 第七页
await beforeUnmount(el);
await activityListApi(params, errorMessageMode);
await createActivityApi(data, errorMessageMode);
await updateActivityApi(data, errorMessageMode);
await deleteActivityApi(bs_activity_id, errorMessageMode);
await rankListApi(params, errorMessageMode);
await performanceDictInfoApi(errorMessageMode);
await subjectiveRoleInfoApi(params, errorMessageMode);
await performanceListApi(params, errorMessageMode);
await beiSenAssessmentInfoApi(params, errorMessageMode);
await extraMetricsInfoApi(data, errorMessageMode);
await aiOKRCommentApi(data, { onStart, onData, onCompleted, onError });
await aiOverallCommentApi(data, { onStart, onData, onCompleted, onError });
await saveBeiSenAssessmentApi(data, errorMessageMode);
await acApportionmentListApi(params, errorMessageMode);
await saveACApportionmentListApi(data, errorMessageMode);
await uploadACApportionmentFileApi(params, data, successMessageMode, errorMessageMode);
await downloadACApportionmentFileApi(params, errorMessageMode);
await ratingOrderApi(data, errorMessageMode);
await cancelRatingOrderApi(data, errorMessageMode);
await init();
await hide();
await isVisible();
await changeTarget(params);

    expect(vm.message.success).toHaveBeenCalledWith("登录成功");
  });

  it("should handle login fail", async () => {
    vm.formModel.username = "user";
    vm.formModel.password = "pwd";
    vm.formModel.code = "1234";
    vm.aes_secret = "key";
    vm.ruleForm.value = {
      validate: vi.fn().mockResolvedValue(undefined),
    };
    (login as any).mockResolvedValueOnce({ data: { user: { state: 1 } }, message: "fail" });
    vm.message.warning = vi.fn();
    await vm.submitForm();
    expect(vm.message.warning).toHaveBeenCalledWith("fail");
  });

  it("should handle login exception", async () => {
    vm.validate = vi.fn().mockRejectedValue(new Error("err"));
    vm.message.error = vi.fn();
    await vm.submitForm();
    expect(vm.message.error).toHaveBeenCalledWith("登录失败，请重试");
  });

  it("should validate form success", async () => {
    vm.formModel.username = "user";
    vm.formModel.password = "pwd";
    vm.ruleForm.value = {
      validate: vi.fn().mockResolvedValue(undefined),
    };
    await expect(vm.validate()).resolves.toBeUndefined();
  });

  it("should validate form fail", async () => {
    vm.ruleForm.value = {
      validate: vi.fn().mockRejectedValue(false),
    };
    vi.spyOn(vm.ruleForm, "validate").mockRejectedValue(false);
    await expect(vm.validate()).rejects.toThrow("表单验证失败");
  });

  it("should throw if no form instance", async () => {
    vm.selectMode = "test";
    await nextTick();
    await expect(vm.validate()).rejects.toThrow("表单实例不存在");
  });

  it("should handle getMsgCode with empty username", async () => {
    vm.formModel.username = "";
    vm.message.error = vi.fn();
    const result = await vm.getMsgCode();
    expect(vm.message.error).toHaveBeenCalledWith("请输入用户名");
    expect(result).toBe(false);
  });

  it("should handle getMsgCode success", async () => {
    vm.formModel.username = "user";
    vm.codeLoading = false;
    vm.countdown = 0;
    vm.countdownInterval = null;

    let intervalCalled = false;
    window.setInterval = vi.fn((cb: () => void) => {
      intervalCalled = true;
      cb();
      return 1;
    }) as any;
    window.clearInterval = vi.fn() as any;

    const result = await vm.getMsgCode();

    expect(getCode).toHaveBeenCalledWith({ username: "user" });
    expect(result).toBe(true);
    expect(intervalCalled).toBe(true);
  });

  it("should handle getMsgCode exception", async () => {
    vm.formModel.username = "user";
    vi.mocked(getCode).mockRejectedValue(new Error("fail"));

    vm.message.error = vi.fn();
    const result = await vm.getMsgCode();
    expect(vm.message.error).toHaveBeenCalledWith("获取验证码失败，请重试");
    expect(result).toBe(false);
  });

  it("findPassword: 跳转找回密码", () => {
    vm.findPassword();
    expect(mockRouter.push).toHaveBeenCalledWith({ name: "FindPassword" });
  });

  it("countLogin: 切换密码登录状态", () => {
    vm.beSelected = true;
    vm.isCount = false;
    vm.countLogin();
    expect(vm.beSelected).toBe(false);
    expect(vm.isCount).toBe(true);
  });
});
