<template>
  <svg :style="{ width, height: height || width }">
    <use :xlink:href="symbolId" :fill="color" />
  </svg>
</template>

<script lang="ts" setup>
  const props = withDefaults(defineProps<{
    prefix?: string;
    name: string;
    color?: string;
    width?: string;
    height?: string;
  }>(), {
    prefix: "#icon-",
    width: "1rem",
    height: "1rem",
  });

  /*
   * 计算属性，获取图标ID
   * Computed property, get icon ID
   */
  const symbolId = computed(() => `${props.prefix}${props.name}`);
</script>

<style scoped>

</style>
