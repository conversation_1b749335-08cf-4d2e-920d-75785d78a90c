import type { App } from "vue";
import type { I18nOptions } from "vue-i18n";
import { deepMerge } from "@celeris/utils";
import { createI18n } from "vue-i18n";
import { LocalesConfiguration } from "./config";

// eslint-disable-next-line import/no-mutable-exports
export let i18n: ReturnType<typeof createI18n>;

/**
 * 创建国际化选项
 * Create i18n options
 * @returns 国际化选项
 * I18n options
 */
async function createI18nOptions(): Promise<I18nOptions> {
  return deepMerge({
    legacy: false,
    locale: LocalesConfiguration.locale,
    fallbackLocale: LocalesConfiguration.fallbackLocale,
    messages: await LocalesConfiguration.messagesHandler(),
  }, LocalesConfiguration.otherOptions);
}

/**
 * 设置国际化
 * Set up i18n
 * @param app - Vue应用实例
 * Vue application instance
 */
export async function setupI18n(app: App) {
  const options = await createI18nOptions();
  i18n = createI18n(options);
  app.use(i18n);
}
