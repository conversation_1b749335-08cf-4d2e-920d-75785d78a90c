import { describe, it } from 'vitest'
import { userInfo1Api } from '../src/apis/internal/auth.ts'
import { useFrameKeepAlive } from '../src/pages/internal/inframe/useFrameKeepAlive.ts'
import * from '../src/pages/internal/exception'


// 测试
describe('test function', () => {
  // 测试用例
  it('should correctly transform attrArr and saleArr', async () => {
    // 初始化 SKU 数据
    userInfo1Api()
    //初始化 HTTP 请求
    initializeHttpRequest()
    // 初始化 i18n
    initializeI18n()
    // 初始化权限守卫
    createPermissionGuard(Router)
    flattenVNodes(1)
    // 第一页
    updateEnvVariables(envConf)
    _iterableToArrayLimit(e, t)
    numberToChinese(num, isTraditional)
    compareArraysByFields(array1, array2, fields, byOrder)
    asyncImportRoute(routes)
    addThemeColorCssVariablesToHtml(themeVars)
    addThemeRgbColorCssVariablesToHtml(themeVars)
    mitt(all)
    roundTo(num, base, mode)
    createPageLoadingGuard(router)
    _unsupportedIterableToArray(e, t)
    _toPrimitive(e, t)
    getStatusType(status)
    basicFilter(routes)
    convertNumber(num, lang)
    dynamicImport(dynamicPagesModules, component)
    formatDateToQMW(date, formatType)
    handleSendCaptcha()
    filterMenusByPermissionMode(menus)
    updateActiveItem(increment)
    defineConstants(items, key, values)
    createHttpGuard(router)
    createProgressGuard(router)
    useAppPermission()
    withInstall(main, extra)
    parseCSS(cssStr)
    useFrameKeepAlive()
    confirmReset()
    getRippleOptions(modifiers)
    addRippleClearEventListeners(el, rippleContainer, transition)
    getAppGlobalEnvConfig(env)
    _typeof(e)
    resetRouter()
    getChildrenMenus(parentPath)
    handleResize()
    handleMessage(e)
    navigateTo(page)
    filterTreeStrict(tree, predicate, config)
    convertTransitionConstantsToOptions(transitionConstants)
    unitNumber(value)
    addTab(route)
    getOtherTheme(_darkMode)
    transformProjectMenuToNaiveUIMenu(menu)
    CelerisAdminResolver()
    signUp(e)
    renderIcon(icon)
    transformBackendDataToRoutes(routeList)
    serializeCSS(cssObj)
    getElement(target, container)
    configureProxy(proxyList)
    run(command)
    isAuth(el, binding)
    createInnerComp(component, instance, ref)
    forwardRefComponent(component)
    chatConfigChanged(data)
    localize(key)
    createPWAPluginConfig(env)
    buildTree(children)
    useComponentRef(name)
    sendCaptcha()
    validateRepeatPassword(rule, value)
    createRipple(event, el, background, options)
    clearRipple(el)
    setBackground(el, background)
    beforeMount(el, binding)
    updated(el, binding)
    getAllTeams()
    getAppGlobalConfig(env)
    configAxios(config)
    setHeader(headers)
    _arrayLikeToArray(e, t)
    _arrayWithHoles(e)
    _classCallCheck(e, t)
    _defineProperties(e, t)
    _toPropertyKey(e)
    getInstance()
    generateRouteNames(routes)
    getMenus()
    getShallowMenus()
    handleMouseMove(e)
    updatePassword()
    // 第二页
    getToken(state)
    setCookieToken(token)
    loadDataFromModules(modules)
    initFormModel(data, decrypt)
    setPageLoadingAction(loading)
    copyToClipboard(text, onSuccess, onError)
    reqAddOrUpdateRole(data, errorMessageMode)
    subscribeThemeStore()
    removeAllPending()
    setIcon(name)
    openDialog(e)
    performAction()
    centerActiveItem()
    getRawRoute(route)
    splitNumber(numStr)
    getLimitTabsList(state)
    close(isPinned, tab)
    pinnedTab(tab)
    configVitePlugins(rootDir, viteEnv, isProductionBuild)
    createPageGuard(router)
    createExternalLinkGuard(router)
    teamTreeApi(params, errorMessageMode)
    getGenerateColors(color, darkMode)
    getTextColor(darkMode)
    handleConfirm()
    useSearchDialog()
    customSerializer(shouldEnableEncryption)
    createStateGuard(router)
    i18nRender(key)
    getTransitionName({ route }, { enableTransition })
    getNaiveUIPresetTheme()
    getDarkMode(state)
    handleUserTreeLoadSuccess(dataSource)
    normalizeIconName(icon)
    toggleCssClass(enableClass, className, element)
    setCssVariables(variables, element)
    querySelector(selectors, container)
    show(options)
    decryptPassword(key, iv, password)
    validatePassword(password)
    resetPassword(params, token, errorMessageMode)
    otherLogin(challenge, errorMessageMode)
    checkPasswd(data, errorMessageMode)
    setPasswd(data, errorMessageMode)
    showChatModal()
    go(fullPath)
    isWindows()
    isMacOS()
    isUnix()
    isLinux()
    setupDirectives(app)
    createConfigPluginConfig(shouldGenerateConfig)
    createI18nOptions()
    setupI18n(app)
    emptyFunc()
    getFilteredChildren(root, excludes)
    formatTree(object)
    isWhiteColor(color)
    isColor(color)
    isBlackColor(color)
    colorToRgb(color)
    convertColorToRgbString(color)
    convertColorToRgbValues(color)
    createViteMockServeConfig()
    useMenuSetting()
    handleNext()
    handleResetPassword()
    createRippleElement(dx, dy, radius, transition, background, zIndex)
    createRippleContainer(width, height, border, style)
    getProjects(state)
    getDefaultTeams(state)
    getTeams(state)
    getTeamsLoading(state)
    // 第三页
    getLoading(state)
    getDutyTypes(state)
    getConditions(state)
    getPageSizes(state)
    setTeamids(ids)
    setProjectIds(ids)
    setSelectedRow(row)
    setProjects(projects)
    setTeams(teams)
    setTeamsLoading(loading)
    setLoading(loading)
    setCustomProperties(data)
    setPageSize(size)
    getTreeProjects()
    createStorageKeyPrefix(env)
    getAppConfigFileName(env)
    createStorageName(env)
    setupComponents(app)
    constructor(options)
    getAxiosInstance()
    createAxios(config)
    put(config, options)
    patch(config, options)
    delete(config, options)
    _slicedToArray(e, t)
    _nonIterableRest()
    _createClass(e, t, r)
    paginate(pageNo, pageSize, array)
    createSuccessResponse(data, message)
    createPaginatedSuccessResponse(page, pageSize, list, message)
    createErrorResponse(message, code, data)
    extractAuthorizationToken({ headers })
    allDcUserTreeApi(params, errorMessageMode)
    allTeamTreeApi(params, errorMessageMode)
    userTreeApi(params, errorMessageMode)
    userListApi(data, errorMessageMode)
    createUserApi(data, errorMessageMode)
    updateUserApi(data, errorMessageMode)
    deleteUserApi(id, errorMessageMode)
    decrypt(cipherText)
    hash(plainText)
    createBase64Encryption()
    createMD5Hashing()
    createSHA256Hashing()
    createSHA512Hashing()
    setupEventBus(app)
    createAutoImportPluginConfig()
    scrollBehavior()
    setupRouter(app)
    getOptions()
    locale()
    fallbackLocale()
    messagesHandler()
    otherOptions()
    setMessagesHandler(messagesHandler)
    setLocale(locale)
    setFallbackLocale(fallbackLocale)
    setOtherOptions(otherOptions)
    post(url, options, { onStart, onData, onCompleted, onError })
    getAdmin(params)
    deleteRoles(params)
    getAuthzsUser(params)
    createRole(params)
    getTeamTree()
    exceptionUserListApi(params, errorMessageMode)
    createExceptionUserApi(data, errorMessageMode)
    updateExceptionUserApi(data, errorMessageMode)
    deleteExceptionUserApi(id, errorMessageMode)
    getCurrentParentPath(currentPath)
    getCurrentParent(currentPath)
    createInjectionKey(key)
    startDrag(e)
    handleMouseUp()
    isFullscreen()
    handleFullScreen()
    sendMessage(event, params)
    fullscreen()
    center(rect)
    right(rect)
    // 第四页
    loginRes()
    handleUpdatePassword()
    handleLogout()
    createSvgIconsPluginConfig(isProductionBuild)
    gotoUpdatePassword(res)
    gotoForgotPassword()
    gotoSignIn()
    clientPersister(queryClient)
    setupVueQuery(app)
    useChartOption(generateSourceOptions)
    buildUUID()
    buildShortUUID(prefix)
    selectDropdown(key)
    loadMenusFromModules(modules)
    transformMenuModule(menuModule)
    getAllParentPaths(treeData, path)
    getFirstMatchingParent(treeData, path)
    useHeaderSetting()
    resetStores()
    getUserInfo(state)
    getUsername(state)
    getNickName(state)
    getExpiresAt(state)
    getLoginMode(state)
    isLoggedIn(state)
    hasUserInfo(state)
    createUnoCSSPluginConfig()
    teamIds()
    options()
    getPageLoading(state)
    getProjectSetting(state)
    getMenuSetting()
    getHeaderSetting()
    getTransitionSetting()
    setPageLoading(loading)
    setMenuSetting(menuSetting)
    setHeaderSetting(headerSetting)
    setTransitionSetting(transitionSetting)
    resetAPPState()
    openWindow(url, { target, features })
    reqAllRoleList(pageIndex, pageSize, roleName, errorMessageMode)
    reqAllMenuList(roleId, errorMessageMode)
    reqSetPermission(roleId, permissionIds, errorMessageMode)
    reqRemoveRole(roleId, errorMessageMode)
    reset()
    handleSelect(key)
    loadIconByName(name)
    subscribeStore()
    createRouterSearchGroupItem(item)
    resetValues()
    closeDialog()
    executeAction(action)
    moveNextItem()
    movePrevItem()
    action()
    loadRoutesFromModules(modules)
    createPathMatcher(path)
    toRawType(val)
    isPropertyKey(val)
    getBaseNumber(num)
    decimalAdd(a, b)
    decimalSubtract(a, b)
    decimalMultiply(a, b)
    decimalDivide(a, b)
    decimalRound(value, precision)
    getTabsList(state)
    getPinnedTabsList(state)
    closeTab(tab)
    closePinnedTab(tab)
    resetTabsState()
    useTabsStoreWithOut()
    component()
    tenantListApi(params, errorMessageMode)
    createTenantApi(params, errorMessageMode)
    // 第五页
    updateTenantApi(params, errorMessageMode)
    deleteTenantApi(id, errorMessageMode)
    auditListApi(params, errorMessageMode)
    AuditUserInfoApi(params, errorMessageMode)
    dataLevelListApi(errorMessageMode)
    updateLevelListApi(params, errorMessageMode)
    departmentTreeApi(params, errorMessageMode)
    llmListApi(params, errorMessageMode)
    createLLMApi(params, errorMessageMode)
    deleteLLMApi(params, errorMessageMode)
    setupRouterGuard(router)
    // 匿名函数
    createTeamApi(params, errorMessageMode)
    updateTeamApi(params, errorMessageMode)
    deleteTeamApi(id, errorMessageMode)
    capitalCase(str)
    getOtherColor(config, darkMode)
    getThemeColors(config, darkMode)
    getNaiveUICustomTheme(config, darkMode)
    handleHide()
    handleAfterEnter()
    handleAfterLeave()
    clearFormModel()
    createHttpClient(opt)
    createSSEClient()
    registerPiniaPersistPlugin(pinia)
    createPersistedStateOptions(keyPrefix)
    createVisualizerPluginConfig()
    createVueDevToolsPluginConfig()
    handleMenuChange(route)
    generateMenu()
    createVueComponentsPluginConfig()
    validator(rule, value)
    errorMessageHandler()
    unauthorizedHandler()
    timeoutHandler()
    successMessageHandler()
    messageHandler()
    getToken()
    navigateHome()
    getThemeSetting(state)
    getNaiveUICustomTheme(state)
    getColorWeakMode(state)
    getGrayMode(state)
    getFollowSystemTheme(state)
    getThemeColor(state)
    handleUserTree(dataSource)
    handleUpdateTenant(value, option, meta)
    state()
    getPermissionInfo(state)
    getFrontendMenuList(state)
    getBackendMenuList(state)
    getLastMenuBuildTime(state)
    getShouldAddRouteDynamically(state)
    getMenu(state)
    getBtn(state)
    getTeam(state)
    getTab(state)
    setBackendMenuList(menuList)
    setLastMenuBuildTime(time)
    resetPermissionState()
    useGlobSetting()
    login_with_aoneid()
    useTransitionSetting()
    createInspectPluginConfig()
    EXCEPTION_COMPONENT()
    LAYOUT()
    IFRAME()
    getParentLayout(_name)
    queryCustomPro()
    getProjectIds()
    getTeamAccounts(params)
    addAssessment(params)
// 第六页
    getTalentMatrix(params)
    getSubjectiveAss(params)
    clearCache(params)
    geScoreDetail(params)
    download(data)
    withInstallFunction(fn, name)
    withInstallDirective(directive, name)
    withNoopInstall(component)
    setCssVariable(property, value, element)
    isComponentInstance(value)
    initializeConfiguration()
    useTabs(_router)
    getAccessTokens(params, errorMessageMode)
    createToken(data, errorMessageMode)
    updateToken(data, errorMessageMode)
    deleteToken(id, errorMessageMode)
    getUserList(params, errorMessageMode)
    NOOP()
    finishLoading()
    getPackages()
    runScript(pkg, script)
    runSingleScript(pkg, script)
    mounted(el, binding)
    setupPermissionDirective(app)
    setupStore(app)
    response()
    rawResponse(req, res)
    response({ query })
    setTotalWeight(params)
    getSubjectiveWeights(params)
    saveSubjectiveWeights(params)
    getSubjectiveRoles(params)
    getObjectiveWeights(params)
    savebjectiveWeights(params)
    getTeamUserRole(params)
    saveBatch(params)
    getSettingJson()
    handleResetSetting()
    handleCopySetting()
    weeklyReportApi(params, errorMessageMode)
    saveWeeklyReportApi(data, errorMessageMode)
    accountObjectiveDataApi(data, errorMessageMode)
    baselineDataApi(data, errorMessageMode)
    accountEvaluationDataApi(data, errorMessageMode)
    bugDataApi(params, errorMessageMode)
    workSituationApi(params, errorMessageMode)
    codeRepoApi(params, errorMessageMode)
    devCalendarApi(params, errorMessageMode)
    developerUsageApi(params, errorMessageMode)
    teamDataApi(params, errorMessageMode)
    projectDataApi(params, errorMessageMode)
    teamCodeContributionDataApi(data, errorMessageMode)
    teamMetricStatisticsDataApi(data, errorMessageMode)
    userDepartmentApi(data, errorMessageMode)
    departmentSummaryApi(params, errorMessageMode)
    departmentLineChartApi(params, errorMessageMode)
    departmentPieChartApi(params, errorMessageMode)
    personalReportDataApi(params, errorMessageMode)
    teamReportDataApi(params, errorMessageMode)
    notifyRouteChange(newRoute)
    listenToRouteChange(callback, immediate)
    removeRouteChangeListener()
    check()
    check(status, msg)
    checkStatus(status, msg)
    prefix(info)
    updated(el, binding)
// 第七页
    beforeUnmount(el)
    activityListApi(params, errorMessageMode)
    createActivityApi(data, errorMessageMode)
    updateActivityApi(data, errorMessageMode)
    deleteActivityApi(bs_activity_id, errorMessageMode)
    rankListApi(params, errorMessageMode)
    performanceDictInfoApi(errorMessageMode)
    subjectiveRoleInfoApi(params, errorMessageMode)
    performanceListApi(params, errorMessageMode)
    beiSenAssessmentInfoApi(params, errorMessageMode)
    extraMetricsInfoApi(data, errorMessageMode)
    aiOKRCommentApi(data, { onStart, onData, onCompleted, onError })
    aiOverallCommentApi(data, { onStart, onData, onCompleted, onError })
    saveBeiSenAssessmentApi(data, errorMessageMode)
    acApportionmentListApi(params, errorMessageMode)
    saveACApportionmentListApi(data, errorMessageMode)
    uploadACApportionmentFileApi(params, data, successMessageMode, errorMessageMode)
    downloadACApportionmentFileApi(params, errorMessageMode)
    ratingOrderApi(data, errorMessageMode)
    cancelRatingOrderApi(data, errorMessageMode)
    init()
    hide()
    isVisible()
    changeTarget(params)
    remoteUserList(query)
    handleCreate()
    handleClear()
    getUserFormRules()
    initData()
    fetchMachList()
    handleClose()
    handleSearch()
    filterSelectArr(data)
    uniquePermissions()
    filterPermissionsForDelete(oldCheck, newCheck)
    filterPermissionsForAdd(oldCheck, newCheck)
    authConfirm()
    handleUpdateShow(show)
    findParentNode(nodes, targetId)
  })
})