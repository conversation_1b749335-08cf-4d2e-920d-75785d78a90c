<template>
  <CardModal
    v-bind="$attrs"
    class="w-xl"
    title="关联租户"
    :empty-func="() => false"
    :mask-closable="false"
    @update:show="handleHide"
    @after-enter="handleAfterEnter"
    @after-leave="handleAfterLeave"
  >
    <div class="p-8">
      <NForm
        ref="formRef"
        label-width="80"
        label-placement="left"
        require-mark-placement="left"
        filterable
        :model="formModel"
        :rules="formRules"
      >
        <NFormItem path="tenant" label="租户">
          <NSelect
            v-model:value="formModel.tenant"
            :options="tenantOptions"
            placeholder="请选择租户"
            multiple
          />
        </NFormItem>
      </NForm>
    </div>

    <template #footer>
      <NFlex justify="center">
        <NButton @click="handleHide">
          取消
        </NButton>
        <NButton type="primary" :loading="loading" @click="handleConfirm">
          确定
        </NButton>
      </NFlex>
    </template>
  </CardModal>
</template>

<script lang="ts" setup>
  import type { UserListExpandedItem } from "-/user";
  import type { FormInst, FormRules, SelectOption } from "naive-ui";
  import { createUserApi, updateUserApi } from "~/apis/internal/user";
  import CardModal from "~/component/CardModal/src/CardModal.vue";

  defineOptions({
    name: "RelTenantModal",
    inheritAttrs: false,
  });

  const props = withDefaults(defineProps<{
    data: UserListExpandedItem | null;
    tenantOptions: SelectOption[];
  }>(), {
    data: () => ({}) as UserListExpandedItem,
  });

  const emit = defineEmits<{
    "update:show": [show: boolean];
    "positiveClick": [success: boolean];
  }>();

  const isUpdateUser = computed(() => !!(props.data && props.data.username));

  /**
   * @description 弹窗进入动画后初始化表单数据。
   * 若有传入 data，则初始化为 data，否则清空表单。
   */
  function handleAfterEnter() {
    props.data ? initFormModel() : clearFormModel();
  }

  /**
   * @description 弹窗关闭动画后清空表单数据。
   */
  function handleAfterLeave() {
    clearFormModel();
  }

  /**
   * @description 关闭弹窗，通知父组件更新显示状态。
   */
  function handleHide() {
    emit("update:show", false);
  }

  // 初始表单数据
  const INIT_USER_FORM_MODEL = {
    tenant: "",
  };

  const formRef = useTemplateRef<FormInst>("formRef");
  const formModel = ref({ ...INIT_USER_FORM_MODEL });
  const formRules: FormRules = {
    tenant: [{ required: false, message: "请选择租户", trigger: "input" }],
  };
  const loading = ref(false);

  /**
   * @description 根据 props.data 初始化表单模型。
   */
  function initFormModel() {
    const data = props.data;
    Object.keys(INIT_USER_FORM_MODEL).forEach((key) => {
      formModel.value[key] = data?.[key] || INIT_USER_FORM_MODEL[key];
    });
    // 使用类型断言处理tenant属性
    formModel.value.tenant = (data as any)?.tenant || [];
  }

  /**
   * @description 重置表单模型为初始值，并恢复表单校验状态。
   */
  function clearFormModel() {
    formModel.value = { ...INIT_USER_FORM_MODEL };
    formRef.value?.restoreValidation?.();
  }

  const message = useMessage();

  /**
   * @description 提交表单，进行校验并调用关联租户接口。
   * 校验通过后自动关闭弹窗并通知父组件。
   * @returns {Promise<void>}
   */
  async function handleConfirm() {
    try {
      await formRef.value?.validate();
      const data = {
        username: props.data?.username,
        tenant: formModel.value.tenant,
      };
      loading.value = true;
      isUpdateUser.value ? await updateUserApi(data) : await createUserApi(data);
      handleHide();
      emit("positiveClick", true);
      message.success("关联租户成功");
    } catch (e) {
      console.error(e);
    } finally {
      loading.value = false;
    }
  }

  // Expose functions used in template to fix linting issues
  defineExpose({
    handleHide,
    handleAfterEnter,
    handleAfterLeave,
    handleConfirm,
  });
</script>
