import type { ApprovalInfoExpanded } from "-/tenant";
import { describe, expect, it, vi } from "vitest";

// Mock dependencies
vi.mock("~/apis/internal/tenant", () => ({
  approvelListApi: vi.fn().mockResolvedValue({
    data: [],
    total: 0,
  }),
}));

vi.mock("~/composables/useTable", () => ({
  useTable: () => ({
    tableLoading: { value: false },
    tableData: { value: [] },
    tablePagination: { value: { page: 1, pageSize: 20, itemCount: 0 } },
    loadData: vi.fn(),
    handlePageChange: vi.fn(),
    handlePageSizeChange: vi.fn(),
    handleScroll: vi.fn(),
  }),
}));

vi.mock("naive-ui", () => ({
  useMessage: () => ({ success: vi.fn(), error: vi.fn(), info: vi.fn() }),
  useDialog: () => ({ info: vi.fn(), warning: vi.fn() }),
}));

vi.mock("@celeris/utils", () => ({
  formatToDate: vi.fn(date => date ? "2024-01-01 12:00:00" : "--"),
}));

vi.mock("lodash-es", () => ({
  cloneDeep: vi.fn(obj => JSON.parse(JSON.stringify(obj))),
}));

describe("approvalList Component", () => {
  describe("application Type Mapping", () => {
    it("should correctly map application types", () => {
      const typeMap = {
        create: "创建租户",
        update: "更新租户",
        delete: "删除租户",
        reset: "重置密码",
      };

      expect(typeMap.create).toBe("创建租户");
      expect(typeMap.update).toBe("更新租户");
      expect(typeMap.delete).toBe("删除租户");
      expect(typeMap.reset).toBe("重置密码");
    });
  });

  describe("status Mapping", () => {
    it("should correctly map approval statuses", () => {
      const statusMap = {
        pending: { type: "warning", text: "待审核" },
        approved: { type: "success", text: "已通过" },
        rejected: { type: "error", text: "已拒绝" },
        cancelled: { type: "info", text: "已取消" },
      };

      expect(statusMap.pending.text).toBe("待审核");
      expect(statusMap.pending.type).toBe("warning");
      expect(statusMap.approved.text).toBe("已通过");
      expect(statusMap.approved.type).toBe("success");
      expect(statusMap.rejected.text).toBe("已拒绝");
      expect(statusMap.rejected.type).toBe("error");
      expect(statusMap.cancelled.text).toBe("已取消");
      expect(statusMap.cancelled.type).toBe("info");
    });
  });

  describe("dropdown Options", () => {
    interface DropdownOption {
      label: string;
      key: string;
      props?: {
        style: string;
      };
    }

    const getDropdownOptions = (row: { status: string }): DropdownOption[] => {
      const options: DropdownOption[] = [];

      if (row.status === "pending") {
        options.push({
          label: "取消申请",
          key: "cancel",
          props: {
            style: "color: #d03050;",
          },
        });
      }

      options.push({
        label: "查看详情",
        key: "detail",
      });

      return options;
    };

    it("should generate correct dropdown options for pending status", () => {
      const pendingOptions = getDropdownOptions({ status: "pending" });

      expect(pendingOptions).toHaveLength(2);
      expect(pendingOptions.some(opt => opt.key === "cancel")).toBe(true);
      expect(pendingOptions.some(opt => opt.key === "detail")).toBe(true);

      const cancelOption = pendingOptions.find(opt => opt.key === "cancel");
      expect(cancelOption?.label).toBe("取消申请");
      expect(cancelOption?.props?.style).toBe("color: #d03050;");
    });

    it("should generate correct dropdown options for completed status", () => {
      const approvedOptions = getDropdownOptions({ status: "approved" });

      expect(approvedOptions).toHaveLength(1);
      expect(approvedOptions.some(opt => opt.key === "detail")).toBe(true);
      expect(approvedOptions.some(opt => opt.key === "cancel")).toBe(false);
    });
  });

  describe("data Processing", () => {
    it("should correctly process approval data", () => {
      const mockData: ApprovalInfoExpanded[] = [
        {
          id: "1",
          applicant: "张三",
          tenant_name: "测试租户1",
          application_type: "create",
          status: "pending",
          apply_time: "2024-01-01T00:00:00Z",
          description: "申请创建新租户",
          key: "1",
          approveLoading: false,
          rejectLoading: false,
        },
        {
          id: "2",
          applicant: "李四",
          tenant_name: "测试租户2",
          application_type: "update",
          status: "approved",
          apply_time: "2024-01-02T00:00:00Z",
          approve_time: "2024-01-03T00:00:00Z",
          approver: "管理员",
          key: "2",
          approveLoading: false,
          rejectLoading: false,
        },
      ];

      expect(mockData).toHaveLength(2);
      expect(mockData[0].applicant).toBe("张三");
      expect(mockData[0].application_type).toBe("create");
      expect(mockData[0].status).toBe("pending");
      expect(mockData[1].applicant).toBe("李四");
      expect(mockData[1].application_type).toBe("update");
      expect(mockData[1].status).toBe("approved");
    });
  });

  describe("query Criteria", () => {
    it("should handle query criteria correctly", () => {
      const queryCriteria = {
        applicant: "张三",
        status: "pending",
        tenant_name: "测试租户",
      };

      expect(queryCriteria.applicant).toBe("张三");
      expect(queryCriteria.status).toBe("pending");
      expect(queryCriteria.tenant_name).toBe("测试租户");
    });
  });
});
