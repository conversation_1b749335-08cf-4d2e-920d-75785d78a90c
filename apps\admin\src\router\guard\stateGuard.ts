import type { Router } from "vue-router";
import { PageConstants } from "@celeris/constants";
import { useUserStore } from "~/store/modules/user";

export function createStateGuard(router: Router) {
  router.afterEach((to, from) => {
    // Clear authentication information when user enters login page
    if (to.path === PageConstants.BASE_LOGIN && from.path !== to.path) {
      const userStore = useUserStore();
      userStore.logout(false, false);
    }
  });
}
