<template>
  <NDropdown
    :options="tenantOptions"
    trigger="click"
    :show-arrow="false"
    placement="left"
    :width="280"
    :render-option="renderMenuOptions"
    @select="handleTenantSelect"
  >
    <NButton
      quaternary
      block
      :loading="availableTenantsLoading"
      class="!w-32"
    >
      <template #icon>
        <svg class="i-tabler-building text-base" />
      </template>
      选择租户
    </NButton>
  </NDropdown>
</template>

<script setup lang="ts">
  import type { DropdownOption } from "naive-ui";
  import { NButton, NDropdown, useMessage } from "naive-ui";
  import { storeToRefs } from "pinia";
  import { h } from "vue";
  import { setDefaultTenantApi } from "~/apis/internal/tenant";
  import { useUserStore } from "~/store/modules/user";

  // 定义 emit
  const emit = defineEmits<{
    (e: "tenantSwitched"): void;
  }>();
  const message = useMessage();
  const userStore = useUserStore();

  // 从store获取租户相关数据
  const { getCurrentTenant, getAvailableTenants, getAvailableTenantsLoading } = storeToRefs(userStore);

  // 计算属性
  const availableTenantsLoading = computed(() => {
    return getAvailableTenantsLoading.value;
  });

  // 租户下拉选项
  const tenantOptions = computed((): DropdownOption[] => {
    return getAvailableTenants.value.map((tenant) => {
      return {
        key: tenant.tenant_id || tenant.id,
        label: tenant.name,
        disabled: tenant.state === 0,
      };
    });
  });

  /**
   * 自定义渲染下拉菜单选项
   */
  function renderMenuOptions({ option }) {
    // 根据option.key查找对应的租户信息
    const tenant = getAvailableTenants.value.find(t => (t.tenant_id || t.id) === option.key);
    if (!tenant) {
      return null;
    }

    // 判断是否为当前租户
    const isCurrentTenant = getCurrentTenant.value?.id === tenant.id;

    // 判断是否为默认租户（优先使用 isDefault 字段，如果没有则使用当前租户作为默认租户）
    const isDefaultTenant = tenant.is_default;

    return h("div", {
      class: [
        "tenant-option",
        "flex items-center justify-between w-full px-4 py-3 my-0.5 rounded-lg transition-all duration-200 cursor-pointer min-h-11",
        isCurrentTenant
          ? "bg-blue-50 border-l-4 border-blue-500"
          : "bg-transparent border-l-4 border-transparent hover:bg-gray-50",
      ],
      onMouseenter: (e: Event) => {
        // 只显示"设为默认"按钮
        const button = (e.target as HTMLElement).querySelector(".set-default-btn") as HTMLElement;
        if (button) {
          button.style.opacity = "1";
          button.style.visibility = "visible";
        }
      },
      onMouseleave: (e: Event) => {
        // 只隐藏"设为默认"按钮
        const button = (e.target as HTMLElement).querySelector(".set-default-btn") as HTMLElement;
        if (button) {
          button.style.opacity = "0";
          button.style.visibility = "hidden";
        }
      },
      onClick: (e: Event) => {
        e.stopPropagation();
        handleTenantSelect(tenant.tenant_id || tenant.id!);
      },
    }, [
      // 左侧：租户信息区域
      h("div", {
        class: "flex items-center flex-1 min-w-0",
      }, [
        // 租户名称
        h("span", {
          class: [
            "tenant-name",
            "text-sm mr-2 truncate",
            isCurrentTenant
              ? "font-semibold text-blue-600"
              : "font-normal text-gray-800",
          ],
        }, tenant.name),

        // 当前租户标识
        isCurrentTenant
          ? h("div", {
            class: "current-badge flex items-center bg-blue-500 text-white px-2 py-0.5 rounded-full text-xs font-medium",
          }, [
            h("span", {
              class: "mr-1 text-xs",
            }, "✓"),
            h("span", "当前"),
          ])
          : null,
      ]),

      // 右侧：操作区域（根据是否为默认租户显示不同内容）
      isDefaultTenant
        // 默认租户直接显示"当前默认"文字
        ? h("div", {
          class: "current-default-text text-xs text-blue-600 px-3 py-1.5 font-medium flex-shrink-0 ml-3 transition-all duration-200",
        }, "默认")
        // 非默认租户显示"设为默认"按钮（悬停时显示）
        : h("button", {
          class: "set-default-btn text-xs bg-blue-500 text-white px-3 py-1.5 rounded-full border-none cursor-pointer transition-all duration-200 font-medium flex-shrink-0 ml-3 opacity-0 invisible hover:bg-blue-600 hover:scale-105",

          onClick: (e: Event) => {
            e.stopPropagation();
            handleSetDefault(tenant.tenant_id || tenant.id!);
          },
        }, "设为默认"),
    ]);
  }

  /**
   * 处理租户选择
   */
  async function handleTenantSelect(tenantId: string) {
    try {
      await userStore.switchTenant(tenantId);
      const selectedTenant = getAvailableTenants.value.find(t => t.id === tenantId);
      message.success(`已切换到租户：${selectedTenant?.name}`);

      // 通知父组件关闭弹出框
      emit("tenantSwitched");

      // 切换租户后刷新当前页面，重新请求数据
      setTimeout(() => {
        window.location.reload();
      }, 500); // 延迟500ms让成功消息显示完成
    } catch (error) {
      console.error("切换租户失败:", error);
      message.error("切换租户失败，请重试");
    }
  }

  /**
   * 处理设置默认租户
   */
  async function handleSetDefault(tenantId: string) {
    try {
      await setDefaultTenantApi(tenantId);
      await userStore.fetchAvailableTenants(true, true);
      message.success("已设置默认租户");
    } catch (error) {
      console.error("设置默认租户失败:", error);
      message.error("设置默认租户失败，请重试");
    }
  }

  // 组件挂载时获取租户列表
  onMounted(async () => {
    try {
      await userStore.fetchAvailableTenants();
    } catch (error) {
      console.error("获取租户列表失败:", error);
    }
  });
</script>

<style scoped>
/* 租户选项悬停时只显示设为默认按钮 */
.tenant-option:hover .set-default-btn {
  opacity: 1;
  visibility: visible;
}
</style>
