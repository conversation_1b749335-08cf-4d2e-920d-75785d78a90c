{"name": "@celeris/hooks", "type": "module", "version": "0.0.3", "description": "hooks for <PERSON><PERSON><PERSON>", "author": "<PERSON> (https://github.com/kirklin)", "license": "MIT", "homepage": "https://github.com/kirklin/celeris-web", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./index.ts", "module": "./index.ts", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --stub", "clean": "pnpm rimraf node_modules && pnpm rimraf dist", "test": "vitest", "test:coverage": "vitest run --coverage", "prepublishOnly": "npm run build", "postinstall": "npm run build"}, "peerDependencies": {"vue": ">=3.3.4"}, "dependencies": {"@celeris/utils": "workspace:*", "@vueuse/core": "^11.3.0", "@vueuse/integrations": "^11.3.0", "universal-cookie": "^7.2.2"}}