import type { App, AppContext, Directive, Plugin } from "vue";
import { NOOP } from "@vue/shared";

export type SFCWithInstall<T> = T & Plugin;

export type SFCInstallWithContext<T> = SFCWithInstall<T> & {
  _context: AppContext | null;
};
/*
 * 为组件提供安装方法
 * Provide an install method for the component
 */
export function withInstall<T, E extends Record<string, any>>(main: T, extra?: E) {
  (main as SFCWithInstall<T>).install = (app): void => {
    for (const comp of [main, ...Object.values(extra ?? {})]) {
      app.component(comp.name, comp);
    }
  };

  if (extra) {
    for (const [key, comp] of Object.entries(extra)) {
      (main as any)[key] = comp;
    }
  }
  return main as SFCWithInstall<T> & E;
}

/*
 * 为函数提供安装方法
 * Provide an install method for the function
 */
export function withInstallFunction<T>(fn: T, name: string) {
  (fn as SFCWithInstall<T>).install = (app: App) => {
    (fn as SFCInstallWithContext<T>)._context = app._context;
    app.config.globalProperties[name] = fn;
  };

  return fn as SFCInstallWithContext<T>;
}

/*
 * 为指令提供安装方法
 * Provide an install method for the directive
 */
export function withInstallDirective<T extends Directive>(directive: T, name: string) {
  (directive as SFCWithInstall<T>).install = (app: App): void => {
    app.directive(name, directive);
  };

  return directive as SFCWithInstall<T>;
}

/*
 * 为组件提供空安装方法
 * Provide an empty install method for the component
 */
export function withNoopInstall<T>(component: T) {
  (component as SFCWithInstall<T>).install = NOOP;

  return component as SFCWithInstall<T>;
}
