<template>
  <NDrawer
    :show="show"
    width="600"
    placement="right"
    @after-leave="handleAfterLeave"
    @update:show="handleUpdateShow"
  >
    <NDrawerContent title="分配权限" closable body-class="p-3">
      <!-- <NSpin v-if="loading" size="large" /> -->
      <!-- 权限选择区域 -->
      <NCheckboxGroup v-model:value="checkedKeys">
        <NGrid :y-gap="8" :cols="1">
          <NGi v-for="authority in permissionArr" :key="authority">
            <NCheckbox :key="authority" :value="authority">
              {{ authority }}
            </NCheckbox>
          </NGi>
        </NGrid>
      </NCheckboxGroup>
      <template #footer>
        <NButton class="mr-3" @click="handleHide">
          取消
        </NButton>
        <NButton type="primary" :loading="submitting" @click="authConfirm">
          确定
        </NButton>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<script lang="ts" setup>
  import type { Permission, PermissionExpanded } from "-/permission";
  import type { RoleExpanded } from "-/role";
  import { createPermission, deletePermission, getPermissionList } from "~/apis/internal/permission";

  /**
   * @description 权限分配抽屉组件
   * @description 用于为角色分配或移除权限，支持批量操作
   */
  defineOptions({
    name: "AuthorityDrawer",
    inheritAttrs: false,
  });

  /**
   * @description 组件属性定义
   */
  const props = withDefaults(defineProps<{
    show: boolean;
    data: RoleExpanded | null; // 角色数据，包含角色ID等信息
    domainId: string;
  }>(), {
    data: () => ({}) as RoleExpanded,
  });

  const emit = defineEmits<{
    "update:show": [show: boolean];
  }>();

  const show = toRef(props, "show");

  const message = useMessage();

  const loading = ref(false); // 加载中
  const submitting = ref(false); // 提交状态标识
  const checkedKeys = ref<string[]>([]); // 当前已勾选的权限键值数组
  const odkCheckedKeys = ref<string[]>([]); // 原始已勾选的权限键值数组（用于对比变更）
  const permissionArr = ref<string[]>([]); //  权限展示数组，用于渲染复选框列表
  const allPermission = ref<Permission[]>([]); // 所有权限数据，包含重复项
  const uniquePermission = ref<Permission[]>([]); // 去重后的权限数据

  /**
   * @description 弹窗打开初始化表单数据
   * @returns {void}
   */
  watch(show, async (val) => {
    if (val) {
      await initData();
    }
  });

  /**
   * @description 初始化权限数据
   * @description 获取所有权限列表，过滤出当前角色已分配的权限，并去重处理
   * @returns {Promise<void>}
   */
  async function initData() {
    loading.value = true;
    try {
      // 获取权限数据
      const result = await getPermissionList(props.domainId, {
        page_index: 1,
        page_size: 999,
      });

      allPermission.value = result.data.value ?? [];
      // 过滤出当前角色已分配的权限
      checkedKeys.value = filterSelectArr(result.data.value);
      // 去重处理权限数据
      uniquePermission.value = uniquePermissions();
      // 格式化权限数据用于展示
      permissionArr.value = uniquePermission.value.map(obj => `动作：${obj.action} --- 资源：-${obj.resource}`);

      // 保存原始勾选状态，用于后续对比变更
      odkCheckedKeys.value = checkedKeys.value;
    } finally {
      loading.value = false;
    }
  }

  /**
   * @description 在抽屉关闭后重置权限相关数据
   * @returns {void}
   */
  function handleAfterLeave() {
    // 重置所有数据
    allPermission.value = [];
    permissionArr.value = [];
    uniquePermission.value = [];
    checkedKeys.value = [];
  }

  /**
   * @description 过滤该角色已经绑定的策略
   * @param allData - 所有权限数据
   * @returns {string[]} 已分配的权限键值数组
   */
  function filterSelectArr(allData: any): string[] {
    const initArr: string[] = [];
    allData.forEach((item: any) => {
      if (item.role === props.data?.name) {
        initArr.push(`动作：${item.action} --- 资源：-${item.resource}`);
      }
    });
    return initArr;
  }

  /**
   * @description 对权限数据进行去重处理
   * @description 基于 resource-action 组合进行去重，保留第一个匹配的权限
   * @returns {Permission[]} 去重后的权限数组
   */
  function uniquePermissions(): Permission[] {
    const uniquePermission = [
      ...new Set(allPermission.value.map(({ resource, action }) => `${resource}-${action}`)),
    ].map(key => allPermission.value.find(permission => `${permission.resource}-${permission.action}` === key)).filter(Boolean) as Permission[];
    return uniquePermission;
  }

  /**
   * @description 确认权限分配操作
   * @description 对比原始勾选状态和当前勾选状态，执行新增和删除权限的操作
   * @returns {Promise<void>}
   */
  async function authConfirm() {
    try {
      submitting.value = true;

      // 计算需要删除的权限
      const removedItems = filterPermissionsForDelete(odkCheckedKeys.value, checkedKeys.value);
      // 计算需要新增的权限
      const addedItems = filterPermissionsForAdd(odkCheckedKeys.value, checkedKeys.value);

      // 执行新增权限操作
      if (addedItems.length > 0) {
        // console.log("增加的勾选：", addedItems);
        await createPermission(props.domainId, addedItems);
      }

      // 执行删除权限操作
      if (removedItems.length > 0) {
        // console.log("减少的勾选：", removedItems);
        await deletePermission(props.domainId, removedItems);
      }

      // 操作成功提示
      message.success("角色策略设置成功");
      handleHide();
    } catch (e) {
      message.error("操作失败");
    } finally {
      submitting.value = false;
    }
  }

  /**
   * @description 计算需要删除的权限
   * @description 对比原始勾选状态和当前勾选状态，找出被取消勾选的权限
   * @param oldCheck - 原始勾选的权限键值数组
   * @param newCheck - 当前勾选的权限键值数组
   * @returns {PermissionExpanded[]} 需要删除的权限数组
   */
  function filterPermissionsForDelete(oldCheck: string[], newCheck: string[]): PermissionExpanded[] {
    // 找出被取消勾选的权限键值
    const deletePermissionKey: string[] = oldCheck.filter(pKey => !newCheck.includes(pKey));

    // 根据键值找到对应的权限对象，并添加角色ID
    return uniquePermission.value
      .filter(permission =>
        deletePermissionKey.includes(`动作：${permission.action} --- 资源：-${permission.resource}`),
      )
      .map(permission => ({
        role: props.data?.name,
        // role_id: props.data?.id as string,
        project: props.data?.project_name,
        resource: permission.resource,
        action: permission.action,
      } as PermissionExpanded));
  }

  /**
   * @description 计算需要新增的权限
   * @description 对比原始勾选状态和当前勾选状态，找出新勾选的权限
   * @param oldCheck - 原始勾选的权限键值数组
   * @param newCheck - 当前勾选的权限键值数组
   * @returns {Permission[]} 需要新增的权限数组
   */
  function filterPermissionsForAdd(oldCheck: string[], newCheck: string[]): Permission[] {
    // 找出新勾选的权限键值
    const addPermissionKey: string[] = newCheck.filter(pKey => !oldCheck.includes(pKey));

    // 根据键值找到对应的权限对象，并添加角色ID
    return uniquePermission.value
      .filter(permission =>
        addPermissionKey.includes(`动作：${permission.action} --- 资源：-${permission.resource}`),
      )
      .map(permission => ({
        role: props.data?.name,
        // role_id: props.data?.id as string,
        project: props.data?.project_name,
        resource: permission.resource,
        action: permission.action,
      } as Permission));
  }

  /**
   * @description 处理抽屉的显示状态更新
   * @param {boolean} show - 当前抽屉显示状态
   * @returns {void}
   */
  function handleUpdateShow(show) {
    if (!show) {
      handleHide();
    }
  }

  /**
   * @description 关闭抽屉
   * @returns {void}
   */
  function handleHide() {
    emit("update:show", false);
  }
</script>
