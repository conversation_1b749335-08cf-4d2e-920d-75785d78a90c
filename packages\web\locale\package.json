{"name": "@celeris/locale", "type": "module", "version": "0.0.3", "description": "locale for Celeris", "author": "<PERSON> (https://github.com/kirklin)", "license": "MIT", "homepage": "https://github.com/kirklin/celeris-web", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./index.ts", "module": "./index.ts", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --stub", "clean": "pnpm rimraf node_modules && pnpm rimraf dist", "prepublishOnly": "npm run build", "postinstall": "npm run build"}, "dependencies": {"@celeris/constants": "workspace:*", "@celeris/hooks": "workspace:*", "@celeris/utils": "workspace:*", "iso-639-1": "^3.1.3", "vue": "^3.5.13", "vue-i18n": "^10.0.4"}}