<template>
  <div>
    <NFlex
      class="overflow-hidden flex-1"
      size="large"
      vertical
      style="height: calc(100vh - 230px)"
    >
      <!-- 工具栏 -->
      <NPageHeader>
        <NFlex class="pr" justify="end">
          <NFlex>
            <NForm :model="searchModel" :show-feedback="false" inline label-placement="left" @submit.prevent>
              <NFormItem path="uid" label="用户">
                <UserSelect
                  v-model:value="searchModel.uid"
                  placeholder="请输入用户账号或姓名"
                  remote
                  clearable
                  @update:value="searchConfirm"
                />
              </NFormItem>
              <NFormItem path="commitBy" label="分析触发人">
                <UserSelect
                  v-model:value="searchModel.commitBy"
                  placeholder="请输入用户账号或姓名"
                  remote
                  clearable
                  @update:value="searchConfirm"
                />
              </NFormItem>
              <NFormItem path="date" label="分析时间">
                <NDatePicker
                  v-model:value="queryTime"
                  type="daterange"
                  class="w-70"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @update:value="searchConfirm"
                />
              </NFormItem>
              <NFormItem>
                <NSpace>
                  <NButton type="primary" @click="searchConfirm">
                    查询
                  </NButton>
                  <NButton @click="searchReset">
                    重置
                  </NButton>
                </NSpace>
              </NFormItem>
            </NForm>
          </NFlex>
        </NFlex>
      </NPageHeader>
      <!-- 表格 -->
      <NDataTable
        class="fixed-right-table h-full [&_.ca-data-table-resize-button::after]:(!bg-[var(--primary-color)])"
        scroll-x="min-content"
        remote
        flex-height
        :bordered="false"
        :single-line="false"
        :columns="columns"
        :data="tableData"
        :loading="tableLoading"
        :pagination="tablePagination"
        @scroll="handleScroll"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      >
      </NDataTable>
    </NFlex>
    <!-- 详情对话框 -->
    <ModelDetailModal
      v-model:show="detailVisible"
      :data="currModelResult"
    />
  </div>
</template>

  <script setup lang="tsx">
  import type { ModelResult, ModelResultExpanded } from "-/behavior";
  import type { DataTableColumns } from "naive-ui";
  import { formatToDate } from "@celeris/utils";
  import { NButton, NFlex } from "naive-ui";
  import { deleteModelResult, getModelResultList } from "~/apis/internal/behavior";
  import { UserSelect } from "~/component/UserSelect";
  import ModelDetailModal from "./ModelDetailModal.vue";

  interface QueryCriteria {
    uid: string | null;
    commitBy: string | null;
    from: string | null;
    to: string | null;
  }

  const INIT_QUERY_MODEL = {
    uid: null,
    commitBy: null,
  };

  const queryCriteria = ref<QueryCriteria>({
    uid: null,
    commitBy: null,
    from: null,
    to: null,
  });

  const {
    tableLoading,
    tableData,
    tablePagination,
    loadData,
    refreshData,
    reloadData,
    handlePageChange,
    handlePageSizeChange,
    handleScroll,
  } = useTable<QueryCriteria, ModelResult, ModelResultExpanded>();

  // 查询参数
  const searchModel = ref({ ...INIT_QUERY_MODEL });
  const queryTime = ref<[number, number]>();

  const currModelResult = ref<ModelResultExpanded | null>(null);

  // 弹窗相关
  const detailVisible = ref(false);

  // ========== 表格列定义 ==========
  const columns: DataTableColumns<ModelResultExpanded> = [
    {
      title: "姓名",
      key: "userName",
      width: 130,
    },
    {
      title: "详情",
      key: "content",
      render(row) {
        return (
            <NFlex>
              <NButton
                text
                type="info"
                onClick={() => showDetail?.(row)}
              >
                解读
              </NButton>
            </NFlex>
        );
      },
    },
    {
      title: "解读范围",
      key: "reportRange",
    },
    {
      title: "解读时间",
      key: "analysisTime",
      render(row) {
        return formatToDate(row.analysisTime);
      },
    },
    {
      title: "分析属性",
      key: "analysisType",
    },
    {
      title: "分析触发人",
      key: "commitBy",
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      render(row) {
        return (
          <NFlex>
            <NButton
              text
              type="error"
              loading={row.deleteLoading}
              onClick={() => handleDelete?.(row)}
            >
            删除
            </NButton>
          </NFlex>
        );
      },
    },
  ];

  const dialog = useDialog();
  const message = useMessage();

  // 初始化
  onMounted(() => {
    loadTableData();
  });

  // 监听 searchModel、queryTime 变化，同步到 queryCriteria
  watch([searchModel, queryTime], ([searchVal, timeVal]) => {
    Object.keys(searchVal).forEach((key) => {
      queryCriteria.value[key] = searchVal?.[key];
    });
    if (timeVal) {
      queryCriteria.value.from = formatToDate(timeVal[0]);
      queryCriteria.value.to = formatToDate(timeVal[1]);
    }
  }, { immediate: true, deep: true });

  /**
   * @description 获取大模型解读列表
   */
  function loadTableData() {
    loadData({
      getQueryCriteria: () => queryCriteria.value,
      dataPath: "data.analysis",
      totalPath: "data.total",
      tableRequest: getModelResultList as unknown as (queryCriteria?: QueryCriteria) => Promise<ModelResult[]>,
      handleTableData: (dataSource: ModelResultExpanded[]) => {
        return dataSource.map((item, index) => {
          return {
            ...item,
            key: item.id ? String(item.id) : index.toString(),
            deleteLoading: false,
          } as ModelResultExpanded;
        });
      },
    });
  }

  /**
   * @description 筛选条件查询，重置页码为1并加载数据
   */
  function searchConfirm() {
    nextTick(() => {
      reloadData();
    });
  }

  /**
   * @description 查询条件重置，清空筛选项并重置页码，重新加载数据
   */
  function searchReset() {
    searchModel.value.uid = null;
    searchModel.value.commitBy = null;
    reloadData();
  }

  /**
   * @description 显示详情弹窗，展示当前行的大模型解读详情
   * @param row - 当前行的大模型解读数据
   */
  function showDetail(row) {
    currModelResult.value = row;
    detailVisible.value = true;
  }

  /**
   * @description 删除大模型解读，弹出确认框，确认后调用删除接口并刷新列表
   * @param {ModelResultExpanded} row - 当前行大模型解读数据
   */
  function handleDelete(row: ModelResultExpanded) {
    dialog.info({
      title: "警告",
      content: `您确认要删除【${row.userName}】大模型解读吗？`,
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          await deleteModelResult({ id: row.id });
          message.success("删除成功");
          refreshData();
        } catch (e) {
          message.error("删除失败");
        }
      },
    });
  }
  </script>

  <style scoped></style>
