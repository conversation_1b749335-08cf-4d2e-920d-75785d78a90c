<template>
  <NFlex vertical size="large">
    <!-- 表格 -->
    <NDataTable
      class="fixed-right-table h-[calc(100vh-260px)] [&_.ca-data-table-resize-button::after]:(!bg-[var(--primary-color)])"
      scroll-x="min-content"
      remote
      flex-height
      :striped="true"
      :bordered="false"
      :single-line="false"
      :loading="tableLoading"
      :data="tableData"
      :columns="tableColumns"
      :pagination="tablePagination"
      @scroll="handleScroll"
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
    />

    <!-- 租户详情弹框 -->
    <TenantDetailModal
      v-model:show="showDetailModal"
      :data="currentTenant"
      :mode="modalMode"
      @submit="handleModalSubmit"
    />

    <!-- 租户用户管理弹框 -->
    <TenantUserManageModal
      v-model:show="showUserManageModal"
      :data="currentTenant"
    />
  </NFlex>
</template>

<script setup lang="ts">
  import type { QueryCriteria, TenantInfo, TenantInfoExpanded, TenantListParams } from "-/tenant";
  import type { DataTableColumns } from "naive-ui";
  import { CAIcon } from "@celeris/components";
  import { formatToDate } from "@celeris/utils";
  import { cloneDeep, debounce } from "lodash-es";
  import { NButton, NDropdown, NFlex, NTag, useDialog, useMessage } from "naive-ui";
  import { createTenantApi, deleteTenantApi, tenantListApi, updateTenantApi } from "~/apis/internal/tenant";
  import { useTable } from "~/composables/useTable";
  import TenantDetailModal from "./TenantDetailModal.vue";
  import TenantUserManageModal from "./TenantUserManageModal.vue";

  const props = withDefaults(defineProps<{
    queryCriteria: QueryCriteria;
  }>(), {
    queryCriteria: () => ({}) as QueryCriteria,
  });

  // ========== 消息提示 ==========
  const message = useMessage();
  const dialog = useDialog();

  // ========== 注入触发器 ==========
  const addTenantTrigger = inject<Ref<number>>("addTenantTrigger");
  const searchTrigger = inject<Ref<number>>("searchTrigger");

  // ========== 弹框状态 ==========
  const showDetailModal = ref(false);
  const showUserManageModal = ref(false);
  const currentTenant = ref<TenantInfoExpanded | null>(null);
  const modalMode = ref<"view" | "edit">("view");

  // ========== 表格相关 ==========
  const {
    tableLoading,
    tableData,
    tablePagination,
    loadData,
    handlePageChange,
    handlePageSizeChange,
    handleScroll,
  } = useTable<QueryCriteria, TenantInfo, TenantInfoExpanded>();

  // ========== 数据加载函数 ==========
  /**
   * 加载表格数据（内部实现）
   */
  async function _loadTableData() {
    await loadData({
      getQueryCriteria: () => {
        // 构建查询字符串
        const queryParts: string[] = [];

        if (props.queryCriteria.name) {
          queryParts.push(`name=~${encodeURIComponent(props.queryCriteria.name)}`);
        }

        return {
          q: queryParts.length > 0 ? queryParts.join(",") : undefined,
        };
      },
      withPagination: true,
      dataPath: "value",
      tableRequest: tenantListApi as any,
      handleTableData: (data: TenantInfo[]) => {
        return data.map((item: TenantInfo) => ({
          ...item,
          key: item.id,
          deleteLoading: false,
          editLoading: false,
        })) as TenantInfoExpanded[];
      },
    });
  }

  /**
   * 加载表格数据（防抖版本，300ms延迟）
   */
  const loadTableData = debounce(_loadTableData, 300);

  // ========== 表格列定义 ==========
  const tableColumns: DataTableColumns<TenantInfoExpanded> = [
    {
      title: "租户名称",
      key: "name",
      width: 200,
      fixed: "left",
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "管理员",
      key: "admin_name",
      width: 150,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        if (!row.admin_name || !Array.isArray(row.admin_name) || row.admin_name.length === 0) {
          return h("span", "--");
        }

        // 如果只有一个管理员，直接显示
        if (row.admin_name.length === 1) {
          return h("span", row.admin_name[0]);
        }

        // 如果有多个管理员，显示前两个，然后用省略号
        const displayNames = row.admin_name.slice(0, 2).join("、");
        const fullNames = row.admin_name.join("、");

        return h("span", {
          title: fullNames, // 鼠标悬停显示所有管理员
        }, row.admin_name.length > 2 ? `${displayNames}等` : displayNames);
      },
    },
    {
      title: "状态",
      key: "state",
      width: 100,
      render(row) {
        const status = row.state || 0;

        const statusMap = {
          1: { type: "success", text: "已激活" },
          0: { type: "warning", text: "未激活" },
        } as const;

        const statusInfo = statusMap[status as keyof typeof statusMap];
        if (!statusInfo) {
          return h("span", status);
        }

        return h(
          NTag,
          {
            type: statusInfo.type,
            bordered: false,
          },
          {
            default: () => statusInfo.text,
          },
        );
      },
    },
    {
      title: "创建时间",
      key: "registration_date",
      width: 160,
      render(row) {
        return h("span", row.registration_date ? formatToDate(row.registration_date) : "--");
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 100,
      fixed: "right",
      render(row) {
        return h(NFlex, {}, {
          default: () => [
            h(NButton, {
              text: true,
              type: "info",
              onClick: () => handleEdit(cloneDeep(row)),
            }, { default: () => "编辑" }),
            h(NButton, {
              text: true,
              type: "info",
              onClick: () => handleView(cloneDeep(row)),
            }, { default: () => "查看" }),
            h(NDropdown, {
              options: getDropdownOptions(row),
              onSelect: (key: string) => handleDropdownSelect(key, row),
              trigger: "hover",
            }, {
              default: () => h(NButton, {
                text: true,
                type: "info",
              }, {
                default: () => [
                  "更多",
                  h("n-icon", {}, h(CAIcon, { icon: "tabler:chevron-down", size: 15 })),
                ],
              }),
            }),
          ],
        });
      },
    },
  ];

  // ========== 操作相关 ==========
  /**
   * 获取下拉菜单选项
   */
  function getDropdownOptions(_row: TenantInfoExpanded) {
    return [
      {
        label: "用户管理",
        key: "userManage",
        props: {
          style: "color: 1fe0d1#;",
        },
      },
      {
        label: "删除",
        key: "delete",
        props: {
          style: "color: #d03050;",
        },
      },
    ];
  }

  /**
   * 处理编辑操作
   */
  function handleEdit(row: TenantInfoExpanded) {
    currentTenant.value = row;
    modalMode.value = "edit";
    showDetailModal.value = true;
  }

  /**
   * 处理查看操作
   */
  function handleView(row: TenantInfoExpanded) {
    currentTenant.value = row;
    modalMode.value = "view";
    showDetailModal.value = true;
  }

  /**
   * 处理下拉菜单选择
   */
  function handleDropdownSelect(key: string, row: TenantInfoExpanded) {
    switch (key) {
      case "delete":
        handleDelete(row);
        break;
      case "userManage":
        handleUserManage(row);
        break;
    }
  }
  /**
   * 处理删除操作
   */
  function handleDelete(row: TenantInfoExpanded) {
    if (row.deleteLoading) {
      return;
    }
    dialog.warning({
      title: "删除确认",
      content: `确认要删除租户 "${row.name}" 吗？`,
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          row.deleteLoading = true;
          // TODO: 调用删除租户API
          await deleteTenantApi(row.id);
          message.success("删除成功");
          await loadTableData();
        } catch (error) {
          console.error("删除失败:", error);
          message.error("删除失败，请重试");
        } finally {
          row.deleteLoading = false;
        }
      },
    });
  }
  /**
   * 处理用户管理操作
   */
  function handleUserManage(row: TenantInfoExpanded) {
    currentTenant.value = cloneDeep(row);
    showUserManageModal.value = true;
  }

  /**
   * 处理新增租户
   */
  function handleAdd() {
    currentTenant.value = null;
    modalMode.value = "edit";
    showDetailModal.value = true;
  }

  /**
   * 处理弹框提交
   */
  async function handleModalSubmit(_data: TenantInfo) {
    try {
      if (_data.id) {
        await updateTenantApi(_data as unknown as AnyObject);
        message.success("更新成功");
      } else {
        // TODO: 调用创建租户API
        await createTenantApi(_data as unknown as AnyObject);
        message.success("创建成功");
      }
      // 重新加载表格数据
      await loadTableData();
    } catch {
      message.error("操作失败，请重试");
    }
  }

  // ========== 数据加载 ==========

  // ========== 生命周期 ==========
  // 监听查询条件变化，重新加载数据
  watchEffect(() => {
    loadTableData();
  });

  // 监听新增租户触发器
  if (addTenantTrigger) {
    watch(addTenantTrigger, (newVal, oldVal) => {
      if (newVal > oldVal) {
        handleAdd();
      }
    });
  }

  // 监听搜索触发器
  if (searchTrigger) {
    watch(searchTrigger, (newVal, oldVal) => {
      if (newVal > oldVal) {
        // 重新加载表格数据
        loadTableData();
      }
    });
  }
</script>

<style scoped>
.fixed-right-table :deep(.n-data-table-th--fixed-right) {
  background-color: var(--n-th-color);
}

.fixed-right-table :deep(.n-data-table-td--fixed-right) {
  background-color: var(--n-td-color);
}
</style>
