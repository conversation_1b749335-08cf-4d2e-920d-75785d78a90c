{"name": "@celeris/utils", "type": "module", "version": "0.0.3", "description": "utils for Celeris", "author": "<PERSON> (https://github.com/kirklin)", "license": "MIT", "homepage": "https://github.com/kirklin/celeris-web", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "src/index.ts", "module": "src/index.ts", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --stub", "clean": "pnpm rimraf node_modules && pnpm rimraf dist", "test": "vitest", "test:coverage": "vitest run --coverage", "release": "bumpp", "typecheck": "tsc --noEmit", "prepublishOnly": "npm run build", "postinstall": "npm run build"}, "dependencies": {"@kirklin/logger": "0.0.2", "@kirklin/palette": "^0.1.1", "algorithm-snowflake": "^0.0.0", "colord": "^2.9.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "detect-mobile": "^0.0.0", "lodash-es": "^4.17.21", "decimal.js": "^10.5.0", "path-to-regexp": "^8.2.0"}, "devDependencies": {"@celeris/constants": "workspace:*", "@celeris/types": "workspace:*", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "vue": "^3.5.13", "vue-router": "^4.4.5"}, "volta": {"node": "18.20.4"}}