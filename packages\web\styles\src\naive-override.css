.ca-modal-mask {
  backdrop-filter: blur(5px);
}
.ca-modal,
.ca-card.ca-modal[role] {
  background-color: rgba(var(--modal-color-rgb), 0.75);
  backdrop-filter: blur(20px);
  max-width: 90%;
  margin: 10vh auto;
}

.ca-image-preview-overlay {
  backdrop-filter: blur(5px);
}

.ca-slider {
  box-sizing: content-box;
}
.ca-calendar * {
  box-sizing: content-box;
}

/* Drawer component with rounded corners */
.ca-drawer.ca-drawer--right-placement {
  border-top-left-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}
.ca-drawer.ca-drawer--left-placement {
  border-top-right-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
}
.ca-drawer.ca-drawer--top-placement {
  border-bottom-left-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
}
.ca-drawer.ca-drawer--bottom-placement {
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
}
.ca-base-selection-tags .ca-base-selection-tag-wrapper .ca-tag {
  font-size: 13px;
}

/* 给右侧固定列的第一列表头增加阴影 */
.fixed-right-table .ca-data-table-th--fixed-right::before {
  box-shadow: var(--n-box-shadow-before);
}
/* 去除右侧固定列除第一列以外的固定列表头的阴影 */
.fixed-right-table .ca-data-table-th--fixed-right ~ .ca-data-table-th--fixed-right::before {
  box-shadow: none;
}
/* 给右侧固定列的第一列表体增加阴影 */
.fixed-right-table .ca-data-table-td--fixed-right::before {
  box-shadow: var(--n-box-shadow-before);
}
/* 去除右侧固定列除第一列以外的固定列表体的阴影 */
.fixed-right-table .ca-data-table-td--fixed-right ~ .ca-data-table-td--fixed-right::before {
  box-shadow: none;
}
/* 当滚动到最右侧时，去除固定列阴影 */
.fixed-right-table.scroll-right .ca-data-table-th--fixed-right::before {
  box-shadow: none;
}
/* 当滚动到最右侧时，去除固定列阴影 */
.fixed-right-table.scroll-right .ca-data-table-th--fixed-right ~ .ca-data-table-th--fixed-right::before {
  box-shadow: none;
}
/* 当滚动到最右侧时，去除固定列阴影 */
.fixed-right-table.scroll-right .ca-data-table-td--fixed-right::before {
  box-shadow: none;
}
/* 当滚动到最右侧时，去除固定列阴影 */
.fixed-right-table.scroll-right .ca-data-table-td--fixed-right ~ .ca-data-table-td--fixed-right::before {
  box-shadow: none;
}
