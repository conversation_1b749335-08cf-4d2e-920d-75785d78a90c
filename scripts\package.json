{"name": "scripts", "version": "0.0.3", "private": true, "author": "<PERSON> (https://github.com/kirklin)", "license": "MIT", "homepage": "https://github.com/kirklin/celeris-web", "bin": {}, "scripts": {"clean": "pnpm rimraf node_modules && pnpm rimraf dist", "dev": "pnpm esno ./src/dev.ts", "build": "pnpm esno ./src/build.ts", "generate-tree": "pnpm esno ./src/directoryTreeOrganizer.ts"}, "devDependencies": {"@types/prompts": "^2.4.9", "execa": "^9.5.1", "prompts": "^2.4.2"}}