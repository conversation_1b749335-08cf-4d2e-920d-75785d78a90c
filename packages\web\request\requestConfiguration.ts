import type { MessageMode } from "@celeris/request";
import type { AxiosError } from "axios";

export interface HttpRequestOptions {
  errorMessageHandler: (message: string, mode?: MessageMode) => void;
  successMessageHandler: (message: string, mode?: MessageMode) => void;
  messageHandler: (message: string, mode?: MessageMode) => void;
  unauthorizedHandler: (params: any | AxiosError<any>) => void;
  timeoutHandler: () => void;
  getToken: () => unknown;
  getCurrentTenant?: () => { id?: string } | null | undefined;
}

export class HttpRequestConfiguration {
  private static options: HttpRequestOptions = {
    // 消息提示类型
    errorMessageHandler: () => {},
    // 成功提示类型
    successMessageHandler: () => {},
    // 普通消息提示类型
    messageHandler: () => {},
    // 未授权处理
    unauthorizedHandler: () => {},
    // 会话超时处理
    timeoutHandler: () => {},
    // 获取token
    getToken: () => undefined,
    // 获取当前租户
    getCurrentTenant: () => null,
  };

  /*
   * 配置请求选项
   * Configure the request options
   */
  static configure(options: Partial<HttpRequestOptions>): void {
    this.options = { ...this.options, ...options };
  }

  /*
   * 获取请求选项
   * Get the request options
   */
  static get successMessageHandler(): (message: string, mode?: MessageMode) => void {
    return this.options.successMessageHandler;
  }

  /*
   * 获取错误消息处理函数
   * Get the error message handler
   */
  static get errorMessageHandler(): (message: string, mode?: MessageMode) => void {
    return this.options.errorMessageHandler;
  }

  /*
   * 获取消息处理函数
   * Get the message handler
   */
  static get messageHandler(): (message: string, mode?: MessageMode) => void {
    return this.options.messageHandler;
  }

  /*
   * 获取未授权处理函数
   * Get the unauthorized handler
   */
  static get unauthorizedHandler(): (params: any | AxiosError<any>) => void {
    return this.options.unauthorizedHandler;
  }

  /*
   * 获取超时处理函数
   * Get the timeout handler
   */
  static get timeoutHandler(): () => void {
    return this.options.timeoutHandler;
  }

  /*
   * 获取获取令牌函数
   * Get the get token function
   */
  static get getToken(): () => unknown {
    return this.options.getToken;
  }

  /*
   * 获取当前租户函数
   * Get the current tenant function
   */
  static get getCurrentTenant(): (() => { id?: string } | null | undefined) | undefined {
    return this.options.getCurrentTenant;
  }
}

export class HttpRequestEngine {
  /*
   * 初始化请求
   * Initialize the request
   */
  static initRequest(configureFunction: () => Partial<HttpRequestOptions>): void {
    HttpRequestConfiguration.configure(configureFunction());
  }

  /*
   * 设置消息处理函数
   * Set the message handler
   */
  static setMessageHandler(messageHandler: (message: string, mode?: MessageMode) => void): void {
    HttpRequestConfiguration.configure({ messageHandler });
  }

  /*
   * 设置成功消息处理函数
   * Set the success message handler
   */
  static setSuccessMessageHandler(successMessageHandler: (message: string, mode?: MessageMode) => void): void {
    HttpRequestConfiguration.configure({ successMessageHandler });
  }

  /*
   * 设置错误消息处理函数
   * Set the error message handler
   */
  static setErrorMessageHandler(errorMessageHandler: (message: string, mode?: MessageMode) => void): void {
    HttpRequestConfiguration.configure({ errorMessageHandler });
  }

  /*
   * 设置未授权处理函数
   * Set the unauthorized handler
   */
  static setUnauthorizedHandler(unauthorizedHandler: (params: any | AxiosError<any>) => void): void {
    HttpRequestConfiguration.configure({ unauthorizedHandler });
  }

  /*
   * 设置超时处理函数
   * Set the timeout handler
   */
  static setTimeoutHandler(timeoutHandler: () => void): void {
    HttpRequestConfiguration.configure({ timeoutHandler });
  }
}
