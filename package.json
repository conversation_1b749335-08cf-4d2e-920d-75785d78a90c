{"name": "celeris-web-monorepo", "type": "module", "version": "0.0.3", "private": true, "packageManager": "pnpm@9.14.2", "author": "<PERSON> (https://github.com/kirklin)", "license": "MIT", "homepage": "https://github.com/kirklin/celeris-web", "keywords": ["Celeris Web", "celeris-web", "celeris", "front-end development", "Vue 3", "Vite", "TypeScript", "monorepo", "OpenAI", "natural language processing", "streamlined development", "lightning-fast", "components", "APIs", "Vue Router", "loading feedback", "state management", "Chinese font", "i18n", "Netlify"], "main": "src/main.ts", "engines": {"node": ">=16"}, "scripts": {"bootstrap": "pnpm install", "dev": "run-p dev:admin", "dev:admin": "pnpm --filter @celeris/admin dev", "build:development": "pnpm --filter @celeris/admin build:development", "build:test": "pnpm --filter @celeris/admin build:test", "build:production": "pnpm --filter @celeris/admin build:production", "generate-tree": "pnpm --filter scripts generate-tree", "clean": "rimraf node_modules **/*/node_modules **/**/*/node_modules **/*/dist **/**/*/dist", "clear": "rimraf 'packages/*/{lib,node_modules}' && rimraf node_modules", "preview": "run-p preview:admin dev:mock dev:ai-services-openai", "preview:admin": "pnpm --filter @celeris/admin preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "pnpm -r run test", "test:coverage": "pnpm -r run test:coverage", "reinstall": "npm run clean && rimraf pnpm-lock.yaml && npm run bootstrap", "cs": "changeset", "cs:version": "changeset version", "cs:tag": "changeset tag", "up": "taze major -r -w -I", "release": "bumpp -r && pnpm -r publish --access public", "prepare": "husky install", "preinstall": "npx only-allow pnpm"}, "devDependencies": {"@celeris/tsconfig": "workspace:*", "@changesets/cli": "^2.27.10", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@kirklin/eslint-config": "^2.7.0", "@types/fs-extra": "^11.0.4", "@types/node": "^22.9.3", "@types/nprogress": "^0.2.3", "@unocss/eslint-plugin": "^0.64.1", "@vitest/coverage-v8": "^2.1.5", "bumpp": "^9.8.1", "cross-env": "^7.0.3", "directory-tree": "^3.5.2", "dotenv": "^16.4.5", "eslint": "^9.15.0", "eslint-plugin-format": "^0.1.2", "esno": "^4.8.0", "fs-extra": "^11.2.0", "husky": "^9.1.7", "jsdom": "^25.0.1", "lint-staged": "^15.2.10", "npm-run-all": "^4.1.5", "rimraf": "^6.0.1", "scripts": "workspace:*", "taze": "^0.18.0", "treeify": "^1.1.0", "tsup": "^8.3.5", "typescript": "^5.7.2", "unbuild": "^2.0.0", "vite": "^5.4.11", "vitest": "^2.1.5"}, "lint-staged": {"**/*": "eslint --fix"}, "volta": {"node": "18.20.4"}}