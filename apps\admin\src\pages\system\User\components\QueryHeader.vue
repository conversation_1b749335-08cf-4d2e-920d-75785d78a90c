<template>
  <NPageHeader>
    <NFlex class="pr" justify="end">
      <NFlex class="pa left-0">
        <slot name="actions" />
      </NFlex>
      <NFlex class="!w-75%" justify="end">
        <NForm inline class="!w-25%" :model="formModel" :show-label="false" :show-feedback="false">
          <NGrid cols="1">
            <NGridItem>
              <NFormItem path="username">
                <NInput
                  v-model:value="formModel.username"
                  placeholder="请输入账号"
                  clearable
                  @clear="handleClear"
                  @keyup.enter="handleSearch"
                />
              </NFormItem>
            </NGridItem>
          </NGrid>
        </NForm>
        <slot name="search" />
      </NFlex>
    </NFlex>
  </NPageHeader>
</template>

<script lang="ts" setup>
  defineOptions({
    name: "QueryHeader",
    inheritAttrs: false,
  });

  const emit = defineEmits<{
    (e: "search"): void;
  }>();

  const modelValue = defineModel("value", { type: Object, default: null });

  const INIT_QUERY_CRITERIA = {
    username: null,
  };

  const formModel = ref({ ...INIT_QUERY_CRITERIA });

  onBeforeMount(initData);

  watch(modelValue, () => {
    initData();
  }, { deep: true });

  /**
   * 初始化表单数据，将 modelValue 的值同步到 formModel。
   * 如果 modelValue 中没有对应字段，则使用 INIT_QUERY_CRITERIA 的默认值。
   */
  function initData() {
    Object.keys(INIT_QUERY_CRITERIA).forEach((key) => {
      formModel.value[key] = modelValue?.value?.[key] || INIT_QUERY_CRITERIA[key];
    });
  }

  watch(formModel, (newVal) => {
    modelValue.value = { ...modelValue.value, ...newVal };
  }, { deep: true });

  /**
   * 清空输入框时触发，延迟执行 search 事件。
   * 使用 useTimeout 保证 UI 响应后再触发。
   */
  function handleClear() {
    useTimeout(0, {
      callback: () => {
        emit("search");
      },
    });
  }

  /**
   * 按下回车或主动搜索时触发，向父组件派发 search 事件。
   */
  function handleSearch() {
    emit("search");
  }
</script>

<style scoped>

</style>
