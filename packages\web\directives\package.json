{"name": "@celeris/directives", "type": "module", "version": "0.0.3", "description": "directives for <PERSON><PERSON><PERSON>", "author": "<PERSON> (https://github.com/kirklin)", "license": "MIT", "homepage": "https://github.com/kirklin/celeris-web", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "./index.ts", "module": "./index.ts", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --stub", "clean": "pnpm rimraf node_modules && pnpm rimraf dist", "prepublishOnly": "npm run build", "postinstall": "npm run build"}, "peerDependencies": {"vue": ">3.0.0"}, "dependencies": {"@celeris/utils": "workspace:*"}, "devDependencies": {"vue": "^3.5.13"}}