<template>
  <NPageHeader class="p-4">
    <!-- <template #extra> -->
      <NFlex class="pr" justify="space-between">
        <!-- 根据tab是否是租户列表显示添加按钮 -->
        <div class="!w-25%" >
          <NButton type="primary" @click="handleAdd" >
            新增租户
          </NButton>
        </div>
        <NFlex>
          <NForm inline :model="formModel" label-placement="left" :show-feedback="false">
            <NFormItem path="tenant_id">
              <NInput v-model:value="formModel.name" placeholder="请输入租户名称" clearable @keyup.enter="handleSearch" />
            </NFormItem>
            <NFormItem>
              <NButton type="primary" @click="handleSearch">
                查询
              </NButton>
            </NFormItem>
          </NForm>
        </NFlex>
      </NFlex>
    <!-- </template> -->
  </NPageHeader>
</template>

<script lang="ts" setup>
  import { INIT_QUERY_CRITERIA } from "../../config";

  defineOptions({
    name: "QueryHeader",
    inheritAttrs: false,
  });

  const props = withDefaults(defineProps<Props>(), {
    activeTab: "",
  });

  const emit = defineEmits<{
    (e: "search"): void;
    (e: "add"): void;
  }>();

  interface Props {
    activeTab?: string;
  }

  const modelValue = defineModel("value", { type: Object, default: null });

  const formModel = ref({ ...INIT_QUERY_CRITERIA });

  // ========== 计算属性 ==========
  const showAddButton = computed(() => {
    return props.activeTab === "TenantList";
  });

  onBeforeMount(initData);

  watch(modelValue, () => {
    initData();
  }, { deep: true });

  function handleSearch() {
    emit("search");
  }

  function handleAdd() {
    emit("add");
  }

  function initData() {
    Object.keys(INIT_QUERY_CRITERIA).forEach((key) => {
      formModel.value[key] = modelValue?.value?.[key] || INIT_QUERY_CRITERIA[key];
    });
  }

  watch(formModel, (newVal) => {
    modelValue.value = { ...modelValue.value, ...newVal };
  }, { deep: true });
</script>

<style scoped>

</style>
