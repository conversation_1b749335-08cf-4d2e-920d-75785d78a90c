{"routes": {"components": {"components": "Component Example", "table": "Table Component", "headlessTable": "Headless Table", "headlessTableBasic": "Basic Table", "headlessTablePagination": "Pagination Table"}, "chat": {"chat": "Cha<PERSON>"}, "profile": {"profile": "Profile"}, "design": {"design": "Design", "palette": "Palette"}, "dashboard": {"dashboard": "Dashboard"}, "directives": {"directives": "Directive Examples", "permission": "Permission Directive", "copy": "Copy Directive", "ripple": "Ripple Directive"}, "iframe": {"iframe": "Embedded Webpage", "githubInternal": "GitHub (Embedded)", "GitHubExternal": "<PERSON><PERSON><PERSON><PERSON> (External)", "viteInternal": "Vite Documentation (Embedded)", "ViteExternal": "Vite Documentation (External)"}, "permission": {"permission": "Permission Test", "authPageA": "Test Page A", "authPageB": "Test Page B", "frontend": "Frontend-based", "backend": "Backend-based", "pageAuth": "Page Permissions", "buttonAuth": "Button Permissions", "role": "Role Permissions", "directive": "Directive Permissions"}, "result": {"result": "Result Page", "success": "Success Page", "fail": "<PERSON><PERSON>"}}, "layouts": {"header": {"toggleCollapsed": "Toggle <PERSON>", "toggleFullScreen": "Toggle Fullscreen", "switchLocale": "Switch Language", "settingDrawer": "Setting<PERSON> Drawer", "openSettingDrawer": "Open Settings Drawer", "projectSetting": "Project Settings", "darkMode": "Dark Mode", "followSystem": "Follow System", "systemDefault": "System Default", "systemTheme": "System Theme", "themeMode": "Theme Mode", "lightMode": "Light Mode", "colorWeak": "Color Weak Mode", "themeConfig": {"title": "Theme Configuration", "copyConfigButton": "Copy Current Configuration", "resetConfigButton": "Reset Current Configuration", "message": {"copyConfigSuccess": "Configuration copied successfully!", "resetConfigSuccess": "Configuration reset successfully!"}}, "transitionSetting": {"title": "Animation Settings", "enableTransition": "Enable Animation", "enableProgressBar": "Enable Progress Bar", "enablePageLoadingTransition": "Enable Page Loading Animation", "routeTransition": "Route Transition"}}, "userInfo": {"userInformation": "User Information", "greeting": "Hello", "rolesList": "Role List: {roles}", "updatePassword": "Update Password", "logoutButton": "Logout"}, "logoutConfirmation": {"title": "Warning", "content": "Are you sure you want to log out?", "positiveText": "Logout", "negativeText": "Cancel", "onNegativeClickMessage": "Logout canceled", "onPositiveClickMessage": "Logout successful"}}, "searchDialog": {"searchPlaceholder": "Search", "noResultsFound": "Sorry, no results found for { search }", "toSelectTooltip": "to select", "toNavigateTooltip": "to navigate", "applications": "Applications", "chatBot": "ChatBot", "action": "Action", "actions": "Actions", "shortcut": "Shortcut", "go": "Go to "}, "page": {"login": {"title": "<PERSON><PERSON>", "form": {"username": {"label": "Username", "placeholder": "Enter your username", "error": "Username cannot be empty"}, "password": {"label": "Password", "placeholder": "Enter your password", "error": "Password cannot be empty", "validator": "12~18 digits, uppercase, lowercase, digits and {'!@#$%^&*()'} combination"}, "confirmPassword": {"label": "Confirm Password", "placeholder": "Enter your confirm password", "error": "Confirm password cannot be empty", "validator": "Password is not same!"}, "forgotPassword": {"label": "Username", "requiredError": "Username is required"}, "captcha": {"label": "<PERSON><PERSON>", "requiredError": "Captcha is required"}, "signText": {"updatePassword": "Update and enter the system", "forgotPassword": "Back to Sign in"}, "welcomeBackTitle": "Welcome Back", "helloTitle": "Hello", "updatePasswordTitle": "Update Password", "forgotPasswordTitle": "Forgot Password", "greetingText": "The Performance Data Platform is a highly performant and customizable efficiency data platform built with Vue 3, Vite, and TypeScript.", "sendCaptcha": "Send Captcha", "captchaSentMessage": "<PERSON><PERSON> sent", "confirmReset": "Confirm reset", "sendResetLinkButton": "Send Reset Link", "resetLinkSentMessage": "Reset Link sent", "signUp": "Sign up", "backToSignIn": "Back to Sign in", "signIn": "Sign in", "remember": "Remember me", "loginButton": "Sign in", "registerButton": "Create an account", "forgetPassword": "Forgot password", "incorrectAccountOrPassword": "Incorrect account or password!"}, "notification": {"loginSuccessMessage": "Login successful", "welcomeBackMessage": "Welcome back, {username}!"}}, "copyDirective": {"copy": "Copy", "copyDirective": "Copy Directive Example", "copyPlaceholder": "Please enter the content to copy", "copySuccess": "Copy successful", "copyError": "Co<PERSON> failed"}, "rippleDirective": {"ripple": "<PERSON><PERSON><PERSON>", "rippleDirective": "Ripple Directive Example", "description1": "Ripples are state layers used to communicate the status of a component or interactive element.", "description2": "A state layer is a semi-transparent covering on an element that indicates its state."}, "permission": {"permissionMode": {"currentMode": "Current Permission Mode", "backendMode": "Backend Permission Mode", "frontendMode": "Frontend Role Permission Mode", "toggleMode": "Toggle Permission Mode"}, "pageTitles": {"frontend": "Frontend Permission Example", "backend": "Backend Permission Example", "button": "Button Permission Control"}, "roleButtonText": "Visible with {role} Role", "codeButtonText": "Visible with [{code}] Code", "currentPermissionMode": "Current Permission Mode", "currentRole": "Current Role", "currentCode": "Current Code", "clickToSeeButtonChange": "Click to see button changes", "clickToSeeLeftMenuChange": "Click to see left menu changes", "frontendPermissionSwitchTitle": "Permission Switching (Please switch permission mode to Frontend Role mode first)", "backendPermissionSwitchTitle": "Permission Switching (Please switch permission mode to Backend Role mode first)", "componentWayTitle": "Component-based Permission Check (You can register it globally if needed)", "functionWayTitle": "Function-based Permission Check (Suitable for filtering within functions)", "directiveWayTitle": "Directive-based Permission Check (This method cannot dynamically modify permissions)", "backendLeftMenuChangeTitle": "Click to see left menu changes (Must be in Backend Role mode to test the functionality displayed on this page)"}, "headlessTable": {"pageTitles": {"basic": "Basic Table"}}, "result": {"status": {"error": "Error", "success": "Success"}, "failPage": {"title": "Error Sending Content", "subTitle": "Sorry, there was a problem sending the content.", "buttons": {"home": "Return to Home", "back": "Go Back"}, "errorHeader": "There might be the following issues when sending content:", "networkIssue": "Network connection problem", "checkNetwork": "Check network >", "messageTooLong": "Message is too long, please simplify", "viewHelp": "View help >"}, "successPage": {"title": "Operation Successful", "subTitle": "Content generated by ChatGPT has been successfully created.", "buttons": {"home": "Return to Home"}, "contentHeader": "AIGC Generated Content:", "generatedTimeLabel": "Generated Time", "generatorLabel": "Generator", "generatedContent": "Generated content will be displayed here", "contentLabel": "Content", "step1": "Input Content", "step2": "Generating", "step2Content": "ChatGPT is generating content, please wait a moment.", "step3": "Under Review", "step3Content": "Generated content is under review and will be approved shortly.", "step4": "Completed"}}}}