<template>
  <NModal
    v-model:show="visible"
    :block-scroll="false"
    preset="dialog"
    class="w-xl bg-white"
    :title="modalTitle"
    style="width: 800px; position: fixed; top: 0px; left: 50%; transform: translateX(-50%)"
    @positive-click="handleClose"
    @after-leave="handleClose"
  >
    <NDivider class="!my-2" />
    <div v-if="detailReport" class="max-h-70vh overflow-y-auto">
      <MdPreview :md-data="detailReport"></MdPreview>
    </div>
  </NModal>
</template>

<script setup lang="ts">
  import type { ModelResultExpanded } from "-/behavior";
  import { MdPreview } from "~/component/MdPreview";

  defineOptions({
    name: "ModelDetailModal",
    inheritAttrs: false,
  });

  const props = withDefaults(defineProps<{
    show: boolean;
    data?: ModelResultExpanded | null;
  }>(), {
    show: false,
    data: () => ({}) as ModelResultExpanded,
  });

  const emit = defineEmits<{
    "update:show": [show: boolean];
  }>();

  const visible = ref(props.show);

  const detailReport = ref<string>("");

  const modalTitle = computed(() => {
    return props.data ? `${props.data.userName} 的行为解读详情（${props.data.reportRange}）` : "解读详情";
  });

  watch(
    () => props.show,
    async (val) => {
      visible.value = val;
      detailReport.value = props.data?.report || "";
    },
  );

  /**
   * 关闭弹窗并通知父组件更新显示状态
   * @function
   * @returns {void} 无返回值
   * @emits update:show 通知父组件关闭弹窗
   */
  function handleClose() {
    emit("update:show", false);
  }
</script>
