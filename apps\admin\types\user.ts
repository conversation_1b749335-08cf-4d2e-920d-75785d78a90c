import type { TenantInfo } from "./tenant";

export interface UserInfo {
  state: number;
  id: number | null;
  username: string;
  nickname: string;
  email: string | null;
  telephone: string | null;
  password_expired?: string | null;
  account_expired?: string | null;
  tenant?: TenantInfo[]; // 用户可用的租户列表
}

export interface UserResponse {
  done_token: string;
  user: UserInfo;
}

export interface UserParam {
  nickname?: string;
  username?: string;
  telephone?: string;
  email?: string;
  groupid?: string;
}

export interface UserListItem {
  id: number;
  username: string;
  nickname: string;
  email: string;
  telephone: string;
  preserved?: number;
  state: number;
  // preserved?: 0 | 1 | 2 | 3
  job_title: string;
  hr_com: string;
  position: string;
  group_id: string;
  group: string;
  group_leader: string;
  group_leader_name: string;
  group_lines: string[];
  create_time: string;
  entry_time: string;
  account_expired: string;
  password_expired: string;
}

export type UserListExpandedItem = UserListItem & {
  key: string;
  deleteLoading: boolean;
};

export interface SimpleUserItem {
  username: string;
  nickname: string;
  email: string | null;
}

/**
 * @description 覆盖项目数据结构
 */
export interface CoverageProjectItem {
  /** 项目名称 */
  name: string;
  /** 用户角色类型 */
  userType: string;
}
