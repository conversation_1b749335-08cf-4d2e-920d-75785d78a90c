import type { PermissionInfo } from "-/permission";
import type { Menu } from "@celeris/types";
import type { RouteRecordRaw } from "vue-router";
import { PermissionModeConstants } from "@celeris/constants";
import { filterTreeKeepParents, flattenMultiLevelRoutes, transformRouteToMenu } from "@celeris/utils";
import { getPermissions } from "~/apis/internal/permission";
import { asyncRoutes } from "~/router/routes";
import { useAppStore } from "~/store/modules/app";
import { APP_PERMISSION_STORE_ID } from "../constants";

// 定义权限状态接口
// Define the interface for permission state
interface PermissionState {
  permissionInfo: PermissionInfo | null;
  // 当前路由是否是动态添加的标识
  // Flag indicating whether the current route is dynamically added
  shouldAddRouteDynamically: boolean;
  // 上一次菜单更新的时间戳，用于判断是否需要更新菜单
  // Timestamp of the last menu update, used to determine if the menu needs to be updated
  lastMenuBuildTime: number;
  // 前端控制的菜单列表，通过用户角色来过滤
  // Menu list controlled by frontend, filtered by user roles
  frontendMenuList: Menu[];
  // 后台控制的菜单列表，用于动态生成路由表
  // Menu list controlled by backend for dynamic route generation
  backendMenuList: Menu[];
}

export const usePermissionStore = defineStore({
  id: APP_PERMISSION_STORE_ID,
  state: (): PermissionState => ({
    permissionInfo: null,
    // 当前路由是否是动态添加的标识
    // Flag indicating whether the current route is dynamically added
    shouldAddRouteDynamically: false,
    // 上一次菜单更新的时间戳，用于判断是否需要更新菜单
    // Timestamp of the last menu update, used to determine if the menu needs to be updated
    // 初始值为0是为了立即刷新菜单
    // Initial value of 0 is to immediately refresh the menu
    lastMenuBuildTime: 0,
    // 前端控制的菜单列表，通过用户角色来过滤
    // Menu list controlled by frontend, filtered by user roles
    frontendMenuList: [],
    // 后台控制的菜单列表，用于动态生成路由表
    // Menu list controlled by backend for dynamic route generation
    backendMenuList: [],
  }),
  getters: {
    // 获取权限信息
    // Get permission information
    getPermissionInfo(state): PermissionInfo | null {
      return state.permissionInfo;
    },
    // 获取前端控制的菜单列表
    // Get the menu list controlled by frontend
    getFrontendMenuList(state): Menu[] {
      return state.frontendMenuList;
    },
    // 获取后台控制的菜单列表
    // Get the menu list controlled by backend
    getBackendMenuList(state): Menu[] {
      return state.backendMenuList;
    },
    // 获取上一次菜单更新的时间戳
    // Get the timestamp of the last menu update
    getLastMenuBuildTime(state) {
      return state.lastMenuBuildTime;
    },
    // 获取当前路由是否是动态添加的标识
    // Get the flag indicating whether the current route is dynamically added
    getShouldAddRouteDynamically(state): boolean {
      return state.shouldAddRouteDynamically;
    },
    // 获取权限信息中的菜单列表
    getMenu(state) {
      return state.permissionInfo?.menu || [];
    },
    // 获取权限信息中的按钮列表
    getBtn(state) {
      return state.permissionInfo?.btn || [];
    },
    // 获取权限信息中的团队列表
    getTeam(state) {
      return state.permissionInfo?.teams || [];
    },
    // 获取权限信息中的标签列表
    getTab(state) {
      return state.permissionInfo?.tabs || [];
    },
  },
  actions: {
    // 设置权限信息
    setPermissionInfo(permissionInfo: PermissionInfo) {
      this.permissionInfo = permissionInfo;
    },
    // 获取用户权限
    async getUserPermissions() {
      const res = await getPermissions();
      this.setPermissionInfo({ ...res });
    },

    // 设置前端控制的菜单列表
    // Set the menu list controlled by frontend
    setFrontendMenuList(menuList: Menu[]) {
      this.frontendMenuList = menuList;
    },
    // 设置后台控制的菜单列表
    // Set the menu list controlled by backend
    setBackendMenuList(menuList: Menu[]) {
      this.backendMenuList = menuList;
      menuList.length > 0 && this.setLastMenuBuildTime();
    },
    // 设置上一次菜单更新的时间戳
    // Set the timestamp of the last menu update
    setLastMenuBuildTime(time?: number) {
      this.lastMenuBuildTime = time || Date.now();
    },
    // 设置当前路由是否是动态添加的标识
    // Set the flag indicating whether the current route is dynamically added
    setShouldAddRouteDynamically(flag: boolean) {
      this.shouldAddRouteDynamically = flag;
    },
    // 清空权限状态
    // Clear permission state
    resetPermissionState() {
      this.permissionInfo = null;
      this.frontendMenuList = [];
      this.backendMenuList = [];
      this.lastMenuBuildTime = 0;
      this.shouldAddRouteDynamically = false;
    },

    async buildRoutesAction(): Promise<RouteRecordRaw[]> {
      const appStore = useAppStore();
      let routes: RouteRecordRaw[] = [];

      const permissionMode = appStore.getProjectSetting.permissionMode || PermissionModeConstants.ROUTE_MAPPING;

      // 获取菜单列表
      const menuList: string[] = this.getMenu || [];

      // 过滤路由
      const routeFilter = (route: RouteRecordRaw) => {
        const { meta, name } = route;
        const { shouldVerifyVisiblePermission } = meta || {};
        if (shouldVerifyVisiblePermission) {
          return menuList.includes(`menu-${String(name)}`);
        }
        return true;
      };

      // Filter out routes with meta.shouldIgnoreRoute = true
      const routeFilterIgnore = (route: RouteRecordRaw) => {
        const { meta } = route;
        const { shouldIgnoreRoute } = meta || {};
        return !shouldIgnoreRoute;
      };

      const disableMenuList = (menuList: Menu[]) => {
        const llm = this.getPermissionInfo?.llm;
        menuList.forEach((menu) => {
          if (menu.meta?.shouldVerifyDisablePermission) {
            menu.shouldDisabled = !llm;
          }
          if (menu.children?.length) {
            disableMenuList(menu.children);
          }
        });
      };

      switch (permissionMode) {
        case PermissionModeConstants.ROUTE_MAPPING: {
          // Filter non-top-level routes
          routes = filterTreeKeepParents(asyncRoutes, routeFilter);
          // Filter top-level routes
          routes = routes.filter(routeFilter);
          // Convert routes to menu
          const menuList = transformRouteToMenu(routes, true);
          // Remove routes with meta.shouldIgnoreRoute = true
          routes = filterTreeKeepParents(routes, routeFilterIgnore);
          routes = routes.filter(routeFilterIgnore);
          // Sort menu items by meta.orderNo
          menuList.sort((a, b) => {
            return (Number(a.meta?.orderNumber) || 0) - (Number(b.meta?.orderNumber) || 0);
          });
          // Disable menu items by meta.shouldVerifyDisablePermission
          disableMenuList(menuList);
          // Set frontend menu list
          this.setFrontendMenuList(menuList);
          // Convert multi-level routing to level 2 routing
          routes = flattenMultiLevelRoutes(routes);
          break;
        }
      }
      return routes;
    },
  },
});

// Need to be used outside the setup
// 需要在设置之外使用
export function usePermissionStoreWithOut() {
  return usePermissionStore(store);
}
