<template>
  <NModal v-bind="$attrs" preset="card" :bordered="false" :segmented="false">
    <div class="h-full bg-white border-rd-[var(--n-border-radius)]" :style="{ height }">
      <NSpin content-class="h-full" :show="loading">
        <NEmpty v-if="emptyFunc()" class="h-full justify-center" />
        <slot v-else />
      </NSpin>
    </div>

    <template v-for="(slot, key) in slotsWithoutDefault" #[key]="slotData">
      <slot :name="key" v-bind="slotData || {}" />
    </template>
  </NModal>
</template>

<script lang="ts" setup>
  /*
  * 弹窗定义
  */

  defineOptions({
    name: "CardModal",
    inheritAttrs: false,
  });

  /*
   * 弹窗传参
   */

  withDefaults(defineProps<{
    loading?: boolean;
    emptyFunc?: () => boolean;
    height?: string;
  }>(), {
    loading: false,
    // 默认不显示空状态
    emptyFunc: () => true,
    height: "auto",
  });

  /*
   * 弹窗插槽
   */

  const slots = useSlots();
  /*
   * 弹窗插槽
   */
  const slotsWithoutDefault = computed(() => {
    return Object
      .keys(slots)
      .filter(key => key !== "default")
      .reduce((prev, curr) => {
        prev[curr] = slots[curr];
        return prev;
      }, {});
  });
</script>

<style scoped>
:deep(.ca-spin-container) {
  height: 100%;
}
</style>
