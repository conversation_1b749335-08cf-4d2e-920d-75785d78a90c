import type { RouteLocationNormalized, RouteRecordName } from "vue-router";
import { PageConstants } from "@celeris/constants";
import { isGreaterOrEqual2xl } from "@celeris/hooks";
import { takeRight, uniqBy } from "@celeris/utils";
import { defineStore } from "pinia";
import { PAGE_NOT_FOUND_ROUTE, REDIRECT_ROUTE } from "~/router/routes/basic";
import { APP_TABS_STORE_ID } from "../constants";

interface AppTabsState {
  tabs: Tab[];
  pinnedTabs: Tab[];
  maxVisibleTabs: number;
}
export interface Tab {
  name: RouteRecordName;
  fullPath: string;
  title: string;
}
export const useTabsStore = defineStore({
  id: APP_TABS_STORE_ID,
  persist: [{
    storage: localStorage,
  }, {
    storage: sessionStorage,
  }],
  /*
   * 标签页
   * Tabs
   */
  state: (): AppTabsState => ({
    tabs: [],
    pinnedTabs: [],
    maxVisibleTabs: 3,
  }),
  getters: {
    // 获取标签页列表
    getTabsList(state): Tab[] {
      return state.tabs;
    },
    // 获取当前激活的tab
    getLimitTabsList(state): Tab[] {
      if (isGreaterOrEqual2xl.value) {
        state.maxVisibleTabs = 3;
      } else {
        state.maxVisibleTabs = 3;
      }
      return takeRight(
        state.tabs.filter(tab => state.pinnedTabs.findIndex(p => p.fullPath === tab.fullPath) === -1).reverse(),
        state.maxVisibleTabs,
      );
    },
    // 获取固定标签页列表
    getPinnedTabsList(state): Tab[] {
      return state.pinnedTabs;
    },
  },
  actions: {
    /**
     * 添加标签页
     * Add tab
     *
     * @param route 路由对象
     */
    addTab(route: RouteLocationNormalized) {
      const { path, name, meta } = route;
      if (!name || path === PageConstants.ERROR_PAGE || path === PageConstants.BASE_LOGIN || [REDIRECT_ROUTE.name, PAGE_NOT_FOUND_ROUTE.name].includes(name)) {
        return;
      }
      const title = meta?.title as string || name.toString().split("-").at(-1);
      if (title) {
        const newTab: Tab = { name, fullPath: route.fullPath, title };
        this.tabs = uniqBy([newTab, ...this.tabs], "fullPath");
      }
    },
    /**
     * 关闭标签页
     * Close tab
     *
     * @param isPinned 是否是固定标签页
     * @param tab 要关闭的标签页
     */
    close(isPinned: boolean, tab: Tab) {
      if (isPinned) {
        this.pinnedTabs = this.pinnedTabs.filter(currentTab => currentTab.fullPath !== tab.fullPath);
      }
      this.tabs = this.tabs.filter(currentTab => currentTab.fullPath !== tab.fullPath);
    },
    /**
     * 关闭标签页
     * Close tab
     *
     * @param tab 要关闭的标签页
     */
    closeTab(tab: Tab) {
      this.close(false, tab);
    },
    /**
     * 关闭固定标签页
     * Close pinned tab
     *
     * @param tab 要关闭的标签页
     */
    closePinnedTab(tab: Tab) {
      this.close(true, tab);
    },
    /**
     * 固定标签页
     * Pin tab
     *
     * @param tab 要固定的标签页
     */
    pinnedTab(tab: Tab) {
      const isPresent = this.pinnedTabs.some(pinnedTab => pinnedTab.fullPath === tab.fullPath);
      if (!isPresent) {
        this.pinnedTabs = [tab, ...this.pinnedTabs];
      }
      return true;
    },
    /**
     * 重置标签页状态
     * Reset tabs state
     */
    resetTabsState() {
      this.tabs = [];
      this.pinnedTabs = [];
    },
  },
});

// Need to be used outside the setup
export function useTabsStoreWithOut() {
  return useTabsStore(store);
}
