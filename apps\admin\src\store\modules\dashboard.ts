import { getProjectIds, queryCustomPro } from "~/apis/internal/dashboard";
import { userTreeApi } from "~/apis/internal/user";
import { usePermissionStoreWithOut } from "~/store/modules/permission";

export const useDashboardStore = defineStore({
  id: "APP_Dashboard_STORE",
  persist: {
    storage: localStorage,
  },
  state: () => ({
    projects: [],
    teams: [],
    teamsLoading: false,
    loading: false,
    conditionsOpenFlag: true,
    custom_properties: {
      职责类型: [],
      职级: [],
    },
    selectedRow: null,
    pageSize: 300,
    conditions: {
      team_ids: [],
      project_ids: [],
      account_ids: [],
      role_classes: [],
      role_types: [],
      delegation: null,
    },
  }),
  getters: {
    // 获取项目列表
    getProjects: state => state.projects,
    // 获取默认团队列表
    getDefaultTeams: state => state.teams.map((item: any) => item.id),
    // 获取团队列表
    getTeams: state => state.teams,
    // 获取团队列表加载状态
    getTeamsLoading: state => state.teamsLoading,
    // 获取加载状态
    getLoading: state => state.loading,
    // 获取条件展开状态
    getDutyTypes: state => state.custom_properties["职责类型"] || [],
    // 获取条件
    getConditions: state => state.conditions,
    // 获取每页条数
    getPageSizes: state => state.pageSize,
  },
  actions: {
    // 设置条件
    setTeamids(ids) {
      this.conditions.team_ids = ids;
    },
    // 设置项目列表
    setProjectIds(ids) {
      this.conditions.project_ids = ids;
    },
    // 设置选中行
    setSelectedRow(row) {
      this.selectedRow = row;
    },
    // 设置项目列表
    setProjects(projects: string[]) {
      this.projects = projects;
    },
    // 设置团队列表
    setTeams(teams) {
      this.teams = teams;
    },
    // 设置团队列表加载状态
    setTeamsLoading(loading: boolean) {
      this.teamsLoading = loading;
    },
    // 设置加载状态
    setLoading(loading: boolean) {
      this.loading = loading;
    },
    // 设置自定义属性
    setCustomProperties(data) {
      this.custom_properties = data;
    },
    // 设置每页条数
    setPageSize(size: number) {
      this.pageSize = size;
    },
    // 设置条件
    async getAllTeams() {
      this.setTeamsLoading(true);
      const permissionStore = usePermissionStoreWithOut();
      await permissionStore.getUserPermissions();
      const teamIds = permissionStore.getTeam.join(",");
      if (teamIds) {
        // TODO 使用工具函数
        const res = await userTreeApi({ team_ids: teamIds });
        this.setTeams(res);
        this.setTeamsLoading(false);
        this.setTeamids(res.map((item: any) => item.id));
      } else {
        this.setTeams([]);
      }
    },
    // 获取项目列表
    async getTreeProjects() {
      try {
        const res = await getProjectIds();
        const projects = res?.tree || [];
        this.setProjects(projects);
        this.setProjectIds(projects.map((item: any) => item.id));
      } catch (error) {
        this.setProjects([]);
      }
    },
  },
});
