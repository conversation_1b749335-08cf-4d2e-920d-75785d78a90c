<template>
  <PageWrapper :use-default-padding="false">
    <QueryHeader
      v-model:value="queryCriteria"
      :active-tab="activeTab"
      @add="handleAdd"
      @search="handleSearch"
    />
    <TenantManagementTab
      :query-criteria="queryCriteria"
      @tab-change="handleTabChange"
      @add-tenant="handleAddTenant"
    />
  </PageWrapper>
</template>

<script setup lang="ts">
  import type { QueryCriteria } from "-/tenant";
  import PageWrapper from "~/component/PageWrapper/src/PageWrapper.vue";
  import QueryHeader from "./components/QueryHeader/index.vue";
  import TenantManagementTab from "./components/TenantManagementTab/index.vue";

  const queryCriteria = ref<QueryCriteria>({});
  const activeTab = ref("TenantList");
  const addTenantTrigger = ref(0);
  const searchTrigger = ref(0);

  function handleTabChange(tab: string) {
    activeTab.value = tab;
  }

  function handleAdd() {
    // 触发新增租户事件
    addTenantTrigger.value++;
  }

  function handleSearch() {
    // 触发搜索事件，重新加载表格数据
    searchTrigger.value++;
  }

  function handleAddTenant() {
    // 这里可以添加新增租户的逻辑
    // 比如打开新增租户的弹窗
    // 暂时不做任何操作，由子组件处理
  }

  // 提供触发器给子组件
  provide("addTenantTrigger", addTenantTrigger);
  provide("searchTrigger", searchTrigger);
</script>
