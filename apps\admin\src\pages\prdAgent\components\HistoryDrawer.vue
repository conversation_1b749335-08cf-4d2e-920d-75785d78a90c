<template>
  <NDrawer
    v-model:show="visible"
    placement="left"
    width="400px"
    :mask-closable="true"
  >
    <NDrawerContent title="历史记录" closable>
      <!-- <NFlex vertical class="h-full"> -->
        <!-- 历史记录列表 -->
         <NScrollbar @scroll="handleScroll">
          <NFlex vertical>
            <NSpin :show="loading && list.length === 0">
              <div v-if="list.length > 0" class="space-y-4">
                <div
                  v-for="item in list"
                  :key="item.id"
                  class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                  @click="selectItem(item)"
                >
                  <div class="flex justify-between items-start mb-2">
                    <h3 class="text-base font-medium text-gray-900 dark:text-gray-100">
                      {{ item.title }}
                    </h3>
                    <span class="text-xs text-gray-500 dark:text-gray-400">{{ formatDate(item.created_at) }}</span>
                  </div>
                  <p class="text-sm text-gray-600 dark:text-gray-300 line-clamp-1">
                    {{ item.description }}
                  </p>
                  <div class="mt-2">
                    <p class="text-xs text-gray-600 dark:text-gray-400 line-clamp-3">
                      {{ item.requirements }}
                    </p>
                  </div>
                </div>

                <!-- 加载更多指示器 -->
                <div v-if="loading" class="py-4 text-center">
                  <NSpin size="small" />
                  <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">加载中...</span>
                </div>

                <!-- 无更多数据提示 -->
                <div v-if="!hasMore && list.length > 0" class="py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                  没有更多了
                </div>
              </div>

              <!-- 空状态 -->
              <NEmpty v-else-if="!loading" description="暂无历史记录" />
            </NSpin>
          </NFlex>
         </NScrollbar>
      <!-- </NFlex> -->
    </NDrawerContent>
  </NDrawer>
</template>

<script setup lang="ts">
  import { formatToDate } from "@celeris/utils";
  import { NButton, NDrawer, NDrawerContent, NEmpty, NSpin, useMessage } from "naive-ui";
  import { ref, watch } from "vue";
  import { getPrdList } from "~/apis/internal/prd";

  const props = defineProps<Props>();

  const emits = defineEmits<{
    (e: "update:visible", value: boolean): void;
    (e: "select", item: HistoryItem): void;
  }>();

  // 防抖函数
  function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
    let timeout: NodeJS.Timeout;
    return ((...args: any[]) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    }) as T;
  }

  interface HistoryItem {
    id: string;
    user_id: number;
    tenant_id: number;
    title: string;
    description: string;
    requirements: string;
    generated_prd: string;
    status: "draft" | "published" | "archived";
    created_at: string;
    updated_at: string;
  }

  interface PrdListResponse {
    total: number;
    page_index: number;
    page_size: number;
    value: HistoryItem[];
  }

  interface Props {
    visible: boolean;
  }

  const message = useMessage();

  // 历史记录相关状态
  const visible = ref(false);
  const list = ref<HistoryItem[]>([]);
  const loading = ref(false);
  const page = ref(1);
  const pageSize = ref(10);
  const hasMore = ref(true);

  // 监听props中的visible属性变化
  watch(() => props.visible, (newValue) => {
    visible.value = newValue;
    if (newValue) {
      // 重置分页状态
      resetAndLoad();
    }
  }, { immediate: true });

  // 监听内部visible状态变化，通知父组件
  watch(() => visible.value, (newValue) => {
    if (props.visible !== newValue) {
      emits("update:visible", newValue);
    }
  });

  // 重置并加载数据
  function resetAndLoad() {
    page.value = 1;
    list.value = [];
    hasMore.value = true;
    // 加载第一页数据
    loadData();
  }

  // 加载历史记录数据
  async function loadData() {
    console.warn("loadData被调用:", {
      loading: loading.value,
      hasMore: hasMore.value,
      currentPage: page.value,
      listLength: list.value.length,
    });

    if (loading.value || !hasMore.value) {
      console.warn("loadData提前返回:", { loading: loading.value, hasMore: hasMore.value });
      return;
    }

    loading.value = true;
    console.warn(`开始加载第${page.value}页数据...`);

    try {
      // 调用真实的PRD列表接口
      const res = await getPrdList({
        page_index: page.value,
        page_size: pageSize.value,
      });

      // 处理真实的API响应数据
      // API返回格式: { total: number, page_index: number, page_size: number, value: HistoryItem[] }
      const response = res as unknown as PrdListResponse;
      const newItems = response.value || [];

      console.warn("API响应数据:", {
        total: response.total,
        page_index: response.page_index,
        page_size: response.page_size,
        newItemsCount: newItems.length,
        currentListLength: list.value.length,
      });

      // 追加数据到列表
      list.value = [...list.value, ...newItems];

      // 根据总数和当前已加载数量判断是否还有更多数据
      const totalLoaded = list.value.length;
      hasMore.value = totalLoaded < response.total;

      console.warn("数据加载完成:", {
        totalLoaded,
        total: response.total,
        hasMore: hasMore.value,
        nextPage: page.value + 1,
      });

      // 更新页码
      page.value++;
    } catch (error) {
      console.error("加载历史记录失败:", error);
      // 发生错误时停止加载更多
      hasMore.value = false;
      message.error("加载历史记录失败，请重试");
    } finally {
      loading.value = false;
    }
  }

  // 处理滚动加载更多（原始函数）
  function _handleScroll(e: Event) {
    const target = e.target as HTMLElement;
    const { scrollTop, scrollHeight, clientHeight } = target;

    // 调试信息
    console.warn("滚动事件触发:", {
      scrollTop,
      scrollHeight,
      clientHeight,
      distanceToBottom: scrollHeight - scrollTop - clientHeight,
      loading: loading.value,
      hasMore: hasMore.value,
      listLength: list.value.length,
    });

    // 当滚动到距离底部50px时加载更多
    const distanceToBottom = scrollHeight - scrollTop - clientHeight;
    if (distanceToBottom < 50 && !loading.value && hasMore.value) {
      console.warn("触发加载更多数据");
      loadData();
    }
  }

  // 防抖处理的滚动函数
  const handleScroll = debounce(_handleScroll, 100);

  // 选择历史记录
  function selectItem(item: HistoryItem) {
    // 发送选择事件
    emits("select", item);

    // 关闭抽屉
    visible.value = false;
  }

  // 格式化日期
  function formatDate(dateString: string) {
    return formatToDate(new Date(dateString), "YYYY-MM-DD HH:mm");
  }
</script>

<style scoped>
/* 历史记录抽屉样式 */
:deep(.n-drawer-content-wrapper) {
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

:deep(.n-drawer-header) {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.n-drawer-body) {
  padding: 0;
}

:deep(.n-drawer-content) {
  padding: 16px;
}


/* 滚动条样式 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
