import { RequestConstants } from "@celeris/constants";

export class SseClient {
  constructor() {}

  /*
   * 发送 SSE POST 请求
   * Send SSE POST request
   */
  post<T = any>(
    url: string,
    options: RequestInit,
    {
      onStart,
      onData,
      onCompleted,
      onError,
    },
  ) {
    this.request(url, { ...options, method: RequestConstants.POST }, {
      onStart,
      onData,
      onCompleted,
      onError,
    });
  }

  /*
   * 发送 SSE 请求
   * Send SSE request
   */
  request(
    url: string,
    options: RequestInit,
    {
      onStart,
      onData,
      onCompleted,
      onError,
    },
  ) {
    const { body } = options;
    if (body) {
      options.body = JSON.stringify(body);
    }

    fetch(url, options)
      .then((res: any) => {
        if (!/^(2|3)\d{2}$/.test(res.status)) {
          onError?.("Server Error");
          return;
        }
        return this.handleStream(res, onStart, onData, onCompleted);
      })
      .catch((e) => {
        onError?.(e);
      });
  }

  handleStream(
    response: Response,
    onStart,
    onData,
    onCompleted,
  ) {
    if (!response.ok) {
      throw new Error("接口请求失败，请稍后重试！");
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = "";

    function read() {
      let hasError = false;
      reader?.read().then((result: any) => {
        if (result.done) {
          onCompleted?.();
          return;
        }

        buffer += decoder.decode(result.value, { stream: true });
        const lines = buffer.split("\n");

        try {
          lines.forEach((message) => {
            if (message.startsWith("data: ")) { // check if it starts with data:
              onData(message.slice(6));
            }
          });
          buffer = lines[lines.length - 1];
        } catch (e) {
          onData("");
          hasError = true;
          onCompleted?.(true);
          return;
        }

        if (!hasError) {
          read();
        }
      });
    }

    onStart?.();

    read();
  }
}
