server {

    listen 80;

    server_name localhost;

    #charset koi8-r;

    access_log /var/log/nginx/host.access.log main;
    client_max_body_size 100m;
    client_header_timeout 1m;
    client_body_timeout 1m;
    proxy_connect_timeout 60s;
    proxy_read_timeout 1m;
    proxy_send_timeout 1m;

    proxy_buffer_size 4k;
    proxy_buffers 4 32k;
    proxy_busy_buffers_size 64k;
    proxy_temp_file_write_size 64k;

    error_log /var/log/nginx/error.log error;

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html;
    }

}

