import type { Paging } from "./common";

export interface Behavior {
  uid: string;
  userName: string;
  gitBehavior?: string;
  woneBehavior?: string;
  joneBehavior?: string;
}

export type BehaviorExpanded = Behavior & {
  key?: string;
  deleteLoading?: boolean;
};

export interface BehaviorParam {
  page_index?: string | number;
  page_size?: string | number;
  from?: string;
  to?: string;
  user?: string;
  path?: string;
}

export interface GetActionDetailParam extends Paging {
  uid?: string;
  src?: string;
  from?: string;
  to?: string;
  commit_by?: string;
}

export interface ModelResult {
  id: string;
  uid: string;
  userName: string;
  reportRange: string;
  report: string;
  analysisTime: string;
  analysisType: string;
  commitBy: string;
}

export type ModelResultExpanded = ModelResult & {
  key?: string;
  deleteLoading?: boolean;
};

export interface AddModelResultParam {
  uid: string;
  userName: string;
  reportRange: string;
  report: string;
}

export interface GetModelResultParam {
  q?: string;
}
