<template>
  <div
    class="w-full h-screen flex flex-col bg-cover bg-no-repeat bg-top-right"
    :style="{ backgroundImage: `url(${loginBg})` }"
  >
    <div class="h-[9%] header__logo">
      <!-- <img :src="loginLogo" alt="登录页 logo" class="mt-[1.2vh] ml-[1.9vw] h-full" /> -->
    </div>
    <NFlex justify="center" align="center" :size="0" class="flex-1 w-full h-[83%]">
      <div class="flex flex-nowrap rounded-[20px] shadow-[0_0_40px_25px_rgba(3,69,236,0.2)] h-[78.7vh] w-[140vh] transition-all duration-1000 m-0 p-0 bg-cover" :style="{ backgroundImage: `url(${loginInner})` }">
        <div class="w-1/2 h-full min-w-[200px] flex flex-col items-center rounded-l-[20px] bg-cover" :style="{ backgroundImage: `url(${loginLeftBackground})` }"></div>
        <div class="w-1/2 h-full min-w-[200px] flex flex-col justify-center items-center">
          <NFlex justify="center" align="center" class="w-[85%] mt-4">
            <NTabs v-model:value="selectMode" justify-content="start" type="line" tab-class="!text-[2.4vh]">
              <NTabPane tab="欢迎登录" name="login">
              </NTabPane>
            </NTabs>
          </NFlex>
          <!-- <NFlex justify="center" align="center" :size="0" class="h-[8%] w-[70%] my-[5px] p-1">
            <NTabs v-model:value="selectMode" type="segment" animated size="large" class="old-login-tabs" tab-class="!font-bold !text-[2.2vh] bg-[#f3f0f0]">
              <NTabPane tab="账密登录" name="login">
              </NTabPane>
              <NTabPane tab="翼认证登录" name="yiAuth">
              </NTabPane>
            </NTabs>
          </NFlex>
          <div v-if="selectMode === 'yiAuth'" class="w-full h-[90%] overflow-hidden">
            <iframe
              id="iframe"
              :src="`https://idaas.ctyun.cn/uias/loginOS?responseType=code&appCode=${uiasId}&resource=web&scope=all&state=${loginChallenge}&redirectUri=${baseUrl}/one/api/v2/user/uias/ssoLogin`"
              scrolling="no" class="w-full h-full border-none"
            />
          </div> -->
          <div v-if="selectMode === 'login'" class="h-[90%] w-[85%]">
            <!-- <div>
              <span class="text-[#3d73f5] text-[2.8vh] cursor-pointer underline decoration-[rgba(0,118,254,0.2)] decoration-[6px]">账号密码登录</span>
            </div> -->
            <NFlex vertical justify="center" align="center" :size="0" class="w-full mt-10px">
              <NForm
                ref="ruleForm"
                :rules="formRules"
                :model="formModel"
                :validate-on-rule-change="false"
                :show-label="false"
                class="mt-[5vh] w-full loginForm"
                size="medium"
              >
                <NFormItem path="email">
                  <NInput
                    v-model:value.trim="formModel.email"
                    name="email"
                    placeholder="输入用户名或邮箱"
                    autocomplete="on"
                  >
                    <template #prefix>
                      <CAIcon icon="tabler:user" alt="用户名图标" />
                    </template>
                  </NInput>
                </NFormItem>
                <NFormItem v-if="!emergency" path="password" :safe-html="true">
                  <NInput
                    v-model:value="formModel.password"
                    name="password"
                    type="password"
                    autocomplete="on"
                    show-password-on="click"
                    placeholder="输入密码"
                  >
                    <template #prefix>
                      <CAIcon icon="tabler:lock" alt="密码图标" />
                    </template>
                    <template #password-visible-icon>
                      <CAIcon icon="tabler:eye" />
                    </template>
                    <template #password-invisible-icon>
                      <CAIcon icon="tabler:eye-closed" />
                    </template>
                  </NInput>
                </NFormItem>
                <NFormItem v-if="emergency || enablePhone" path="code">
                  <NInput v-model:value="formModel.code" placeholder="请输入验证码" @keyup.enter="submitForm">
                    <template #prefix>
                      <CAIcon icon="tabler:shield-lock" alt="验证码图标" />
                    </template>
                  </NInput>
                  <NButton
                    class="ml-[1.85vh] text-[1.85vh] px-[1.8vh] py-[2.7vh]"
                    :disabled="countdown > 0 || codeLoading"
                    type="primary"
                    @click="getMsgCode"
                    @blur="formModel.code = $event.target.value.trim()"
                  >
                    <span v-if="codeLoading">正在获取验证码</span>
                    <span v-else-if="!countdown">点击获取验证码</span>
                    <span v-else>
                      重新获取
                      <span v-if="countdown"> {{ countdown }} 秒</span>
                    </span>
                  </NButton>
                </NFormItem>
                <NFormItem>
                  <NButton class="mt-[10px] h-[6.8vh] w-full text-[2.2vh] shadow-[1px_10px_10px_#aec2e7]" type="primary" :loading="loginLoading" @click="submitForm">
                    登 录
                  </NButton>
                </NFormItem>
                <div v-if="enableFindPwd" class="w-full text-right cursor-pointer text-[2.2vh] mt-[3px] hover:text-[#3491fa]" @click="findPassword">
                  忘记密码
                </div>
                <!-- <NFormItem>
                <div class="flex items-center text-[var(--color-neutral-6)]">
                  <span>其他登录方式：</span>
                  <div class="flex items-center cursor-pointer" @click="handlerOtherLogin">
                    <img :src="loginOtherLogo" class="w-[25px] h-[25px]" />
                    <span>翼认证（通过云台3.0扫码登录，推荐）</span>
                  </div>
                </div>
              </NFormItem> -->
              </NForm>
            </NFlex>
          </div>
          <NFlex v-if="otherLoginMode.includes('oidc')" justify="center" align="center" :size="0" class="w-fit h-[20%] overflow-hidden">
            <span class="text-[1.8vh] text-[#010309]">其他登录方式：</span>
            <NButton class="ml-2 text-[1.8vh]" type="primary" @click="loginWithAone">
              AOneID
            </NButton>
          </NFlex>
        </div>
      </div>
    </NFlex>
    <NFlex vertical justify="end" align="center" :size="2" class="footer h-[8%] pb-2 text-[#96afcc]">
      <span class="text-base">天翼云科技有限公司 版权所有</span>
      <span class="text-xs">design by cute-design</span>
    </NFlex>
  </div>
</template>

<script setup lang="ts">
  import type { ResponseData } from "-/http";
  import type { LoginParam } from "-/login";

  import type { UserResponse } from "-/user";
  import type { FormInst, FormRules } from "naive-ui";
  // private
  import { loginBg, loginInner, loginLeftBackground, loginLogo } from "@celeris/assets";
  import { PageConstants } from "@celeris/constants";
  import { getQueryVariable } from "@celeris/utils";
  // 3rd
  import { getCode, getLoginMode, loginAone } from "~/apis/internal/login";
  import { useUserStore } from "~/store/modules/user";
  import { encryptPassword } from "~/utils/cipher";
  import { handleTenantSelection, initUserInfo } from "./login";
  // import iframeToUias from "http://***********/uias/dist/iframe.js";
  import "~/utils/uias-jssdk.min.js";

  // 类型定义
  interface LoginFormModel {
    email: string;
    password: string;
    code: string;
  }

  interface LoginModeResponse {
    enable_emergency: boolean;
    enable_phone: boolean;
    enable_find_password: boolean;
    others: string[] | null;
    aes_secret: string;
  }

  interface UiasSDKConfig {
    sms: boolean;
    scancode: boolean;
  }

  const router = useRouter();
  const message = useMessage();
  const userStore = useUserStore();

  const loginChallenge = ref<string>("");
  const selectMode = ref<string>("login");
  const baseUrl = import.meta.env.VUE_APP_BASE_URL;
  const uiasId = import.meta.env.VUE_APP_UIAS_ID;

  // 状态变量
  const isCount = ref<boolean>(false);
  const beSelected = ref<boolean>(true);
  const loginLoading = ref<boolean>(false);
  const codeLoading = ref<boolean>(false);
  const countdown = ref<number>(0); // 验证码倒计时
  const countdownInterval = ref<number | null>(null); // 验证码倒计时定时器

  // 登录模式相关变量
  const emergency = ref<boolean>(false);
  const enablePhone = ref<boolean>(false);
  const enableFindPwd = ref<boolean>(false);
  const otherLoginMode = ref<string[]>([]);
  const aes_secret = ref<string>("");

  // 表单引用
  const ruleForm = useTemplateRef<HTMLElement & FormInst>("ruleForm");

  // 表单数据
  const formModel = ref<LoginFormModel>({
    email: "",
    password: "",
    code: "",
  });

  // 校验规则
  const formRules = ref<FormRules>({});

  /**
   * 组件创建时初始化
   * 1. 获取登录模式配置
   * 2. 设置表单验证规则
   */
  onBeforeMount(async () => {
    try {
      const res = await getLoginMode() as LoginModeResponse;
      // 1、紧急情况：用户名 + 短信验证码
      // 2、非紧急：用户名+密码 / 用户名+密码+短信
      const { enable_emergency: emergencyMode, enable_phone: phoneMode, enable_find_password: findPwd, others, aes_secret: secret } = res;
      emergency.value = emergencyMode;
      enablePhone.value = phoneMode;
      enableFindPwd.value = findPwd;
      otherLoginMode.value = others ?? [];
      aes_secret.value = secret;

      formRules.value = {
        email: [{ required: true, message: "请输入用户名", trigger: "blur" }],
        password: [{ required: !emergency.value, message: "请输入密码", trigger: "blur" }],
        code: [{ required: emergency.value || enablePhone.value, message: "请输入验证码", trigger: "blur" }],
      };
    } catch (error) {
      console.error("获取登录模式失败:", error);
    }
  });

  // 生命周期钩子
  onMounted(() => {
    loginChallenge.value = getQueryVariable("login_challenge") || "7kDxC8L1YmFhVKw5";
    const iframeElement = document.getElementById("iframe");
    if (!iframeElement) {
      return;
    }

    const config: UiasSDKConfig = {
      sms: false,
      scancode: true,
    };

    // const iframeInstance = new UiasSDK(iframeElement, config);
    const iframeInstance = new (window as any).UiasSDK(iframeElement, config);
    iframeInstance.init(() => {
      iframeInstance.handleEvent("ssoLogin", (res) => {
        iframeInstance.handleMessage({
          data: res,
          success: (ret) => {
            // 登录成功，返回回调地址
            window.open(ret.url, "_self");
          },
          fail: (err) => {
            // 登录失败，返回错误信息
            console.error(err);
          },
        });
      });
    });
  });

  /**
   * 提交登录表单
   * 1. 验证表单数据
   * 2. 加密密码
   * 3. 调用登录接口
   * 4. 处理登录结果
   */
  async function submitForm(): Promise<void> {
    try {
      loginLoading.value = true;
      await validate();
      const params: LoginParam = { ...formModel.value };
      // params.password = encryptPassword(aes_secret.value, aes_secret.value, params.password as string);

      const res = await userStore.login(params) as ResponseData<UserResponse>;
      const { user, done_token } = res.data || {};

      if (res.code !== 0 || !user) {
        localStorage.setItem("login_message", res.message);
        message.warning(res.message);
        return;
      }
      if (user.state === 0) {
        userStore.setToken(done_token);
        userStore.setUserInfo(user);
        userStore.setUserName(user.username);
        await initUserInfo();
        // 根据租户数量决定跳转路径
        await handleTenantSelection();
        message.success("登录成功");
      } else {
        // 跳转到修改密码
        router.push("/reset-password");
        message.warning(res.message);
      }
    } catch (error) {
      console.error("登录失败:", error);
      message.error("登录失败，请重试");
    } finally {
      loginLoading.value = false;
    }
  }

  /**
   * 验证表单数据
   * @returns {boolean} 验证结果
   */
  async function validate(): Promise<void> {
    if (!ruleForm.value) {
      throw new Error("表单实例不存在");
    }
    try {
      await ruleForm?.value.validate();
    } catch (err) {
      console.error(err);
      throw new Error("表单验证失败");
    }
  }

  /**
   * AOneID登录
   * 跳转到AOneID登录页面
   */
  async function loginWithAone() {
    const res = await loginAone();
    window.location.href = res;
  }

  /**
   * 获取短信验证码
   */
  async function getMsgCode(): Promise<boolean> {
    try {
      if (!formModel.value.email) {
        message.error("请输入用户名");
        return false;
      }

      codeLoading.value = true;
      await getCode({ username: formModel.value.email });
      message.success("验证码发送成功");

      countdown.value = 60;
      if (countdownInterval.value) {
        clearInterval(countdownInterval.value);
      }

      countdownInterval.value = window.setInterval(() => {
        countdown.value--;
        if (countdown.value === 0) {
          clearInterval(countdownInterval.value!);
          countdownInterval.value = null;
        }
      }, 1000);

      return true;
    } catch (error) {
      console.error("获取验证码失败:", error);
      message.error("获取验证码失败，请重试");
      return false;
    } finally {
      codeLoading.value = false;
    }
  }

  /**
   * 忘记密码
   */
  function findPassword(): void {
    router.push({
      name: "FindPassword",
    });
  }

  /**
   * 切换密码显示/隐藏状态
   */
  function countLogin(): void {
    beSelected.value = false;
    isCount.value = true;
  }
</script>

<style scoped>
/deep/ .old-login-tabs .ca-tabs-tab.ca-tabs-tab--active {
  color: #1388fb !important;
}

/deep/ .loginForm .ca-form-item--medium-size .ca-input {
  padding: calc(2.8vh - 16px) 0vh;
  font-size: 1.85vh;
}
/deep/ .loginForm .ca-form-item--medium-size .ca-form-item-feedback {
  font-size: 1.85vh;
}
/deep/ .loginForm .ca-form-item--medium-size:not(:first-child) {
  margin-top: calc(5vh - 24px);
}
@media screen and (max-height: 450px) {
  .header__logo,
  .footer {
    display: none !important;
  }
}
</style>
