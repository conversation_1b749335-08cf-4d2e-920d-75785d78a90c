import type { Role, RoleExpanded, RoleParam } from "-/role";
import type { MessageMode } from "@celeris/request";
import { request } from "@celeris/request";
import { replaceUrlPathParams } from "@celeris/utils";

enum API {
  Roles = "/v1/projects/{domainId}/roles",
  DeleteRole = "/v1/projects/{domainId}/roles/{roleId}",
}

/**
 * 获取角色列表
 * @param domainId - 项目/领域ID
 * @param params - 查询参数
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function getRoleList(domainId: string, params: RoleParam = {}, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.Roles, { domainId });
  return request.get({ url, params }, { errorMessageMode, shouldTransformResponse: false });
}

/**
 * 新增角色
 * @param domainId - 项目/领域ID
 * @param data - 角色数据
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function createRole(domainId: string, data: Role, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.Roles, { domainId });
  return request.post({ url, data }, { errorMessageMode });
}

/**
 * 更新角色
 * @param domainId - 项目/领域ID
 * @param data - 角色数据
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function updateRole(domainId: string, data: RoleExpanded, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.Roles, { domainId });
  return request.put({ url, data }, { errorMessageMode });
}

/**
 * 删除角色
 * @param domainId - 项目/领域ID
 * @param roleId - 角色ID
 * @param errorMessageMode - 错误提示模式
 * @returns Promise<any>
 */
export function deleteRole(domainId: string, roleId: string, errorMessageMode: MessageMode = "message") {
  const url = replaceUrlPathParams(API.DeleteRole, { domainId, roleId });
  return request.delete({ url }, { errorMessageMode });
}

export { API };
